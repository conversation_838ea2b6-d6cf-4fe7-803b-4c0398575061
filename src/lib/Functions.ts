import type { User } from '$lib/User';
import { APP_NAME, DIR_NAME, LABEL_PRINTER_ID_LIST } from '$stores/constant';
import { goto } from '$app/navigation';
import { ask, message } from '@tauri-apps/plugin-dialog';
import { BaseDirectory, exists, remove, writeFile } from '@tauri-apps/plugin-fs';
import { downloadDir } from '@tauri-apps/api/path';
import { openPath } from '@tauri-apps/plugin-opener';
import { printers } from 'tauri-plugin-printer-api';
import { toast } from 'svoast';
import { authClient } from '$lib/services/AxiosBackend';
import Canvas from 'canvas';
import JsBarcode from 'jsbarcode';

/**
 * Tauri의 dialog API중 message를 짧게 사용하기 위해 사용.
 *
 * @param msg
 * @param kind
 */
export async function executeMessage(
	msg: string,
	kind: 'info' | 'warning' | 'error' | undefined = 'info'
) {
	await message(msg, {
		title: APP_NAME,
		kind: kind
	});
}

export async function executeAsk(
	msg: string,
	kind: 'info' | 'warning' | 'error' | undefined = 'info'
) {
	return await ask(msg, {
		title: APP_NAME,
		kind: kind,
		okLabel: '예',
		cancelLabel: '아니오'
	});
}

/**
 * 관리자인지 체크 : 관리자가 아니면 지정된 주소로 보내기
 * @param user
 * @param gotoUrl
 */
export async function checkAdminAndRedirect(user: User, gotoUrl: string) {
	const adminRoles = ['Super-Admin', 'Admin'];

	if (!user || !adminRoles.includes(user.role)) {
		await executeMessage('관리자만 접근할 수 있는 페이지 입니다.', 'error');
		await goto(gotoUrl);
	}
}

const regionMap: Record<string, { locale: string; timezone: string }> = {
	ko: { locale: 'ko-KR', timezone: 'Asia/Seoul' },
	tw: { locale: 'zh-TW', timezone: 'Asia/Taipei' }
};

/**
 * 지역에 따른 날자 변경
 *
 * @param dt
 * @param region
 * @param format
 */
export function dateFormat(
	dt?: string,
	region: string = 'ko', // 기본값: 한국
	format: 'date' | 'datetime' | 'full' = 'full'
) {
	const { locale, timezone } = regionMap[region] || { locale: 'ko-KR', timezone: 'Asia/Seoul' };

	// dt 값이 없을 경우 현재 날짜로 설정
	const date = dt
		? /Z$|[+-]\d{2}:\d{2}$/.test(dt)
			? new Date(dt)       // 이미 시간대 정보가 있는 경우
			: new Date(`${dt}Z`) // 시간대 정보가 없는 경우 Z 추가
		: new Date();

	let options: Intl.DateTimeFormatOptions;

	if (format === 'date') {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric' };
	} else if (format === 'datetime') {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: false };
	} else {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false };
	}

	return new Intl.DateTimeFormat(locale, options).format(date);
}

/**
 * Date 객체를 문자열(YYYY-MM-DD)로 변환하는 함수
 *
 * @param date
 */
export function formatDateToString(date: Date) {
	return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

/**
 * Date 객체를 문자열(YYYY-MM-DD)로 변환하는 함수
 *
 * @param date
 */
export function formatDateTimeToString(date: string) {
	if (!date) return '';

	const datetime = new Date(date);

	const YYYY = datetime.getFullYear();
	const MM = ('0' + (datetime.getMonth() + 1)).slice(-2);
	const DD = ('0' + datetime.getDate()).slice(-2);

	return `${YYYY}-${MM}-${DD}`;
}

/**
 * Date 객체를 문자열(YYYY-MM-DD HH:mm)로 변환하는 함수
 *
 * @param date
 */
export function formatDateTimeToFullString(date: string) {
	if (!date) return '';

	const datetime = new Date(date);

	const YYYY = datetime.getFullYear();
	const MM = ('0' + (datetime.getMonth() + 1)).slice(-2);
	const DD = ('0' + datetime.getDate()).slice(-2);
	const hh = ('0' + datetime.getHours()).slice(-2);
	const mm = ('0' + datetime.getMinutes()).slice(-2);

	return `${YYYY}-${MM}-${DD} ${hh}:${mm}`;
}

/**
 * 숫자에 , 를 넣는다.
 *
 * @param number
 * @param region
 */
export function getNumberFormat(number: number, region: string = 'ko') {
	let locale = 'ko-KR';
	if (region === 'tw') {
		locale = 'zh-TW';
	}

	return new Intl.NumberFormat(locale).format(Number(number));
}

/**
 * 시작 날짜가 없을 경우 3개월 전 1일자 날짜를 가져온다.
 */
export function getBeginAt(month: number = 3) {
	const date = new Date();
	date.setMonth(date.getMonth() - month);
	date.setDate(1);

	return formatDateToString(date);
}

/**
 * id에 해당하는 곳으로 페이지를 이동시킨다.
 *
 * @param id
 */
export function scrollToElement(id: string) {
	const elem = document.getElementById(id);
	if (elem) {
		elem.scrollIntoView();

		// 약간의 지연 후 추가 오프셋 적용
		setTimeout(() => {
			window.scrollBy({
				top: 60,
				behavior: 'smooth'
			});
		}, 100);
	} else {
		console.log(`[${id}]엘리먼트를 찾을 수 없습니다.`);
	}
}

/**
 * 클립보드로 복사
 *
 * @param text
 */
export async function copyToClipboard(text: string) {
	try {
		await window.navigator.clipboard.writeText(text);
		toast.success('복사되었습니다.');
	} catch (error) {
		toast.error(`복사 실패: ${error}`);
	}
}

/**
 * 팔레트 번호 가져오기 (전체 버전)
 */
export function makeLocationInfo(loc: any) {
	const location = {
		country: 'KR',
		city: 'CJ',
		store: '',
		line: '',
		rack: '',
		level: '',
		column: '',
		location_name: '',
		pallet_code: ''
	};

	const locationCode = loc.code;
	const locationPlace = loc.place;
	const locationName = loc.name;

	const locationCdArr = locationCode.split('-');
	const locationPlaceArr = locationPlace.split('-');

	if (locationPlaceArr.length === 2) {
		location.country = locationPlaceArr[0];
		location.city = locationPlaceArr[1];
	}

	if (locationCdArr.length === 1) {
		location.store = locationCdArr[0];
		location.line = '';
		location.rack = '';
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 2) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = '';
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 3) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 4) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = locationCdArr[3];
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 5) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = locationCdArr[3];
		location.column = locationCdArr[4];
	} else if (locationCdArr.length >= 7) {
		location.country = locationCdArr[0];
		location.city = locationCdArr[1];
		location.store = locationCdArr[2];
		location.line = locationCdArr[3];
		location.rack = locationCdArr[4];
		location.level = locationCdArr[5];
		location.column = locationCdArr[6];
	}

	location.pallet_code = location.level + '-' + location.column;

	return location;
}

export function generateRandomNumber() {
	return Math.floor(1000000000 + Math.random() * 9000000000);
}

/**
 * 엑셀로 다운로드
 */
export async function handleDownload(url: string, payload: object) {
	try {
		const response = await authClient.post(url, payload, {
			responseType: 'arraybuffer',
			headers: {
				Accept:
					'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			}
		});

		const now = new Date();
		const year = now.getFullYear();
		const month = String(now.getMonth() + 1).padStart(2, '0');
		const day = String(now.getDate()).padStart(2, '0');
		const hours = String(now.getHours()).padStart(2, '0');
		const minutes = String(now.getMinutes()).padStart(2, '0');
		const timestamp = `${year}${month}${day}_${hours}${minutes}`;

		// 기본 파일명
		let filename = `excel_${timestamp}.xlsx`;

		const disposition = response.headers['content-disposition'];
		if (disposition) {
			// 1. filename* (UTF-8)를 먼저 찾습니다. (최신 브라우저 표준)
			const filenameStarMatch = /filename\*=UTF-8''([^;]+)/i.exec(disposition);
			if (filenameStarMatch && filenameStarMatch[1]) {
				filename = decodeURIComponent(filenameStarMatch[1]);
			}
			// 2. filename*이 없으면, 구형 filename="..."을 찾습니다.
			else {
				const filenameMatch = /filename="([^"]+)"/i.exec(disposition);
				if (filenameMatch && filenameMatch[1]) {
					filename = filenameMatch[1];
				}
			}
		}

		await writeFile(filename, response.data, { baseDir: BaseDirectory.Download });
		const result = await ask(
			`파일이 다운로드 폴더에 저장되었습니다.\n\n[ ${filename} ]\n\n지금 파일을 여시겠습니까?`,
			{
				title: '다운로드 완료',
				kind: 'info',
				okLabel: '열기',
				cancelLabel: '닫기'
			}
		);

		if (result) {
			const filePath = `${await downloadDir()}\\${filename}`;
			await openPath(filePath);
		}
	} catch (error) {
		await executeMessage(
			'다운로드 에러 (다시 시도해 보세요)\n만약 그래도 안 되면 프로그래머에게 문의 해 주세요.\n\n' + error,
			'error'
		);
	}
}

export function getPayload(data: string): { [key: string]: string | number[] } {
	return data.split('&').reduce((result: { [key: string]: string }, item) => {
		const parts = item.split('=');
		result[parts[0]] = parts[1];

		return result;
	}, {});
}

/**
 * Generates a barcode image from the given barcode string using the specified font.
 *
 * @param {string} barcode - The barcode string.
 * @param options
 * @returns {string} - The data URL of the generated barcode image in PNG format.
 */
export const makeBarcodeImage = async (
	barcode: string,
	options: object | null = null
): Promise<string> => {
	const canvasElement: Canvas.Canvas = Canvas.createCanvas(399, 120);

	const defaultOptions = {
		format: 'CODE128',
		width: 2,
		height: 65,
		displayValue: false,
		text: undefined,
		fontOptions: 'bold',
		font: 'monospace',
		textAlign: 'center',
		textPosition: 'bottom',
		textMargin: 2,
		fontSize: 15,
		background: '#ffffff',
		lineColor: '#000000',
		margin: 2,
		flat: false
	};

	JsBarcode(canvasElement, barcode, Object.assign(defaultOptions, options));

	return canvasElement.toDataURL('image/png');
};

export const deleteFile = async (barcode: string) => {
	const filePath = `${DIR_NAME}/${barcode}.pdf`;

	return await exists(filePath, { baseDir: BaseDirectory.Document })
		.then(async () => {
			await remove(filePath, { baseDir: BaseDirectory.Document });
			return true;
		})
		.catch(() => {
			return false;
		});
};

export function setDefaultPrinter(print: object) {
	window.localStorage.setItem('defaultPrint', JSON.stringify(print));
}

export function getDefaultPrinter() {
	const printer: string | null = window.localStorage.getItem('defaultPrint');

	return printer ? JSON.parse(printer) : null;
}

export async function getLocalLabelPrintList() {
	// 기본으로 설정된 프린트
	const defaultPrint = getDefaultPrinter();

	// 현재 컴퓨터에 연결된 프린트 리스트
	const printerList = await printers();

	// 사용 가능한 라벨 프린터 리스트
	let myPrinters;
	if (printerList && printerList.length > 0) {
		myPrinters = printerList.filter((item) =>
			LABEL_PRINTER_ID_LIST.some((id) => item.id.includes(id))
		);

		// 설정된 기본 프린트가 없으면 첫 번째 프린트를 기본 프린트로 설정
		if (!defaultPrint && myPrinters.length > 0) {
			setDefaultPrinter(myPrinters[0]);
		}

		return myPrinters;
	}
}

/**
 * Aging 상품 표시 여부 확인 (RG: 30일, 일반: 60일)
 * @param reqDateStr - 요청 날짜 문자열
 * @param isRg - RG 상품 여부 (기본값: 'N')
 * @returns 기한 초과 여부
 */
export function isOverDueDate(reqDateStr: string, isRg: string = 'N') {
	const reqDate: Date = new Date(reqDateStr);
	const currentDate: Date = new Date();

	const diffTime = Math.abs(currentDate.getTime() - reqDate.getTime());
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

	const dueDateLimit = isRg === 'Y' ? 30 : 60;

	return diffDays > dueDateLimit;
}

/**
 * 에러를 처리하는 공용 함수
 * @param {unknown} e - Axios 에러 객체
 * @param {boolean} returnMessageOnly - true일 경우 메시지만 반환하고 경고창을 표시하지 않음
 * @returns {Promise<string|void>} - returnMessageOnly가 true일 경우 에러 메시지 반환, 아닐 경우 void
 */
export async function handleCatch(e: unknown, returnMessageOnly: boolean = false): Promise<string | void> {
	console.error('서버 에러: ', e);

	// 오류 메시지 생성 함수
	const getMessage = (): string => {
		// axios 에러 타입 체크
		if (e && typeof e === 'object' && 'response' in e) {
			const { status, data } = e.response as { status: number; data: any };

			// 기본 메시지 설정
			let message = status >= 500
				? '서버 오류가 발생했습니다. 잠시 후 다시 시도해 주세요.'
				: '잘못된 요청입니다. 다시 확인해 주세요.';

			// 서버에서 제공한 메시지가 있으면 사용
			if (data?.message) {
				message = data.message;

				// 추가 오류 메시지가 있으면 추가
				if (data.error_message) {
					message += '\n' + data.error_message;
				}

				// 상세 오류 정보가 있으면 추가
				if (data.errors && typeof data.errors === 'object') {
					const errorFields = Object.keys(data.errors);
					if (errorFields.length > 0) {
						message += '\n\n문제가 발생한 항목: ' + errorFields.join(', ');
					}
				}
			}

			return message;
		} else {
			// 기본 오류 메시지
			return '알 수 없는 오류가 발생했습니다. 관리자에게 문의해 주세요.';
		}
	};

	// 메시지 생성
	const errorMessage = getMessage();

	// 반환 방식에 따라 처리
	if (returnMessageOnly) {
		return errorMessage;
	} else {
		await executeMessage(errorMessage, 'error');
	}
}

/**
 * 파일 업로드 처리 핸들러
 * 2MB 이상 파일은 업로드 제한
 */
export async function checkFileSize(event: Event) {
	const input = event.target as HTMLInputElement;
	const file = input.files?.[0];

	if (file) {
		// 파일 크기 확인 (바이트 단위, 2MB = 2 * 1024 * 1024 바이트)
		const maxSize = 2 * 1024 * 1024; // 2MB

		// 파일 크기가 2MB를 초과하는 경우
		if (file.size > maxSize) {
			await executeMessage("파일 크기는 2MB를 초과할 수 없습니다.");
			input.value = '';

			return;
		}
	}
}

export function addMemo(code: string, memo: string) {
	let message = '';
	if (code === 'repaired') {
		message = '수리 완료';
	}

	memo = memo.trim();

	if (memo !== '') memo += '\n';
	memo += `${message}`;

	return memo;
}

export const displayRepairUsername = (item: any) => {
	let username;

	if (item.repair_product?.completed_user) {
		username = item.repair_product.completed_user.username;
	} else if (item.repair_product?.waiting_user) {
		username = item.repair_product.waiting_user.username;
	} else if (item.pallet_products.length > 0) {
		username = item.pallet_products[0].registered_user.name;
	} else {
		username = '-';
	}

	return username;
};

export const displayRepairTime = (item: any) => {
	let time = null;

	if (item.repair_product?.completed_at) {
		time = item.repair_product.completed_at;
	} else if (item.repair_product?.waiting_at) {
		time = item.repair_product.waiting_at;
	} else if (item.pallet_products.length > 0) {
		time = item.pallet_products[0].registered_at;
	}

	if (time === null) return '-';
	else return formatDateTimeToFullString(time);
};

/**
 * 한글 입력 방지 처리 함수(한글이 입력되면 한글을 제거한다)
 * @param checkingBarcode - 현재 바코드 입력값
 * @param updateFunction - 바코드 업데이트 함수
 */
export function preventKoreanInput(
	checkingBarcode: string,
	updateFunction: (value: string) => void
): void {
	const koreanRegex = /[ㄱ-ㅎㅏ-ㅣ가-힣]/g;
	if (koreanRegex.test(checkingBarcode)) {
		const value = checkingBarcode.replace(koreanRegex, "");
		requestAnimationFrame(() => {
			updateFunction(value);
		});
	}
}

/**
 * 바코드 문자열을 처리하는 함수
 * - 숫자로만 구성된 경우 앞에 'Q'를 추가
 * - 그 외의 경우 첫 번째 문자를 'Q'로 변경
 * - 모든 문자를 대문자로 변환
 *
 * @param barcode 처리할 바코드 문자열
 * @returns 처리된 바코드 문자열
 */
export function processBarcode(barcode: string): string {
	if (!barcode) return '';

	let processedBarcode = barcode.trim();

	// 숫자로만 구성된 경우 앞에 'Q' 추가
	if (/^\d+$/.test(processedBarcode)) {
		processedBarcode = 'Q' + processedBarcode;
	}
	// 숫자가 아닌 경우 첫 글자를 'Q'로 변경
	else {
		processedBarcode = 'Q' + processedBarcode.slice(1);
	}

	// 모든 문자를 대문자로 변환
	return processedBarcode.toUpperCase();
}

/**
 * 반품 사유
 */
export const formattedReturnReason = (reason: string, type: string = 'tooltip') => {
	if (!reason) return '';

	if (type === 'tooltip') {
		return reason.replace(/\[EOL]/g, " ");
	} else if (type === 'display') {
		return reason.replace(/\[EOL]/g, "<br>");
	} else {
		return reason;
	}
};