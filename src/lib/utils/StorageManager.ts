/**
 * 로컬 스토리지에 값을 저장하는 함수
 * @param key 저장할 키 이름
 * @param value 저장할 값
 * @param updateState 상태 업데이트 콜백 함수 (옵션)
 */
export function saveToLocalStorage(
	key: string,
	value: string,
	updateState?: (value: string) => void
): void {
	if (value && value.trim() !== '') {
		window.localStorage.setItem(key, value);
		if (updateState) {
			updateState(value);
		}
	}
}

/**
 * 로컬 스토리지에서 값을 가져오는 함수
 * @param key 가져올 키 이름
 * @param defaultValue 기본값 (옵션)
 * @returns 저장된 값 또는 기본값
 */
export function getFromLocalStorage(key: string, defaultValue: string = ''): string {
	return window.localStorage.getItem(key) ?? defaultValue;
}

/**
 * 로컬 스토리지에서 값을 삭제하는 함수
 * @param key 삭제할 키 이름
 * @param updateState 상태 업데이트 콜백 함수 (옵션)
 */
export function removeFromLocalStorage(key: string, updateState?: (value: string) => void): void {
	window.localStorage.removeItem(key);
	if (updateState) {
		updateState('');
	}
}

/**
 * JSON 객체를 로컬스토리지에 저장하는 함수
 * @param key 저장할 키 이름
 * @param value 저장할 객체
 */
export function saveObjectToLocalStorage(key: string, value: any): void {
	try {
		window.localStorage.setItem(key, JSON.stringify(value));
	} catch (error) {
		console.error(`로컬스토리지 저장 실패 (${key}):`, error);
		throw new Error('로컬스토리지 저장에 실패했습니다.');
	}
}

/**
 * 로컬스토리지에서 JSON 객체를 가져오는 함수
 * @param key 가져올 키 이름
 * @param defaultValue 기본값 (옵션)
 * @returns 파싱된 객체 또는 기본값
 */
export function getObjectFromLocalStorage<T>(key: string, defaultValue: T): T {
	try {
		const stored = window.localStorage.getItem(key);
		if (!stored) {
			return defaultValue;
		}
		return JSON.parse(stored) as T;
	} catch (error) {
		console.error(`로컬스토리지 로드 실패 (${key}):`, error);
		return defaultValue;
	}
}

/**
 * 스토리지 용량 정보 확인
 * @returns 스토리지 용량 정보
 */
export function getStorageCapacityInfo() {
	try {
		// 대략적인 스토리지 사용량 계산
		let totalSize = 0;
		for (let key in window.localStorage) {
			if (window.localStorage.hasOwnProperty(key)) {
				totalSize += window.localStorage[key].length;
			}
		}

		// 브라우저별 대략적인 로컬스토리지 제한 (5MB)
		const estimatedLimit = 5 * 1024 * 1024; // 5MB in bytes
		const used = totalSize;
		const available = estimatedLimit - used;
		const percentage = used / estimatedLimit;

		return {
			used,
			available,
			percentage,
			isNearLimit: percentage > 0.8 // 80% 사용 시 경고
		};
	} catch (error) {
		console.error('스토리지 용량 확인 실패:', error);
		return {
			used: 0,
			available: 0,
			percentage: 0,
			isNearLimit: false
		};
	}
}
