import { memoize, createPerformanceCache } from './performance';

// 개선된 간소화된 페이지네이션 인터페이스
export interface OptimizedPageData {
	total: number; // 전체 데이터 수
	current_page: number; // 현재 페이지
	last_page: number; // 마지막 페이지
	per_page: number; // 페이지당 데이터 수
	from: number; // 현재 페이지 시작 번호
	to: number; // 현재 페이지 끝 번호
	has_prev: boolean; // 이전 페이지 존재 여부
	has_next: boolean; // 다음 페이지 존재 여부
}

// 페이지네이션 성능 최적화를 위한 캐시
const paginationCache = createPerformanceCache<string, any>(50, 2 * 60 * 1000); // 2분 TTL

// 개선된 페이지네이션 처리 함수
export function processPageData(pageData: OptimizedPageData) {
	const pageCurrentPage = pageData.current_page;
	const pageTotal = pageData.total;
	const pageFrom = pageData.from;
	const pageTo = pageData.to;
	const pageLastPage = pageData.last_page;
	const pagePerPage = pageData.per_page;
	const pageStartNo = pageTotal - pageFrom + 1;
	const hasNext = pageData.has_next;
	const hasPrev = pageData.has_prev;

	// 클라이언트에서 페이지 링크 생성
	const pageLinks = generatePageLinks(pageCurrentPage, pageLastPage);

	return {
		pageTotal,
		pageCurrentPage,
		pageFrom,
		pageTo,
		pageLastPage,
		pagePerPage,
		pageStartNo,
		hasNext,
		hasPrev,
		pageLinks
	};
}

// 메모이제이션된 페이지 링크 생성 함수 (성능 최적화)
export const generatePageLinks = memoize(
	(currentPage: number, lastPage: number, maxLinks: number = 10) => {
		const links = [];

		// 표시할 페이지 범위 계산
		const halfRange = Math.floor(maxLinks / 2);
		let startPage = Math.max(1, currentPage - halfRange);
		let endPage = Math.min(lastPage, startPage + maxLinks - 1);

		// 끝 페이지 기준으로 시작 페이지 재조정
		if (endPage - startPage + 1 < maxLinks) {
			startPage = Math.max(1, endPage - maxLinks + 1);
		}

		// 첫 페이지 추가 (1이 범위에 없을 경우)
		if (startPage > 1) {
			links.push({ label: '1', active: false, page: 1 });
			if (startPage > 2) {
				links.push({ label: '...', active: false, page: null });
			}
		}

		// 페이지 범위 추가
		for (let i = startPage; i <= endPage; i++) {
			links.push({
				label: i.toString(),
				active: i === currentPage,
				page: i
			});
		}

		// 마지막 페이지 추가 (lastPage가 범위에 없을 경우)
		if (endPage < lastPage) {
			if (endPage < lastPage - 1) {
				links.push({ label: '...', active: false, page: null });
			}
			links.push({ label: lastPage.toString(), active: false, page: lastPage });
		}

		return links;
	},
	(currentPage: number, lastPage: number, maxLinks: number = 10) =>
		`${currentPage}-${lastPage}-${maxLinks}`
);

// 메모이제이션된 페이지네이션 정보 요약 함수 (성능 최적화)
export const getPaginationSummary = memoize(
	(pageData: OptimizedPageData) => {
		const { total, current_page, per_page, from, to } = pageData;

		return {
			showing: `${from}~${to}`,
			total: total,
			page: `${current_page}/${Math.ceil(total / per_page)}`,
			hasData: total > 0
		};
	},
	(pageData: OptimizedPageData) =>
		`${pageData.total}-${pageData.current_page}-${pageData.per_page}-${pageData.from}-${pageData.to}`
);

/**
 * 대용량 데이터 처리를 위한 최적화된 페이지네이션 계산
 * @param totalItems 전체 아이템 수
 * @param currentPage 현재 페이지
 * @param pageSize 페이지 크기
 * @returns 최적화된 페이지네이션 데이터
 */
export function calculateOptimizedPagination(
	totalItems: number,
	currentPage: number,
	pageSize: number
): OptimizedPageData {
	const lastPage = Math.ceil(totalItems / pageSize);
	const from = (currentPage - 1) * pageSize + 1;
	const to = Math.min(currentPage * pageSize, totalItems);

	return {
		total: totalItems,
		current_page: currentPage,
		last_page: lastPage,
		per_page: pageSize,
		from: Math.max(1, from),
		to: Math.max(0, to),
		has_prev: currentPage > 1,
		has_next: currentPage < lastPage
	};
}

/**
 * 페이지네이션 캐시 관리 함수들
 */
export const paginationCacheManager = {
	/**
	 * 캐시 정리 (만료된 항목 제거)
	 */
	cleanup: () => {
		paginationCache.cleanup();
	},

	/**
	 * 캐시 전체 삭제
	 */
	clear: () => {
		paginationCache.clear();
	},

	/**
	 * 캐시 크기 확인
	 */
	size: () => {
		return paginationCache.size();
	},

	/**
	 * 특정 키의 캐시 삭제
	 */
	delete: (key: string) => {
		return paginationCache.delete(key);
	}
};

/**
 * 스마트 페이지 크기 추천 함수
 * 화면 크기와 성능을 고려하여 최적의 페이지 크기를 추천
 * @param screenHeight 화면 높이
 * @param itemHeight 예상 아이템 높이
 * @param performanceLevel 성능 레벨 (1: 낮음, 2: 보통, 3: 높음)
 * @returns 추천 페이지 크기
 */
export function getRecommendedPageSize(
	screenHeight: number = window.innerHeight,
	itemHeight: number = 60,
	performanceLevel: number = 2
): number {
	const visibleItems = Math.floor(screenHeight / itemHeight);
	const baseMultiplier = performanceLevel === 1 ? 1.5 : performanceLevel === 2 ? 2 : 3;
	const recommendedSize = Math.ceil(visibleItems * baseMultiplier);

	// 최소 16개, 최대 320개로 제한
	return Math.max(16, Math.min(320, recommendedSize));
}

/**
 * 페이지네이션 성능 모니터링 인터페이스
 */
export interface PaginationPerformanceMonitorInstance {
	startMeasure: (key: string) => () => void;
	getMetrics: (key: string) => { renderTime: number; count: number } | undefined;
	getAllMetrics: () => Record<string, { renderTime: number; count: number }>;
	clear: () => void;
}

/**
 * 페이지네이션 성능 모니터링 생성 함수
 * @returns 성능 모니터링 인스턴스
 */
function createPaginationPerformanceMonitor(): PaginationPerformanceMonitorInstance {
	const metrics = new Map<string, { renderTime: number; count: number }>();

	return {
		startMeasure: (key: string): (() => void) => {
			const startTime = performance.now();

			return () => {
				const endTime = performance.now();
				const renderTime = endTime - startTime;

				const existing = metrics.get(key) || { renderTime: 0, count: 0 };
				metrics.set(key, {
					renderTime: (existing.renderTime * existing.count + renderTime) / (existing.count + 1),
					count: existing.count + 1
				});
			};
		},

		getMetrics: (key: string) => {
			return metrics.get(key);
		},

		getAllMetrics: () => {
			return Object.fromEntries(metrics);
		},

		clear: () => {
			metrics.clear();
		}
	};
}

/**
 * 전역 페이지네이션 성능 모니터 인스턴스
 */
export const PaginationPerformanceMonitor = createPaginationPerformanceMonitor();
