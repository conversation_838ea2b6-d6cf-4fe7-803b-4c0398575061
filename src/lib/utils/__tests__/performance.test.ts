import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	debounce,
	throttle,
	memoize,
	memoizeWithStats,
	memoizeWithTTL,
	createPerformanceCache,
	calculateVirtualScrollRange,
	processInChunks,
	calculateRenderWindow,
	createDebouncedSearch
} from '../performance';

describe('Performance Utilities', () => {
	beforeEach(() => {
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('debounce', () => {
		it('should delay function execution', () => {
			const mockFn = vi.fn();
			const debouncedFn = debounce(mockFn, 100);

			debouncedFn('test');
			expect(mockFn).not.toHaveBeenCalled();

			vi.advanceTimersByTime(100);
			expect(mockFn).toHaveBeenCalledWith('test');
		});

		it('should cancel previous calls', () => {
			const mockFn = vi.fn();
			const debouncedFn = debounce(mockFn, 100);

			debouncedFn('first');
			debouncedFn('second');

			vi.advanceTimersByTime(100);
			expect(mockFn).toHaveBeenCalledTimes(1);
			expect(mockFn).toHaveBeenCalledWith('second');
		});
	});

	describe('throttle', () => {
		it('should limit function calls', () => {
			const mockFn = vi.fn();
			const throttledFn = throttle(mockFn, 100);

			throttledFn('first');
			throttledFn('second');
			throttledFn('third');

			expect(mockFn).toHaveBeenCalledTimes(1);
			expect(mockFn).toHaveBeenCalledWith('first');
		});

		it('should allow calls after limit period', () => {
			const mockFn = vi.fn();
			const throttledFn = throttle(mockFn, 100);

			throttledFn('first');
			vi.advanceTimersByTime(100);
			throttledFn('second');

			expect(mockFn).toHaveBeenCalledTimes(2);
		});
	});

	describe('memoize', () => {
		it('should cache function results', () => {
			const mockFn = vi.fn((x: number) => x * 2);
			const memoizedFn = memoize(mockFn);

			const result1 = memoizedFn(5);
			const result2 = memoizedFn(5);

			expect(result1).toBe(10);
			expect(result2).toBe(10);
			expect(mockFn).toHaveBeenCalledTimes(1);
		});

		it('should use custom key generator (legacy syntax)', () => {
			const mockFn = vi.fn((obj: { id: number; name: string }) => obj.name.toUpperCase());
			const memoizedFn = memoize(mockFn, (obj) => obj.id.toString());

			const obj1 = { id: 1, name: 'test' };
			const obj2 = { id: 1, name: 'different' }; // Same ID, different name

			const result1 = memoizedFn(obj1);
			const result2 = memoizedFn(obj2);

			expect(result1).toBe('TEST');
			expect(result2).toBe('TEST'); // Should return cached result
			expect(mockFn).toHaveBeenCalledTimes(1);
		});

		it('should use custom key generator (new syntax)', () => {
			const mockFn = vi.fn((obj: { id: number; name: string }) => obj.name.toUpperCase());
			const memoizedFn = memoize(mockFn, 100, (obj) => obj.id.toString());

			const obj1 = { id: 1, name: 'test' };
			const obj2 = { id: 1, name: 'different' }; // Same ID, different name

			const result1 = memoizedFn(obj1);
			const result2 = memoizedFn(obj2);

			expect(result1).toBe('TEST');
			expect(result2).toBe('TEST'); // Should return cached result
			expect(mockFn).toHaveBeenCalledTimes(1);
		});

		it('should respect cache size limit (LRU)', () => {
			const mockFn = vi.fn((x: number) => x * 2);
			const memoizedFn = memoize(mockFn, 2); // 최대 2개만 캐시

			// 첫 번째와 두 번째 호출
			memoizedFn(1); // 캐시: [1]
			memoizedFn(2); // 캐시: [1, 2]

			expect(mockFn).toHaveBeenCalledTimes(2);

			// 세 번째 호출 (캐시 크기 초과, 가장 오래된 항목 제거)
			memoizedFn(3); // 캐시: [2, 3] (1 제거됨)

			expect(mockFn).toHaveBeenCalledTimes(3);

			// 1을 다시 호출하면 캐시에 없으므로 함수 재실행
			memoizedFn(1); // 캐시: [3, 1] (2 제거됨)

			expect(mockFn).toHaveBeenCalledTimes(4);

			// 3을 다시 호출하면 캐시에 있으므로 함수 실행 안됨
			memoizedFn(3); // 캐시: [1, 3] (LRU로 3이 맨 뒤로)

			expect(mockFn).toHaveBeenCalledTimes(5); // 3이 제거되었으므로 재실행
		});

		it('should implement LRU correctly', () => {
			const mockFn = vi.fn((x: number) => x);
			const memoizedFn = memoize(mockFn, 2);

			// 캐시 채우기
			memoizedFn(1); // 캐시: [1]
			memoizedFn(2); // 캐시: [1, 2]

			// 1에 접근하여 최근 사용으로 만들기
			memoizedFn(1); // 캐시: [2, 1] (1이 맨 뒤로)

			// 새 항목 추가 (2가 제거되어야 함)
			memoizedFn(3); // 캐시: [1, 3] (2 제거됨)

			expect(mockFn).toHaveBeenCalledTimes(3);

			// 1은 여전히 캐시에 있어야 함
			memoizedFn(1);
			expect(mockFn).toHaveBeenCalledTimes(3); // 재실행 안됨

			// 2는 캐시에서 제거되었으므로 재실행되어야 함
			memoizedFn(2);
			expect(mockFn).toHaveBeenCalledTimes(4); // 재실행됨
		});
	});

	describe('memoizeWithStats', () => {
		it('should track cache statistics', () => {
			const mockFn = vi.fn((x: number) => x * 2);
			const { memoizedFunction, getStats } = memoizeWithStats(mockFn, 3);

			// 초기 상태
			let stats = getStats();
			expect(stats.size).toBe(0);
			expect(stats.totalRequests).toBe(0);
			expect(stats.hitRate).toBe(0);

			// 첫 번째 호출 (캐시 미스)
			memoizedFunction(1);
			stats = getStats();
			expect(stats.size).toBe(1);
			expect(stats.totalRequests).toBe(1);
			expect(stats.cacheHits).toBe(0);
			expect(stats.cacheMisses).toBe(1);
			expect(stats.hitRate).toBe(0);

			// 두 번째 호출 (캐시 히트)
			memoizedFunction(1);
			stats = getStats();
			expect(stats.totalRequests).toBe(2);
			expect(stats.cacheHits).toBe(1);
			expect(stats.cacheMisses).toBe(1);
			expect(stats.hitRate).toBe(50);
		});

		it('should clear cache and stats', () => {
			const mockFn = vi.fn((x: number) => x);
			const { memoizedFunction, getStats, clearCache } = memoizeWithStats(mockFn);

			memoizedFunction(1);
			memoizedFunction(1);

			let stats = getStats();
			expect(stats.size).toBe(1);
			expect(stats.totalRequests).toBe(2);

			clearCache();

			stats = getStats();
			expect(stats.size).toBe(0);
			expect(stats.totalRequests).toBe(0);
			expect(stats.hitRate).toBe(0);
		});
	});

	describe('memoizeWithTTL', () => {
		it('should expire cached values after TTL', () => {
			const mockFn = vi.fn((x: number) => x * 2);
			const memoizedFn = memoizeWithTTL(mockFn, 100, 100); // 100ms TTL

			// 첫 번째 호출
			const result1 = memoizedFn(5);
			expect(result1).toBe(10);
			expect(mockFn).toHaveBeenCalledTimes(1);

			// TTL 내에서 두 번째 호출 (캐시 히트)
			const result2 = memoizedFn(5);
			expect(result2).toBe(10);
			expect(mockFn).toHaveBeenCalledTimes(1);

			// TTL 만료 후 호출
			vi.advanceTimersByTime(150);
			const result3 = memoizedFn(5);
			expect(result3).toBe(10);
			expect(mockFn).toHaveBeenCalledTimes(2); // 재실행됨
		});

		it('should clean up expired entries', () => {
			const mockFn = vi.fn((x: number) => x);
			const memoizedFn = memoizeWithTTL(mockFn, 10, 100);

			// 여러 값 캐시
			memoizedFn(1);
			memoizedFn(2);
			memoizedFn(3);

			expect(mockFn).toHaveBeenCalledTimes(3);

			// TTL 만료 후 새 값 추가 (만료된 항목들이 정리되어야 함)
			vi.advanceTimersByTime(150);
			memoizedFn(4);

			// 만료된 값들 재호출 시 재실행되어야 함
			memoizedFn(1);
			memoizedFn(2);
			memoizedFn(3);

			expect(mockFn).toHaveBeenCalledTimes(7); // 4 + 1,2,3 재실행
		});
	});

	describe('createPerformanceCache', () => {
		it('should store and retrieve values', () => {
			const cache = createPerformanceCache<string, number>(10, 1000);

			cache.set('key1', 100);
			expect(cache.get('key1')).toBe(100);
			expect(cache.has('key1')).toBe(true);
		});

		it('should respect TTL', () => {
			const cache = createPerformanceCache<string, number>(10, 100);

			cache.set('key1', 100);
			expect(cache.get('key1')).toBe(100);

			vi.advanceTimersByTime(150);
			expect(cache.get('key1')).toBe(null);
			expect(cache.has('key1')).toBe(false);
		});

		it('should respect max size', () => {
			const cache = createPerformanceCache<string, number>(2, 1000);

			cache.set('key1', 1);
			cache.set('key2', 2);
			cache.set('key3', 3); // Should evict key1

			expect(cache.get('key1')).toBe(null);
			expect(cache.get('key2')).toBe(2);
			expect(cache.get('key3')).toBe(3);
		});
	});

	describe('calculateVirtualScrollRange', () => {
		it('should calculate correct visible range', () => {
			const result = calculateVirtualScrollRange(1000, 50, 500, 250, 5);

			expect(result.startIndex).toBe(0); // Math.max(0, Math.floor(250/50) - 5)
			expect(result.endIndex).toBe(20); // Math.min(999, 0 + Math.ceil(500/50) + 10)
			expect(result.visibleItemCount).toBe(10);
			expect(result.offsetY).toBe(0);
			expect(result.totalHeight).toBe(50000);
		});
	});

	describe('processInChunks', () => {
		it('should process data in chunks', async () => {
			const data = Array.from({ length: 10 }, (_, i) => i);
			const processedChunks: number[][] = [];

			await processInChunks(data, 3, async (chunk) => {
				processedChunks.push([...chunk]);
			});

			expect(processedChunks).toEqual([[0, 1, 2], [3, 4, 5], [6, 7, 8], [9]]);
		});

		it('should respect delay between chunks', async () => {
			const data = [1, 2, 3, 4];
			const processedChunks: number[] = [];

			await processInChunks(
				data,
				2,
				async (chunk) => {
					processedChunks.push(...chunk);
				},
				0 // No delay for test performance
			);

			expect(processedChunks).toEqual([1, 2, 3, 4]);
		});
	});

	describe('calculateRenderWindow', () => {
		it('should calculate correct render window', () => {
			const result = calculateRenderWindow(100, 10, 50, 20);

			expect(result.start).toBe(40); // Math.max(0, 50 - 10)
			expect(result.end).toBe(59); // Math.min(99, 40 + 20 - 1)
			expect(result.size).toBe(20);
		});

		it('should adjust for boundaries', () => {
			const result = calculateRenderWindow(100, 10, 5, 20);

			expect(result.start).toBe(0); // Adjusted to start
			expect(result.end).toBe(19);
			expect(result.size).toBe(20);
		});
	});

	describe('createDebouncedSearch', () => {
		it('should debounce search calls', async () => {
			const mockSearch = vi.fn().mockResolvedValue('result');
			const debouncedSearch = createDebouncedSearch(mockSearch, 100);

			const promise1 = debouncedSearch.search('query1');
			const promise2 = debouncedSearch.search('query2');

			vi.advanceTimersByTime(100);

			await promise2;

			expect(mockSearch).toHaveBeenCalledTimes(1);
			expect(mockSearch).toHaveBeenCalledWith('query2');
		});

		it('should return cached promise for same query', async () => {
			const mockSearch = vi.fn().mockResolvedValue('result');
			const debouncedSearch = createDebouncedSearch(mockSearch, 100);

			const promise1 = debouncedSearch.search('same-query');
			const promise2 = debouncedSearch.search('same-query');

			expect(promise1).toBe(promise2);
		});
	});
});
