import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
	generatePageLinks,
	getPaginationSummary,
	calculateOptimizedPagination,
	paginationCacheManager,
	getRecommendedPageSize,
	PaginationPerformanceMonitor
} from '../pagination';
import type { OptimizedPageData } from '../pagination';

describe('Pagination Utilities', () => {
	beforeEach(() => {
		// Clear cache before each test
		paginationCacheManager.clear();
	});

	describe('generatePageLinks', () => {
		it('should generate correct page links for small range', () => {
			const links = generatePageLinks(3, 5, 10);

			expect(links).toEqual([
				{ label: '1', active: false, page: 1 },
				{ label: '2', active: false, page: 2 },
				{ label: '3', active: true, page: 3 },
				{ label: '4', active: false, page: 4 },
				{ label: '5', active: false, page: 5 }
			]);
		});

		it('should generate links with ellipsis for large range', () => {
			const links = generatePageLinks(10, 20, 5);

			expect(links).toEqual([
				{ label: '1', active: false, page: 1 },
				{ label: '...', active: false, page: null },
				{ label: '8', active: false, page: 8 },
				{ label: '9', active: false, page: 9 },
				{ label: '10', active: true, page: 10 },
				{ label: '11', active: false, page: 11 },
				{ label: '12', active: false, page: 12 },
				{ label: '...', active: false, page: null },
				{ label: '20', active: false, page: 20 }
			]);
		});

		it('should cache results for performance', () => {
			const spy = vi.spyOn(console, 'time').mockImplementation(() => {});

			// First call
			const links1 = generatePageLinks(5, 10, 5);
			// Second call with same parameters
			const links2 = generatePageLinks(5, 10, 5);

			expect(links1).toEqual(links2);
			spy.mockRestore();
		});
	});

	describe('getPaginationSummary', () => {
		it('should generate correct summary', () => {
			const pageData: OptimizedPageData = {
				total: 100,
				current_page: 3,
				last_page: 10,
				per_page: 10,
				from: 21,
				to: 30,
				has_prev: true,
				has_next: true
			};

			const summary = getPaginationSummary(pageData);

			expect(summary).toEqual({
				showing: '21~30',
				total: 100,
				page: '3/10',
				hasData: true
			});
		});

		it('should handle empty data', () => {
			const pageData: OptimizedPageData = {
				total: 0,
				current_page: 1,
				last_page: 1,
				per_page: 10,
				from: 0,
				to: 0,
				has_prev: false,
				has_next: false
			};

			const summary = getPaginationSummary(pageData);

			expect(summary.hasData).toBe(false);
			expect(summary.total).toBe(0);
		});
	});

	describe('calculateOptimizedPagination', () => {
		it('should calculate correct pagination data', () => {
			const result = calculateOptimizedPagination(100, 3, 10);

			expect(result).toEqual({
				total: 100,
				current_page: 3,
				last_page: 10,
				per_page: 10,
				from: 21,
				to: 30,
				has_prev: true,
				has_next: true
			});
		});

		it('should handle edge cases', () => {
			// First page
			const firstPage = calculateOptimizedPagination(100, 1, 10);
			expect(firstPage.has_prev).toBe(false);
			expect(firstPage.from).toBe(1);

			// Last page
			const lastPage = calculateOptimizedPagination(100, 10, 10);
			expect(lastPage.has_next).toBe(false);
			expect(lastPage.to).toBe(100);

			// Partial last page
			const partialLast = calculateOptimizedPagination(95, 10, 10);
			expect(partialLast.to).toBe(95);
		});
	});

	describe('paginationCacheManager', () => {
		it('should manage cache operations', () => {
			expect(paginationCacheManager.size()).toBe(0);

			// Generate some cached data
			generatePageLinks(1, 10, 5);
			generatePageLinks(2, 10, 5);

			expect(paginationCacheManager.size()).toBeGreaterThan(0);

			paginationCacheManager.clear();
			expect(paginationCacheManager.size()).toBe(0);
		});
	});

	describe('getRecommendedPageSize', () => {
		// Mock window object for testing
		const mockWindow = {
			innerHeight: 800
		};

		beforeEach(() => {
			Object.defineProperty(global, 'window', {
				value: mockWindow,
				writable: true
			});
		});

		it('should recommend appropriate page size based on screen height', () => {
			const pageSize = getRecommendedPageSize(800, 60, 2);

			// Expected: Math.ceil((800 / 60) * 2) = Math.ceil(13.33 * 2) = 27
			// But clamped between 16 and 320
			expect(pageSize).toBeGreaterThanOrEqual(16);
			expect(pageSize).toBeLessThanOrEqual(320);
		});

		it('should respect minimum and maximum limits', () => {
			const smallSize = getRecommendedPageSize(100, 100, 1);
			expect(smallSize).toBe(16); // Minimum

			const largeSize = getRecommendedPageSize(10000, 10, 3);
			expect(largeSize).toBe(320); // Maximum
		});
	});

	describe('PaginationPerformanceMonitor', () => {
		it('should track performance metrics', () => {
			const monitor = PaginationPerformanceMonitor;

			const endMeasure = monitor.startMeasure('test-operation');

			// Simulate some work
			const start = performance.now();
			while (performance.now() - start < 1) {
				// Busy wait for 1ms
			}

			endMeasure();

			const metrics = monitor.getMetrics('test-operation');
			expect(metrics).toBeDefined();
			expect(metrics?.count).toBe(1);
			expect(metrics?.renderTime).toBeGreaterThan(0);
		});

		it('should calculate average render time', () => {
			const monitor = PaginationPerformanceMonitor;
			monitor.clear();

			// Simulate multiple measurements
			const endMeasure1 = monitor.startMeasure('avg-test');
			endMeasure1();

			const endMeasure2 = monitor.startMeasure('avg-test');
			endMeasure2();

			const metrics = monitor.getMetrics('avg-test');
			expect(metrics?.count).toBe(2);
		});

		it('should be consistent instance', () => {
			const monitor1 = PaginationPerformanceMonitor;
			const monitor2 = PaginationPerformanceMonitor;

			expect(monitor1).toBe(monitor2);
		});
	});
});
