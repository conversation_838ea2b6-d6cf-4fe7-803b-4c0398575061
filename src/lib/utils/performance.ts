/**
 * 성능 최적화 유틸리티 함수들
 *
 * 이 모듈은 메모리 효율적인 캐싱과 성능 최적화를 위한 다양한 함수들을 제공합니다.
 * 모든 캐시 구현은 LRU(Least Recently Used) 알고리즘을 사용하여 메모리 누수를 방지합니다.
 *
 * 주요 기능:
 * - memoize: 기본 메모이제이션 (LRU 캐시 적용)
 * - memoizeWithStats: 통계 정보가 포함된 메모이제이션
 * - memoizeWithTTL: TTL(Time To Live) 기능이 포함된 메모이제이션
 * - createPerformanceCache: 범용 성능 캐시 생성
 * - createItemHeightCache: UI 아이템 높이 캐싱
 * - 기타 성능 최적화 유틸리티들
 */

/**
 * 디바운스 함수 - 연속된 함수 호출을 지연시켜 성능 최적화
 * @param func 실행할 함수
 * @param delay 지연 시간 (밀리초)
 * @returns 디바운스된 함수
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	delay: number
): (...args: Parameters<T>) => void {
	let timeoutId: ReturnType<typeof setTimeout>;

	return (...args: Parameters<T>) => {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => func(...args), delay);
	};
}

/**
 * 스로틀 함수 - 함수 호출 빈도를 제한하여 성능 최적화
 * @param func 실행할 함수
 * @param limit 제한 시간 (밀리초)
 * @returns 스로틀된 함수
 */
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number
): (...args: Parameters<T>) => void {
	let inThrottle: boolean;

	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			setTimeout(() => (inThrottle = false), limit);
		}
	};
}

/**
 * 메모이제이션 함수 오버로드 - 기존 호환성 유지
 */
export function memoize<T extends (...args: any[]) => any>(
	func: T,
	keyGenerator?: (...args: Parameters<T>) => string
): T;
export function memoize<T extends (...args: any[]) => any>(
	func: T,
	maxSize: number,
	keyGenerator?: (...args: Parameters<T>) => string
): T;

/**
 * 메모이제이션 함수 - 계산 결과를 캐시하여 성능 최적화 (LRU 캐시 적용)
 * @param func 메모이제이션할 함수
 * @param maxSizeOrKeyGenerator 최대 캐시 크기 또는 키 생성 함수 (기존 호환성)
 * @param keyGenerator 캐시 키 생성 함수 (선택사항)
 * @returns 메모이제이션된 함수
 */
export function memoize<T extends (...args: any[]) => any>(
	func: T,
	maxSizeOrKeyGenerator?: number | ((...args: Parameters<T>) => string),
	keyGenerator?: (...args: Parameters<T>) => string
): T {
	// 매개변수 타입에 따라 분기 처리
	let maxSize: number;
	let keyGen: ((...args: Parameters<T>) => string) | undefined;

	if (typeof maxSizeOrKeyGenerator === 'function') {
		// 기존 방식: memoize(func, keyGenerator)
		maxSize = 100; // 기본값
		keyGen = maxSizeOrKeyGenerator;
	} else if (typeof maxSizeOrKeyGenerator === 'number') {
		// 새로운 방식: memoize(func, maxSize, keyGenerator)
		maxSize = maxSizeOrKeyGenerator;
		keyGen = keyGenerator;
	} else {
		// memoize(func) - 모든 기본값 사용
		maxSize = 100;
		keyGen = undefined;
	}

	const cache = new Map<string, ReturnType<T>>();

	return ((...args: Parameters<T>) => {
		const key = keyGen ? keyGen(...args) : JSON.stringify(args);

		if (cache.has(key)) {
			// LRU: 최근 사용된 항목을 맨 뒤로 이동
			const value = cache.get(key)!;
			cache.delete(key);
			cache.set(key, value);
			return value;
		}

		// 캐시 크기 제한 (가장 오래된 항목 제거)
		if (cache.size >= maxSize) {
			const firstKey = cache.keys().next().value;
			if (firstKey !== undefined) {
				cache.delete(firstKey);
			}
		}

		const result = func(...args);
		cache.set(key, result);
		return result;
	}) as T;
}

/**
 * 기존 호환성을 위한 레거시 memoize 함수 (제한 없는 캐시)
 * @deprecated memoize 함수를 사용하세요 (기본 maxSize: 100)
 * @param func 메모이제이션할 함수
 * @param keyGenerator 캐시 키 생성 함수 (선택사항)
 * @returns 메모이제이션된 함수
 */
export function memoizeUnlimited<T extends (...args: any[]) => any>(
	func: T,
	keyGenerator?: (...args: Parameters<T>) => string
): T {
	const cache = new Map<string, ReturnType<T>>();

	return ((...args: Parameters<T>) => {
		const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

		if (cache.has(key)) {
			return cache.get(key);
		}

		const result = func(...args);
		cache.set(key, result);
		return result;
	}) as T;
}

/**
 * 지연 로딩을 위한 Intersection Observer 래퍼
 * @param callback 요소가 뷰포트에 들어왔을 때 실행할 콜백
 * @param options Intersection Observer 옵션
 * @returns observer 인스턴스와 observe 함수
 */
export function createLazyLoader(
	callback: (entry: IntersectionObserverEntry) => void,
	options: IntersectionObserverInit = { threshold: 0.1 }
) {
	const observer = new IntersectionObserver((entries) => {
		entries.forEach((entry) => {
			if (entry.isIntersecting) {
				callback(entry);
				observer.unobserve(entry.target);
			}
		});
	}, options);

	return {
		observer,
		observe: (element: Element) => observer.observe(element)
	};
}

/**
 * 가상 스크롤링을 위한 아이템 계산 함수
 * @param totalItems 전체 아이템 수
 * @param itemHeight 각 아이템의 높이
 * @param containerHeight 컨테이너 높이
 * @param scrollTop 현재 스크롤 위치
 * @param buffer 버퍼 아이템 수 (기본값: 5)
 * @returns 렌더링할 아이템 범위와 오프셋 정보
 */
export function calculateVirtualScrollRange(
	totalItems: number,
	itemHeight: number,
	containerHeight: number,
	scrollTop: number,
	buffer: number = 5
) {
	const visibleItemCount = Math.ceil(containerHeight / itemHeight);
	const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
	const endIndex = Math.min(totalItems - 1, startIndex + visibleItemCount + buffer * 2);

	return {
		startIndex,
		endIndex,
		visibleItemCount,
		offsetY: startIndex * itemHeight,
		totalHeight: totalItems * itemHeight
	};
}

/**
 * 대용량 데이터 처리를 위한 청크 처리 함수
 * @param data 처리할 데이터 배열
 * @param chunkSize 청크 크기
 * @param processor 각 청크를 처리할 함수
 * @param delay 청크 간 지연 시간 (밀리초, 기본값: 0)
 * @returns Promise<void>
 */
export async function processInChunks<T>(
	data: T[],
	chunkSize: number,
	processor: (chunk: T[], chunkIndex: number) => Promise<void> | void,
	delay: number = 0
): Promise<void> {
	for (let i = 0; i < data.length; i += chunkSize) {
		const chunk = data.slice(i, i + chunkSize);
		const chunkIndex = Math.floor(i / chunkSize);

		await processor(chunk, chunkIndex);

		if (delay > 0 && i + chunkSize < data.length) {
			await new Promise((resolve) => setTimeout(resolve, delay));
		}
	}
}

/**
 * 메모리 효율적인 대용량 리스트 렌더링을 위한 윈도우 계산
 * @param totalItems 전체 아이템 수
 * @param visibleItems 화면에 보이는 아이템 수
 * @param currentIndex 현재 인덱스
 * @param windowSize 윈도우 크기 (기본값: visibleItems * 2)
 * @returns 렌더링할 윈도우 범위
 */
export function calculateRenderWindow(
	totalItems: number,
	visibleItems: number,
	currentIndex: number,
	windowSize: number = visibleItems * 2
) {
	const halfWindow = Math.floor(windowSize / 2);
	const start = Math.max(0, currentIndex - halfWindow);
	const end = Math.min(totalItems - 1, start + windowSize - 1);

	// 끝에 도달했을 때 시작점 재조정
	const adjustedStart = Math.max(0, end - windowSize + 1);

	return {
		start: adjustedStart,
		end,
		size: end - adjustedStart + 1
	};
}

/**
 * 검색 성능 최적화를 위한 디바운스된 검색 함수 생성기
 * @param searchFunction 실제 검색을 수행할 함수
 * @param delay 디바운스 지연 시간 (기본값: 300ms)
 * @returns 디바운스된 검색 함수
 */
export function createDebouncedSearch<T>(
	searchFunction: (query: string) => Promise<T>,
	delay: number = 300
) {
	let currentQuery = '';
	let searchPromise: Promise<T> | null = null;

	const debouncedSearch = debounce(async (query: string) => {
		currentQuery = query;
		searchPromise = searchFunction(query);
		return searchPromise;
	}, delay);

	return {
		search: (query: string) => {
			if (query === currentQuery && searchPromise) {
				return searchPromise;
			}
			return debouncedSearch(query);
		},
		getCurrentQuery: () => currentQuery
	};
}

/**
 * 캐시 관리 인터페이스
 */
export interface CacheItem<V> {
	value: V;
	timestamp: number;
	ttl: number;
}

export interface CacheInstance<K, V> {
	set: (key: K, value: V, ttl?: number) => void;
	get: (key: K) => V | null;
	has: (key: K) => boolean;
	delete: (key: K) => boolean;
	clear: () => void;
	size: () => number;
	cleanup: () => void;
}

/**
 * 성능 캐시 생성 함수
 * @param maxSize 최대 캐시 크기 (기본값: 100)
 * @param defaultTTL 기본 TTL (기본값: 5분)
 * @returns 캐시 인스턴스
 */
export function createPerformanceCache<K, V>(
	maxSize: number = 100,
	defaultTTL: number = 5 * 60 * 1000
): CacheInstance<K, V> {
	const cache = new Map<K, CacheItem<V>>();

	return {
		set: (key: K, value: V, ttl?: number): void => {
			// 캐시 크기 제한
			if (cache.size >= maxSize) {
				const firstKey = cache.keys().next().value;
				if (firstKey !== undefined) {
					cache.delete(firstKey);
				}
			}

			cache.set(key, {
				value,
				timestamp: Date.now(),
				ttl: ttl || defaultTTL
			});
		},

		get: (key: K): V | null => {
			const item = cache.get(key);

			if (!item) {
				return null;
			}

			// TTL 확인
			if (Date.now() - item.timestamp > item.ttl) {
				cache.delete(key);
				return null;
			}

			return item.value;
		},

		has: (key: K): boolean => {
			const item = cache.get(key);
			if (!item) return false;

			// TTL 확인
			if (Date.now() - item.timestamp > item.ttl) {
				cache.delete(key);
				return false;
			}

			return true;
		},

		delete: (key: K): boolean => {
			return cache.delete(key);
		},

		clear: (): void => {
			cache.clear();
		},

		size: (): number => {
			return cache.size;
		},

		cleanup: (): void => {
			const now = Date.now();
			for (const [key, item] of cache.entries()) {
				if (now - item.timestamp > item.ttl) {
					cache.delete(key);
				}
			}
		}
	};
}

/**
 * 전역 성능 캐시 인스턴스
 */
export const globalCache = createPerformanceCache<string, any>(200, 10 * 60 * 1000); // 10분 TTL

/**
 * 캐시 통계 정보 인터페이스
 */
export interface CacheStats {
	size: number;
	maxSize: number;
	hitRate: number;
	totalRequests: number;
	cacheHits: number;
	cacheMisses: number;
}

/**
 * 통계 기능이 포함된 고급 메모이제이션 함수
 * @param func 메모이제이션할 함수
 * @param maxSize 최대 캐시 크기 (기본값: 100)
 * @param keyGenerator 캐시 키 생성 함수 (선택사항)
 * @returns 메모이제이션된 함수와 통계 정보
 */
export function memoizeWithStats<T extends (...args: any[]) => any>(
	func: T,
	maxSize: number = 100,
	keyGenerator?: (...args: Parameters<T>) => string
): {
	memoizedFunction: T;
	getStats: () => CacheStats;
	clearCache: () => void;
} {
	const cache = new Map<string, ReturnType<T>>();
	let totalRequests = 0;
	let cacheHits = 0;
	let cacheMisses = 0;

	const memoizedFunction = ((...args: Parameters<T>) => {
		totalRequests++;
		const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

		if (cache.has(key)) {
			cacheHits++;
			// LRU: 최근 사용된 항목을 맨 뒤로 이동
			const value = cache.get(key)!;
			cache.delete(key);
			cache.set(key, value);
			return value;
		}

		cacheMisses++;

		// 캐시 크기 제한 (가장 오래된 항목 제거)
		if (cache.size >= maxSize) {
			const firstKey = cache.keys().next().value;
			if (firstKey !== undefined) {
				cache.delete(firstKey);
			}
		}

		const result = func(...args);
		cache.set(key, result);
		return result;
	}) as T;

	const getStats = (): CacheStats => ({
		size: cache.size,
		maxSize,
		hitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
		totalRequests,
		cacheHits,
		cacheMisses
	});

	const clearCache = () => {
		cache.clear();
		totalRequests = 0;
		cacheHits = 0;
		cacheMisses = 0;
	};

	return {
		memoizedFunction,
		getStats,
		clearCache
	};
}

/**
 * TTL(Time To Live) 기능이 포함된 메모이제이션 함수
 * @param func 메모이제이션할 함수
 * @param maxSize 최대 캐시 크기 (기본값: 100)
 * @param ttl TTL 시간 (밀리초, 기본값: 5분)
 * @param keyGenerator 캐시 키 생성 함수 (선택사항)
 * @returns 메모이제이션된 함수
 */
export function memoizeWithTTL<T extends (...args: any[]) => any>(
	func: T,
	maxSize: number = 100,
	ttl: number = 5 * 60 * 1000,
	keyGenerator?: (...args: Parameters<T>) => string
): T {
	const cache = new Map<string, { value: ReturnType<T>; timestamp: number }>();

	return ((...args: Parameters<T>) => {
		const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
		const now = Date.now();

		// 기존 캐시 확인 및 TTL 검사
		if (cache.has(key)) {
			const cached = cache.get(key)!;
			if (now - cached.timestamp < ttl) {
				// TTL 내에 있으면 LRU 업데이트
				cache.delete(key);
				cache.set(key, { ...cached, timestamp: now });
				return cached.value;
			} else {
				// TTL 만료된 항목 제거
				cache.delete(key);
			}
		}

		// 만료된 항목들 정리
		for (const [cacheKey, cached] of cache.entries()) {
			if (now - cached.timestamp >= ttl) {
				cache.delete(cacheKey);
			}
		}

		// 캐시 크기 제한
		if (cache.size >= maxSize) {
			const firstKey = cache.keys().next().value;
			if (firstKey !== undefined) {
				cache.delete(firstKey);
			}
		}

		const result = func(...args);
		cache.set(key, { value: result, timestamp: now });
		return result;
	}) as T;
}

/**
 * 아이템 높이 캐시 인터페이스
 */
export interface ItemHeightCacheInstance {
	measureItem: (index: number, element: HTMLElement) => number;
	getHeight: (index: number) => number;
	getAverageHeight: () => number;
	clear: () => void;
}

/**
 * 리스트 아이템 높이 측정 및 캐싱 생성 함수
 * @returns 아이템 높이 캐시 인스턴스
 */
export function createItemHeightCache(): ItemHeightCacheInstance {
	const heights = new Map<number, number>();
	let averageHeight = 0;
	let measuredCount = 0;

	return {
		measureItem: (index: number, element: HTMLElement): number => {
			const height = element.getBoundingClientRect().height;
			heights.set(index, height);

			// 평균 높이 업데이트
			measuredCount++;
			averageHeight = (averageHeight * (measuredCount - 1) + height) / measuredCount;

			return height;
		},

		getHeight: (index: number): number => {
			return heights.get(index) || averageHeight || 50; // 기본값 50px
		},

		getAverageHeight: (): number => {
			return averageHeight || 50;
		},

		clear: (): void => {
			heights.clear();
			averageHeight = 0;
			measuredCount = 0;
		}
	};
}
