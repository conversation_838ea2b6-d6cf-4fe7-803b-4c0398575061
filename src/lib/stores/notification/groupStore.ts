import { writable } from 'svelte/store';
import type {
	GroupStoreData,
	NotificationGroup,
	GroupFilter,
	GroupFormData,
	PaginatedApiResponse
} from '$lib/types/notification';
import type { User } from '$lib/User';
import { authClient } from '$lib/services/AxiosBackend';
import { executeMessage, handleCatch } from '$lib/Functions';
import { processPageData } from '$lib/utils/pagination';
import { API_ENDPOINTS, SUCCESS_MESSAGES } from '$lib/constants/notification';

// ===== 중복 요청 방지를 위한 캐시 =====
const requestCache = new Map<string, Promise<any>>();
const cacheTimeout = 2000; // 2초 동안 같은 요청 캐시

function getCacheKey(url: string, params?: any): string {
	return `${url}${params ? JSON.stringify(params) : ''}`;
}

function getCachedRequest<T>(key: string, requestFn: () => Promise<T>): Promise<any> | undefined {
	if (requestCache.has(key)) {
		return requestCache.get(key);
	}

	const promise = requestFn().finally(() => {
		// 요청 완료 후 일정 시간 후 캐시에서 제거
		setTimeout(() => {
			requestCache.delete(key);
		}, cacheTimeout);
	});

	requestCache.set(key, promise);
	return promise;
}

/**
 * 그룹 관리 스토어
 */
export const groupStore = writable<GroupStoreData>({});

/**
 * 그룹 목록 로드 (중복 요청 방지)
 */
export async function loadGroups(user: User, page: number = 1, filter: GroupFilter = {}) {
	const params = {
		userId: user.id,
		page,
		pageSize: 8,
		...filter
	};

	const cacheKey = getCacheKey(API_ENDPOINTS.GROUPS, params);

	return getCachedRequest(cacheKey, async () => {
		try {
			const { status, data } = await authClient.get(API_ENDPOINTS.GROUPS, {
				params
			});

			if (status === 200 && data?.success) {
				const response = data as PaginatedApiResponse<NotificationGroup>;

				if (!response.data?.items) {
					await executeMessage(
						'데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.',
						'error'
					);
					return;
				}

				const pageData = processPageData(response.data.pagination);
				groupStore.set(pageData);

				const groups = response.data.items;
				groupStore.update((currentData) => ({
					...currentData,
					groups
				}));
			} else {
				await executeMessage('그룹 정보를 받아올 수 없습니다.', 'error');
			}
		} catch (error) {
			await handleCatch(error);
		}
	});
}

/**
 * 그룹 생성
 */
export async function createGroup(user: User, groupData: GroupFormData): Promise<boolean> {
	try {
		const payload = {
			userId: user.id,
			...groupData
		};

		const { status, data } = await authClient.post(API_ENDPOINTS.GROUPS, payload);

		if (status === 200 && data?.success) {
			await executeMessage(data?.data?.message || SUCCESS_MESSAGES.GROUP_CREATED, 'info');
			return true;
		} else {
			await executeMessage(data?.data?.message || '그룹 생성에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 그룹 수정
 */
export async function updateGroup(
	user: User,
	groupId: number,
	groupData: GroupFormData
): Promise<boolean> {
	try {
		const payload = {
			userId: user.id,
			...groupData
		};

		const { status, data } = await authClient.put(`${API_ENDPOINTS.GROUPS}/${groupId}`, payload);

		if (status === 200 && data?.success) {
			await executeMessage(data?.data?.message || SUCCESS_MESSAGES.GROUP_UPDATED, 'info');
			return true;
		} else {
			await executeMessage(data?.data?.message || '그룹 수정에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 그룹 삭제
 */
export async function deleteGroup(user: User, groupId: number): Promise<boolean> {
	try {
		const payload = {
			userId: user.id
		};

		const { status, data } = await authClient.delete(`${API_ENDPOINTS.GROUPS}/${groupId}`, {
			data: payload
		});

		if (status === 200 && data?.success) {
			await executeMessage(data?.data?.message || SUCCESS_MESSAGES.GROUP_DELETED, 'info');
			return true;
		} else {
			await executeMessage(data?.data?.message || '그룹 삭제에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 특정 그룹 조회 (멤버 포함, 중복 요청 방지)
 */
export async function getGroup(user: User, groupId: number): Promise<NotificationGroup | null> {
	const params = {
		userId: user.id,
		include_members: true
	};

	const cacheKey = getCacheKey(`${API_ENDPOINTS.GROUPS}/${groupId}`, params);

	return getCachedRequest(cacheKey, async () => {
		try {
			const { status, data } = await authClient.get(`${API_ENDPOINTS.GROUPS}/${groupId}`, {
				params
			});

			if (status === 200 && data?.success) {
				if (data.data?.group) {
					return data.data.group as NotificationGroup;
				}
				// 서버 응답이 직접 그룹 객체인 경우
				return data.data as NotificationGroup;
			} else {
				await executeMessage(data?.data?.message || '그룹 정보를 받아올 수 없습니다.', 'error');
				return null;
			}
		} catch (error) {
			await handleCatch(error);
			return null;
		}
	});
}

/**
 * 그룹 멤버 추가
 */
export async function addGroupMembers(
	user: User,
	groupId: number,
	memberIds: number[]
): Promise<boolean> {
	try {
		const payload = {
			member_ids: memberIds
		};

		const { status, data } = await authClient.post(
			`${API_ENDPOINTS.GROUPS}/${groupId}/members`,
			payload
		);

		if (status === 200 && data?.success) {
			await executeMessage(data?.data?.message || '멤버가 추가되었습니다.', 'info');
			return true;
		} else {
			await executeMessage(data?.data?.message || '멤버 추가에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 그룹 멤버 제거
 */
export async function removeGroupMembers(
	user: User,
	groupId: number,
	memberId: number[]
): Promise<boolean> {
	try {
		const { status, data } = await authClient.delete(
			`${API_ENDPOINTS.GROUPS}/${groupId}/members/${memberId}`
		);

		if (status === 200 && data?.success) {
			await executeMessage(data?.data?.message || '멤버가 제거되었습니다.', 'info');
			return true;
		} else {
			await executeMessage(data?.data?.message || '멤버 제거에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 모든 그룹 목록 조회 (페이지네이션 없음)
 */
export async function getAllGroups(user: User): Promise<NotificationGroup[]> {
	try {
		const params = {
			userId: user.id,
			search: '',
			all: true
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.GROUPS, {
			params
		});

		if (status === 200 && data?.success) {
			console.log('그룹 목록 조회 성공:', data.data.items);
			return data.data.items as NotificationGroup[];
		} else {
			await executeMessage(data?.data?.message || '그룹 목록을 받아올 수 없습니다.', 'error');
			return [];
		}
	} catch (error) {
		await handleCatch(error);
		return [];
	}
}

/**
 * 스토어 초기화
 */
export function resetGroupStore() {
	groupStore.set({});
}
