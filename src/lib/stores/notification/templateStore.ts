import { writable } from 'svelte/store';
import type {
	NotificationTemplateStoreData,
	NotificationTemplate,
	TemplateFilter,
	TemplateFormData,
	PaginatedApiResponse
} from '$lib/types/notification';
import type { User } from '$lib/User';
import { authClient } from '$lib/services/AxiosBackend';
import { executeMessage, handleCatch } from '$lib/Functions';
import { processPageData } from '$lib/utils/pagination';
import { API_ENDPOINTS, SUCCESS_MESSAGES } from '$lib/constants/notification';

/**
 * 템플릿 관리 스토어
 */
export const templateStore = writable<NotificationTemplateStoreData>({});

/**
 * 템플릿 목록 로드
 */
export async function loadTemplates(
	user: User,
	page: number = 1,
	filter: TemplateFilter & { sort_by?: string; sort_order?: string } = {}
) {
	try {
		const params = {
			userId: user.id,
			page,
			...filter
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.TEMPLATES, {
			params
		});

		if ((status >= 200 && status < 300) && data?.success) {
			const response = data as PaginatedApiResponse<NotificationTemplate>;

			if (!response.data?.items) {
				await executeMessage(
					'데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.',
					'error'
				);
				return;
			}

			const pageData = processPageData(response.data.pagination);
			templateStore.set(pageData);

			const templates = response.data.items;
			templateStore.update((currentData) => ({
				...currentData,
				templates
			}));
		} else {
			await executeMessage('템플릿 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 템플릿 생성
 */
export async function createTemplate(user: User, templateData: TemplateFormData): Promise<boolean> {
	try {
		const payload = {
			userId: user.id,
			...templateData
		};

		const { status, data } = await authClient.post(API_ENDPOINTS.TEMPLATES, payload);

		if ((status >= 200 && status < 300) && data?.success) {
			await executeMessage(data.message || SUCCESS_MESSAGES.TEMPLATE_CREATED, 'info');
			return true;
		} else {
			await executeMessage(data?.message || '템플릿 생성에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 템플릿 수정
 */
export async function updateTemplate(
	user: User,
	templateId: number,
	templateData: TemplateFormData
): Promise<boolean> {
	try {
		const payload = {
			userId: user.id,
			...templateData
		};

		const { status, data } = await authClient.put(
			`${API_ENDPOINTS.TEMPLATES}/${templateId}`,
			payload
		);

		if ((status >= 200 && status < 300) && data?.success) {
			await executeMessage(data.message || SUCCESS_MESSAGES.TEMPLATE_UPDATED, 'info');
			return true;
		} else {
			await executeMessage(data?.message || '템플릿 수정에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 템플릿 삭제
 */
export async function deleteTemplate(user: User, templateId: number): Promise<boolean> {
	try {
		const payload = {
			userId: user.id
		};

		const { status, data } = await authClient.delete(`${API_ENDPOINTS.TEMPLATES}/${templateId}`, {
			data: payload
		});

		if ((status >= 200 && status < 300) && data?.success) {
			await executeMessage(data.message || SUCCESS_MESSAGES.TEMPLATE_DELETED, 'info');
			return true;
		} else {
			await executeMessage(data?.message || '템플릿 삭제에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 특정 템플릿 조회
 */
export async function getTemplate(
	user: User,
	templateId: number
): Promise<NotificationTemplate | null> {
	try {
		const params = {
			userId: user.id
		};

		const { status, data } = await authClient.get(`${API_ENDPOINTS.TEMPLATES}/${templateId}`, {
			params
		});

		if ((status >= 200 && status < 300) && data?.success) {
			return data.data as NotificationTemplate;
		} else {
			await executeMessage(data?.message || '템플릿 정보를 받아올 수 없습니다.', 'error');
			return null;
		}
	} catch (error) {
		await handleCatch(error);
		return null;
	}
}

/**
 * 모든 템플릿 목록 조회 (페이지네이션 없음, 사용 빈도순)
 */
export async function getAllTemplates(user: User): Promise<NotificationTemplate[]> {
	try {
		const params = {
			userId: user.id,
			all: true,
			sort_by: 'usage_count',
			sort_order: 'desc'
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.TEMPLATES, {
			params
		});

		if ((status >= 200 && status < 300) && data?.success) {
			return data.data as NotificationTemplate[];
		} else {
			await executeMessage(data?.message || '템플릿 목록을 받아올 수 없습니다.', 'error');
			return [];
		}
	} catch (error) {
		await handleCatch(error);
		return [];
	}
}

/**
 * 템플릿 사용 횟수 증가
 */
export async function incrementTemplateUsage(user: User, templateId: number): Promise<void> {
	try {
		const payload = {
			userId: user.id
		};

		await authClient.post(`${API_ENDPOINTS.TEMPLATES}/${templateId}/use`, payload);
	} catch (error) {
		// 사용 횟수 증가 실패는 사용자에게 알리지 않음 (백그라운드 작업)
		console.error('템플릿 사용 횟수 증가 실패:', error);
	}
}

/**
 * 스토어 초기화
 */
export function resetTemplateStore() {
	templateStore.set({});
}
