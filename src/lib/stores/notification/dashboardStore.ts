import { writable } from 'svelte/store';
import type {
	DashboardStoreData,
	NotificationStats,
	NotificationGroupStats,
	ApiResponse
} from '$lib/types/notification';
import type { User } from '$lib/User';
import { authClient } from '$lib/services/AxiosBackend';
import { executeMessage, handleCatch } from '$lib/Functions';
import { API_ENDPOINTS } from '$lib/constants/notification';

/**
 * 대시보드 스토어
 */
export const dashboardStore = writable<DashboardStoreData>({
	loading: false,
	error: undefined
});

/**
 * 더미 데이터 생성 (개발/테스트용)
 */
function generateDummyStats(): NotificationStats {
	const today = new Date();
	const weeklyTrend = [];

	// 최근 7일 데이터 생성
	for (let i = 6; i >= 0; i--) {
		const date = new Date(today);
		date.setDate(date.getDate() - i);
		weeklyTrend.push({
			date: date.toISOString().split('T')[0],
			count: Math.floor(Math.random() * 50) + 10
		});
	}

	return {
		today_sent: Math.floor(Math.random() * 30) + 5,
		weekly_trend: weeklyTrend,
		priority_read_rates: [
			{ priority: 'urgent', read_rate: Math.floor(Math.random() * 20) + 80 },
			{ priority: 'high', read_rate: Math.floor(Math.random() * 25) + 70 },
			{ priority: 'normal', read_rate: Math.floor(Math.random() * 30) + 60 },
			{ priority: 'low', read_rate: Math.floor(Math.random() * 35) + 45 }
		],
		online_users: Math.floor(Math.random() * 20) + 5,
		group_stats: [
			{
				id: 1,
				name: '관리팀',
				description: '관리 업무 담당 직원들',
				members_count: 8,
				sent_count: 15,
				read_count: 13,
				read_rate: 87,
				last_activity: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2시간 전
			},
			{
				id: 2,
				name: '영업팀',
				description: '영업 및 고객 관리 담당',
				members_count: 12,
				sent_count: 23,
				read_count: 17,
				read_rate: 74,
				last_activity: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30분 전
			},
			{
				id: 3,
				name: '개발팀',
				description: '시스템 개발 및 유지보수',
				members_count: 15,
				sent_count: 18,
				read_count: 16,
				read_rate: 89,
				last_activity: new Date(Date.now() - 10 * 60 * 1000).toISOString() // 10분 전
			},
			{
				id: 4,
				name: '마케팅팀',
				description: '마케팅 및 홍보 업무',
				members_count: 6,
				sent_count: 12,
				read_count: 8,
				read_rate: 67,
				last_activity: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString() // 4시간 전
			},
			{
				id: 5,
				name: '품질관리팀',
				description: '품질 검사 및 관리',
				members_count: 10,
				sent_count: 9,
				read_count: 8,
				read_rate: 89,
				last_activity: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1시간 전
			}
		] as NotificationGroupStats[]
	};
}

/**
 * 대시보드 통계 로드
 */
export async function loadDashboardStats(user: User) {
	try {
		// 로딩 상태 설정
		dashboardStore.update((current) => ({
			...current,
			loading: true,
			error: undefined
		}));

		// 개발 환경에서는 더미 데이터 사용
		if (import.meta.env.DEV) {
			// 로딩 시뮬레이션
			await new Promise((resolve) => setTimeout(resolve, 1000));

			const stats = generateDummyStats();
			dashboardStore.update((current) => ({
				...current,
				stats,
				loading: false,
				error: undefined
			}));
			return;
		}

		const params = {
			userId: user.id
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.DASHBOARD_STATS, {
			params
		});

		if (status === 200 && data?.success) {
			const response = data as ApiResponse<NotificationStats>;

			if (!response.data) {
				await executeMessage(
					'대시보드 데이터를 받아올 수 없습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.',
					'error'
				);
				dashboardStore.update((current) => ({
					...current,
					loading: false,
					error: '데이터 로드 실패'
				}));
				return;
			}

			const stats = response.data;
			dashboardStore.update((current) => ({
				...current,
				stats,
				loading: false,
				error: undefined
			}));
		} else {
			const errorMessage = data?.message || '대시보드 통계를 받아올 수 없습니다.';
			await executeMessage(errorMessage, 'error');
			dashboardStore.update((current) => ({
				...current,
				loading: false,
				error: errorMessage
			}));
		}
	} catch (error) {
		const errorMessage = (await handleCatch(error, true)) as string;
		dashboardStore.update((current) => ({
			...current,
			loading: false,
			error: errorMessage
		}));
	}
}

/**
 * 실시간 온라인 사용자 수 업데이트
 */
export async function updateOnlineUsers(user: User) {
	try {
		const params = {
			userId: user.id,
			only_online: true
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.DASHBOARD_STATS, {
			params
		});

		if (status === 200 && data?.success && data.data?.online_users !== undefined) {
			dashboardStore.update((current) => ({
				...current,
				stats: current.stats
					? {
							...current.stats,
							online_users: data.data.online_users
						}
					: undefined
			}));
		}
	} catch (error) {
		// 실시간 업데이트 실패는 사용자에게 알리지 않음
		console.error('온라인 사용자 수 업데이트 실패:', error);
	}
}

/**
 * 특정 날짜 범위의 통계 조회
 */
export async function loadCustomStats(
	user: User,
	dateFrom: string,
	dateTo: string
): Promise<NotificationStats | null> {
	try {
		const params = {
			userId: user.id,
			date_from: dateFrom,
			date_to: dateTo
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.DASHBOARD_STATS, {
			params
		});

		if (status === 200 && data?.success) {
			return data.data as NotificationStats;
		} else {
			await executeMessage(data?.message || '통계 데이터를 받아올 수 없습니다.', 'error');
			return null;
		}
	} catch (error) {
		await handleCatch(error);
		return null;
	}
}

/**
 * 대시보드 데이터 새로고침
 */
export async function refreshDashboard(user: User) {
	await loadDashboardStats(user);
}

/**
 * 스토어 초기화
 */
export function resetDashboardStore() {
	dashboardStore.set({
		loading: false,
		error: undefined
	});
}

/**
 * 로딩 상태 설정
 */
export function setDashboardLoading(loading: boolean) {
	dashboardStore.update((current) => ({
		...current,
		loading
	}));
}

/**
 * 에러 상태 설정
 */
export function setDashboardError(error: string | undefined) {
	dashboardStore.update((current) => ({
		...current,
		error,
		loading: false
	}));
}
