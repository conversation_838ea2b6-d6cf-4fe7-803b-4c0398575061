/**
 * 알림 관련 스토어 통합 export 파일
 *
 * 이 파일은 모든 알림 관련 스토어와 함수들을 중앙에서 관리하고 export합니다.
 * 기존 스토어 구조와 일관성을 유지하며, SSE 관련 스토어를 추가로 포함합니다.
 */

// 기존 알림 스토어들 (이미 존재하는 경우)
// export * from './dashboardStore';
// export * from './groupStore';
// export * from './historyStore';
// export * from './memberStore';
// export * from './templateStore';

// SSE 관련 스토어 및 서비스
export * from './sseNotificationStore';

// TODO: 다음 작업들에서 추가될 예정
// - SSE 서비스 함수들
// - 읽음 확인 서비스 함수들
// - 알림 핸들러 함수들

/**
 * 모든 알림 관련 기능을 하나의 객체로 그룹화 (선택사항)
 */
export const notificationStores = {
	// SSE 관련
	sse: {
		// sseNotificationStore 관련 함수들이 여기에 추가될 예정
	}

	// 기존 스토어들도 필요시 여기에 추가 가능
};

// 타입 재export
export type {
	SSENotification,
	SSENotificationState,
	SSEConnectionState,
	NotificationPriority,
	ReadReceiptRequest,
	ReadReceiptResponse
} from '$lib/types/sse';
