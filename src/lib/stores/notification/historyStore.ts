import { writable } from 'svelte/store';
import type {
	NotificationHistoryStoreData,
	Notification,
	NotificationFilter,
	NotificationRecipient,
	PaginatedApiResponse
} from '$lib/types/notification';
import type { User } from '$lib/User';
import { authClient } from '$lib/services/AxiosBackend';
import { executeMessage, handleCatch } from '$lib/Functions';
import {
	processPageData,
	paginationCacheManager,
	PaginationPerformanceMonitor
} from '$lib/utils/pagination';
import { API_ENDPOINTS, SUCCESS_MESSAGES } from '$lib/constants/notification';
import { debounce, createPerformanceCache, processInChunks } from '$lib/utils/performance';

// ===== 성능 최적화된 캐시 시스템 =====
const requestCache = createPerformanceCache<string, Promise<any>>(100, 5 * 60 * 1000); // 5분 TTL
const dataCache = createPerformanceCache<string, any>(200, 10 * 60 * 1000); // 10분 TTL
const performanceMonitor = PaginationPerformanceMonitor;

/**
 * 캐시 키 생성 함수 (성능 최적화)
 */
function getCacheKey(url: string, params?: any): string {
	return `${url}${params ? JSON.stringify(params) : ''}`;
}

/**
 * 최적화된 캐시 요청 함수
 */
function getCachedRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
	// 진행 중인 요청이 있는지 확인
	const existingRequest = requestCache.get(key);
	if (existingRequest) {
		return existingRequest as Promise<T>;
	}

	// 새 요청 생성 및 캐시
	const promise = requestFn().finally(() => {
		// 요청 완료 후 캐시에서 제거 (메모리 절약)
		requestCache.delete(key);
	});

	requestCache.set(key, promise);
	return promise;
}

/**
 * 데이터 캐시 관리 함수들
 */
const historyDataCache = {
	get: (key: string) => dataCache.get(key),
	set: (key: string, data: any, ttl?: number) => dataCache.set(key, data, ttl),
	delete: (key: string) => dataCache.delete(key),
	clear: () => dataCache.clear(),
	cleanup: () => dataCache.cleanup()
};

/**
 * 알림 히스토리 스토어
 */
export const historyStore = writable<NotificationHistoryStoreData>({
	loading: false,
	error: null
});

/**
 * 성능 최적화된 알림 히스토리 목록 로드
 */
export async function loadNotificationHistory(
	user: User,
	page: number = 1,
	filter: NotificationFilter = {},
	pageSize: number = 20
) {
	const params = {
		userId: user.id,
		page,
		pageSize,
		...filter
	};

	const cacheKey = getCacheKey(API_ENDPOINTS.HISTORY, params);
	const dataCacheKey = `history-${cacheKey}`;

	// 성능 측정 시작
	const endMeasure = performanceMonitor.startMeasure(`loadHistory-${page}`);

	return getCachedRequest(cacheKey, async () => {
		try {
			// 로딩 상태 설정
			historyStore.update((current) => ({
				...current,
				loading: true,
				error: null
			}));

			// 캐시된 데이터 확인 (빠른 응답을 위해)
			const cachedData = historyDataCache.get(dataCacheKey);
			if (cachedData && page === 1) {
				// 첫 페이지의 경우 캐시된 데이터를 먼저 표시
				historyStore.update((current) => ({
					...current,
					...cachedData,
					loading: true // 백그라운드에서 새 데이터 로드 중
				}));
			}

			const { status, data } = await authClient.get(API_ENDPOINTS.HISTORY, {
				params
			});

			if (status === 200 && data?.success) {
				const response = data as PaginatedApiResponse<Notification>;

				if (!response.data?.items) {
					await executeMessage(
						'데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.',
						'error'
					);
					historyStore.update((current) => ({
						...current,
						loading: false,
						error: '데이터 로드 실패'
					}));
					return;
				}

				const pageData = processPageData(response.data.pagination);
				const notifications = response.data.items;

				// 대용량 데이터 처리 최적화
				if (notifications.length > 100) {
					// 청크 단위로 처리하여 UI 블로킹 방지
					await processInChunks(
						notifications,
						50, // 50개씩 처리
						async (chunk, chunkIndex) => {
							if (chunkIndex === 0) {
								// 첫 번째 청크는 즉시 표시
								historyStore.update((current) => ({
									...current,
									...pageData,
									notifications: chunk,
									loading: true
								}));
							} else {
								// 나머지 청크는 기존 데이터에 추가
								historyStore.update((current) => ({
									...current,
									notifications: [...(current.notifications || []), ...chunk]
								}));
							}
						},
						10 // 10ms 지연
					);

					historyStore.update((current) => ({
						...current,
						loading: false,
						error: null
					}));
				} else {
					// 일반적인 크기의 데이터는 한 번에 처리
					const storeData = {
						...pageData,
						notifications,
						loading: false,
						error: null
					};

					historyStore.update((current) => ({
						...current,
						...storeData
					}));

					// 데이터 캐시 저장 (첫 페이지만)
					if (page === 1) {
						historyDataCache.set(dataCacheKey, storeData, 2 * 60 * 1000); // 2분 캐시
					}
				}
			} else {
				const errorMessage = data?.message || '알림 히스토리를 받아올 수 없습니다.';
				await executeMessage(errorMessage, 'error');
				historyStore.update((current) => ({
					...current,
					loading: false,
					error: errorMessage
				}));
			}
		} catch (error) {
			const errorMessage = (await handleCatch(error, true)) as string;
			historyStore.update((current) => ({
				...current,
				loading: false,
				error: errorMessage
			}));
		} finally {
			// 성능 측정 종료
			endMeasure();
		}
	});
}

/**
 * 특정 알림 상세 조회
 */
export async function getNotificationDetail(
	user: User,
	notificationId: string
): Promise<Notification | null> {
	try {
		const params = {
			userId: user.id,
			include_recipients: true
		};

		const { status, data } = await authClient.get(
			`${API_ENDPOINTS.HISTORY}/${notificationId}`,
			{
				params
			}
		);

		if (status === 200 && data?.success) {
			return data.data as Notification;
		} else {
			await executeMessage(data?.message || '알림 정보를 받아올 수 없습니다.', 'error');
			return null;
		}
	} catch (error) {
		await handleCatch(error);
		return null;
	}
}

/**
 * 알림 수신자 목록 조회
 */
export async function getNotificationRecipients(
	user: User,
	notificationId: string
): Promise<NotificationRecipient[]> {
	try {
		const params = {
			userId: user.id
		};

		const { status, data } = await authClient.get(
			`${API_ENDPOINTS.HISTORY}/${notificationId}/recipients`,
			{
				params
			}
		);

		if (status === 200 && data?.success) {
			return data.data as NotificationRecipient[];
		} else {
			await executeMessage(data?.message || '수신자 목록을 받아올 수 없습니다.', 'error');
			return [];
		}
	} catch (error) {
		await handleCatch(error);
		return [];
	}
}

/**
 * 알림 취소
 */
export async function cancelNotification(user: User, notificationId: string): Promise<boolean> {
	try {
		const payload = {
			userId: user.id,
			notificationId
		};

		const { status, data } = await authClient.post(API_ENDPOINTS.NOTIFICATION_CANCEL, payload);

		if (status === 200 && data?.success) {
			await executeMessage(data?.message || SUCCESS_MESSAGES.NOTIFICATION_CANCELLED, 'info');
			return true;
		} else {
			await executeMessage(data?.message || '알림 취소에 실패했습니다.', 'error');
			return false;
		}
	} catch (error) {
		await handleCatch(error);
		return false;
	}
}

/**
 * 성능 최적화된 히스토리 새로고침
 */
export async function refreshHistory(
	user: User,
	filter: NotificationFilter = {},
	pageSize: number = 20
) {
	// 모든 캐시 클리어
	requestCache.clear();
	historyDataCache.clear();
	paginationCacheManager.clear();

	await loadNotificationHistory(user, 1, filter, pageSize);
}

/**
 * 디바운스된 검색 함수 (성능 최적화)
 */
export const debouncedSearch = debounce(
	async (user: User, filter: NotificationFilter, pageSize: number = 20) => {
		await loadNotificationHistory(user, 1, filter, pageSize);
	},
	300 // 300ms 디바운스
);

/**
 * 캐시 관리 함수들
 */
export const historyCacheManager = {
	/**
	 * 모든 캐시 정리
	 */
	cleanup: () => {
		requestCache.cleanup();
		historyDataCache.cleanup();
		paginationCacheManager.cleanup();
	},

	/**
	 * 모든 캐시 삭제
	 */
	clearAll: () => {
		requestCache.clear();
		historyDataCache.clear();
		paginationCacheManager.clear();
	},

	/**
	 * 특정 필터의 캐시만 삭제
	 */
	clearFilter: (filter: NotificationFilter) => {
		const filterKey = JSON.stringify(filter);
		// 해당 필터와 관련된 캐시 키들을 찾아서 삭제
		// 실제 구현에서는 더 정교한 키 매칭이 필요할 수 있음
		historyDataCache.clear(); // 간단히 전체 데이터 캐시 클리어
	},

	/**
	 * 캐시 상태 정보
	 */
	getStatus: () => ({
		requestCacheSize: requestCache.size(),
		dataCacheSize: historyDataCache.get('size') || 0,
		paginationCacheSize: paginationCacheManager.size()
	})
};

/**
 * 성능 모니터링 함수들
 */
export const historyPerformanceMonitor = {
	/**
	 * 성능 메트릭 조회
	 */
	getMetrics: () => performanceMonitor.getAllMetrics(),

	/**
	 * 특정 페이지의 성능 메트릭 조회
	 */
	getPageMetrics: (page: number) => performanceMonitor.getMetrics(`loadHistory-${page}`),

	/**
	 * 성능 메트릭 초기화
	 */
	clearMetrics: () => performanceMonitor.clear()
};

/**
 * 스토어 초기화
 */
export function resetHistoryStore() {
	historyStore.set({
		loading: false,
		error: null
	});
}

/**
 * 로딩 상태 설정
 */
export function setHistoryLoading(loading: boolean) {
	historyStore.update((current) => ({
		...current,
		loading
	}));
}

/**
 * 에러 상태 설정
 */
export function setHistoryError(error: string | null) {
	historyStore.update((current) => ({
		...current,
		error,
		loading: false
	}));
}
