import type { Member } from '$lib/types/types';
import type { User } from '$lib/User';
import { authClient } from '$lib/services/AxiosBackend';
import { handleCatch } from '$lib/Functions';
import { API_ENDPOINTS } from '$lib/constants/notification';

// ===== 중복 요청 방지를 위한 캐시 =====
const requestCache = new Map<string, Promise<any>>();
const cacheTimeout = 5000; // 5초 동안 같은 요청 캐시

function getCacheKey(url: string, params?: any): string {
	return `${url}${params ? JSON.stringify(params) : ''}`;
}

function getCachedRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
	if (requestCache.has(key)) {
		return requestCache.get(key);
	}

	const promise = requestFn().finally(() => {
		// 요청 완료 후 일정 시간 후 캐시에서 제거
		setTimeout(() => {
			requestCache.delete(key);
		}, cacheTimeout);
	});

	requestCache.set(key, promise);
	return promise;
}

/**
 * 활성 직원 목록 조회 (알림 대상 선택용, 중복 요청 방지)
 */
export async function getActiveMembers(
	user: User,
	params?: {
		search?: string;
		exclude_group_id?: number;
		role?: string;
		status?: string;
		keyword?: string;
		pageSize?: number;
	}
): Promise<{ success: boolean; data?: { items: Member[] }; message?: string }> {
	const cacheKey = getCacheKey(API_ENDPOINTS.ACTIVE_MEMBERS, params);

	return getCachedRequest(cacheKey, async () => {
		try {
			const requestParams = {
				userId: user.id,
				...params
			};

			const response = await authClient.get(API_ENDPOINTS.ACTIVE_MEMBERS, {
				params: requestParams
			});

			if (response.status === 200 && response.data?.success) {
				return response.data;
			} else {
				return {
					success: false,
					message: response.data?.message || '직원 목록 조회에 실패했습니다.'
				};
			}
		} catch (error) {
			await handleCatch(error);
			return {
				success: false,
				message: '네트워크 오류가 발생했습니다.'
			};
		}
	});
}
