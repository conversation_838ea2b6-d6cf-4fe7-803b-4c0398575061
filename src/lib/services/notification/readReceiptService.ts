/**
 * 읽음 확인 API 서비스
 *
 * 이 서비스는 알림의 읽음 확인을 서버로 전송하는 기능을 제공합니다.
 * 기존 authClient (AxiosBackend.ts)를 활용하여 API 호출을 수행하며,
 * 재시도 로직과 오프라인 복구 기능을 포함합니다.
 */

import type {
	ReadReceiptRequest,
	ReadReceiptResponse,
	BatchReadReceiptRequest,
	BatchReadReceiptResponse
} from '$lib/types/sse';

// TODO: 다음 작업에서 구현될 예정
// - authClient를 활용한 읽음 확인 API 호출
// - 재시도 로직 구현
// - 배치 처리 기능
// - 오프라인 복구 로직

/**
 * 읽음 확인 서비스 placeholder
 * 실제 구현은 작업 3.1에서 진행됩니다.
 */
export const readReceiptService = {
	/**
	 * 단일 읽음 확인 전송
	 */
	sendReadReceipt: async (notificationId: string): Promise<boolean> => {
		throw new Error('읽음 확인 서비스가 아직 구현되지 않았습니다.');
	},

	/**
	 * 배치 읽음 확인 전송 (오프라인 복구용)
	 */
	sendBatchReadReceipts: async (notificationIds: string[]): Promise<boolean> => {
		throw new Error('배치 읽음 확인 서비스가 아직 구현되지 않았습니다.');
	},

	/**
	 * 재시도 로직을 포함한 읽음 확인 전송
	 */
	retryReadReceipt: async (notificationId: string, maxRetries: number = 3): Promise<boolean> => {
		throw new Error('재시도 읽음 확인 서비스가 아직 구현되지 않았습니다.');
	}
};

/**
 * 단일 읽음 확인 전송 함수 placeholder
 */
export async function sendReadReceipt(notificationId: string): Promise<boolean> {
	throw new Error('읽음 확인 전송 함수가 아직 구현되지 않았습니다.');
}

/**
 * 배치 읽음 확인 전송 함수 placeholder
 */
export async function sendBatchReadReceipts(notificationIds: string[]): Promise<boolean> {
	throw new Error('배치 읽음 확인 전송 함수가 아직 구현되지 않았습니다.');
}
