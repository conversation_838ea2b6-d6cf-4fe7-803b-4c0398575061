import type { User } from '$lib/User';
import type {
	NotificationFormData,
	Notification,
	NotificationFilter,
	ApiResponse,
	PaginatedApiResponse
} from '$lib/types/notification';
import { authClient } from '$lib/services/AxiosBackend';
import { handleCatch } from '$lib/Functions';
import { API_ENDPOINTS, ERROR_CODES, ERROR_MESSAGES } from '$lib/constants/notification';

/**
 * 알림 관련 커스텀 에러 - 함수형(팩토리) + 타입 가드
 */
export interface NotificationErrorShape extends Error {
	code: string;
	details?: any;
	name: 'NotificationError';
}

/**
 * 커스텀 알림 에러 생성 함수
 */
export function createNotificationError(
	message: string,
	code: string,
	details?: any
): NotificationErrorShape {
	const err = new Error(message) as NotificationErrorShape;
	err.name = 'NotificationError';
	err.code = code;
	err.details = details;
	return err;
}

/**
 * 커스텀 알림 에러 타입 가드
 */
export function isNotificationError(error: unknown): error is NotificationErrorShape {
	return (
		typeof error === 'object' &&
		error !== null &&
		(error as any).name === 'NotificationError' &&
		typeof (error as any).code === 'string'
	);
}

/**
 * 알림 데이터 검증 (서버 전송 전 내부 검증)
 * - 외부로 노출하지 않음
 */
function validateNotificationData(formData: NotificationFormData): void {
	const errors: string[] = [];

	// 제목 검증
	if (!formData.title?.trim()) {
		errors.push('제목을 입력해주세요.');
	} else if (formData.title.trim().length < 2) {
		errors.push('제목은 최소 2자 이상 입력해주세요.');
	} else if (formData.title.trim().length > 200) {
		errors.push('제목은 200자 이내로 입력해주세요.');
	}

	// 내용 검증
	if (!formData.content?.trim()) {
		errors.push('내용을 입력해주세요.');
	} else if (formData.content.trim().length < 5) {
		errors.push('내용은 최소 5자 이상 입력해주세요.');
	} else if (formData.content.trim().length > 1000) {
		errors.push('내용은 1000자 이내로 입력해주세요.');
	}

	// 대상 검증
	if (formData.target_type === 'group' && (!formData.target_ids || formData.target_ids.length === 0)) {
		errors.push('그룹을 선택해주세요.');
	}

	if (formData.target_type === 'individual' && (!formData.target_ids || formData.target_ids.length === 0)) {
		errors.push('직원을 선택해주세요.');
	}

	// 우선순위 검증
	const validPriorities = ['low', 'normal', 'high', 'urgent'];
	if (!validPriorities.includes(formData.priority)) {
		errors.push('올바른 우선순위를 선택해주세요.');
	}

	// URL 형식 검증 (선택사항)
	if (formData.action_url && formData.action_url.trim()) {
		try {
			const url = new URL(formData.action_url);
			if (!['http:', 'https:'].includes(url.protocol)) {
				errors.push('HTTP 또는 HTTPS URL만 허용됩니다.');
			}
		} catch {
			errors.push('올바른 URL 형식을 입력해주세요. (예: https://example.com)');
		}
	}

	// 에러가 있으면 예외 발생
	if (errors.length > 0) {
		throw createNotificationError(
			errors.length === 1 ? errors[0] : `입력 데이터 오류:\n${errors.join('\n')}`,
			ERROR_CODES.VALIDATION_ERROR,
			{ validation_errors: errors }
		);
	}
}

/**
 * 알림 전송 (함수형)
 */
export async function send(user: User, formData: NotificationFormData): Promise<ApiResponse> {
	const startTime = Date.now();

	try {
		// 입력 데이터 검증
		validateNotificationData(formData);

		const payload = {
			userId: user.id,
			...formData,
			// 전송 시간 추가 (서버에서 처리 시간 추적용)
			client_timestamp: new Date().toISOString()
		};

		// 타임아웃 설정 (3초)
		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => {
				reject(
					createNotificationError(
						'알림 전송 요청이 시간 초과되었습니다. (3초)',
						ERROR_CODES.NETWORK_ERROR
					)
				);
			}, 3000);
		});

		// API 호출과 타임아웃 경쟁
		const apiPromise = authClient.post(API_ENDPOINTS.NOTIFICATION_SEND, payload);

		const { status, data } = (await Promise.race([apiPromise, timeoutPromise])) as any;

		const elapsedTime = Date.now() - startTime;
		console.log(`알림 전송 API 응답 시간: ${elapsedTime}ms`);

		if (status === 200 && data?.success) {
			// 성공 응답에 처리 시간 정보 추가
			return {
				...data,
				processing_time: elapsedTime
			} as ApiResponse;
		} else {
			throw createNotificationError(
				data?.message || ERROR_MESSAGES.SEND_FAILED,
				ERROR_CODES.SEND_FAILED,
				{ ...data, processing_time: elapsedTime }
			);
		}
	} catch (error: any) {
		const elapsedTime = Date.now() - startTime;

		if (isNotificationError(error)) {
			// 처리 시간 정보 추가
			error.details = { ...error.details, processing_time: elapsedTime };
			throw error;
		}

		// 네트워크 에러 처리
		if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
			throw createNotificationError(
				'네트워크 연결 시간이 초과되었습니다.',
				ERROR_CODES.NETWORK_ERROR,
				{ processing_time: elapsedTime }
			);
		}

		await handleCatch(error);
		throw createNotificationError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, {
			original_error: error,
			processing_time: elapsedTime
		});
	}
}

/**
 * 알림 목록 조회 (함수형)
 */
export async function getList(
	user: User,
	page: number = 1,
	filter: NotificationFilter = {}
): Promise<PaginatedApiResponse<Notification>> {
	try {
		const params = {
			userId: user.id,
			page,
			...filter
		};

		const { status, data } = await authClient.get(API_ENDPOINTS.NOTIFICATIONS, {
			params
		});

		if (status === 200 && data?.success) {
			return data as PaginatedApiResponse<Notification>;
		} else {
			throw createNotificationError(
				data?.message || ERROR_MESSAGES.NETWORK_ERROR,
				ERROR_CODES.NETWORK_ERROR,
				data
			);
		}
	} catch (error) {
		if (isNotificationError(error)) {
			throw error;
		}
		await handleCatch(error);
		throw createNotificationError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
	}
}

/**
 * 특정 알림 조회 (함수형)
 */
export async function getById(user: User, notificationId: string): Promise<Notification> {
	try {
		const params = {
			userId: user.id
		};

		const { status, data } = await authClient.get(
			`${API_ENDPOINTS.NOTIFICATIONS}/${notificationId}`,
			{
				params
			}
		);

		if (status === 200 && data?.success) {
			return data.data as Notification;
		} else {
			throw createNotificationError(
				data?.message || ERROR_MESSAGES.NOTIFICATION_NOT_FOUND,
				ERROR_CODES.NOTIFICATION_NOT_FOUND,
				data
			);
		}
	} catch (error) {
		if (isNotificationError(error)) {
			throw error;
		}
		await handleCatch(error);
		throw createNotificationError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
	}
}

/**
 * 알림 취소 (함수형)
 */
export async function cancel(user: User, notificationId: string): Promise<ApiResponse> {
	try {
		const payload = {
			userId: user.id,
			notificationId
		};

		const { status, data } = await authClient.post(API_ENDPOINTS.NOTIFICATION_CANCEL, payload);

		if (status === 200 && data?.success) {
			return data as ApiResponse;
		} else {
			throw createNotificationError(
				data?.message || ERROR_MESSAGES.ALREADY_CANCELLED,
				ERROR_CODES.ALREADY_CANCELLED,
				data
			);
		}
	} catch (error) {
		if (isNotificationError(error)) {
			throw error;
		}
		await handleCatch(error);
		throw createNotificationError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
	}
}

/**
 * [클라이언트] 폼 데이터 유효성 사전 검사
 */
export function validateFormData(formData: NotificationFormData): {
	isValid: boolean;
	errors: Record<string, string>;
} {
	const errors: Record<string, string> = {};

	// 제목 검증
	if (!formData.title?.trim()) {
		errors.title = '제목을 입력해주세요.';
	} else if (formData.title.trim().length < 2) {
		errors.title = '제목은 최소 2자 이상 입력해주세요.';
	} else if (formData.title.trim().length > 200) {
		errors.title = '제목은 200자 이내로 입력해주세요.';
	}

	// 내용 검증
	if (!formData.content?.trim()) {
		errors.content = '내용을 입력해주세요.';
	} else if (formData.content.trim().length < 5) {
		errors.content = '내용은 최소 5자 이상 입력해주세요.';
	} else if (formData.content.trim().length > 1000) {
		errors.content = '내용은 1000자 이내로 입력해주세요.';
	}

	// 대상 검증
	if (formData.target_type === 'group' && (!formData.target_ids || formData.target_ids.length === 0)) {
		errors.target_ids = '그룹을 선택해주세요.';
	}

	if (formData.target_type === 'individual' && (!formData.target_ids || formData.target_ids.length === 0)) {
		errors.target_ids = '직원을 선택해주세요.';
	}

	// URL 검증 (선택사항)
	if (formData.action_url && formData.action_url.trim()) {
		try {
			const url = new URL(formData.action_url);
			if (!['http:', 'https:'].includes(url.protocol)) {
				errors.action_url = 'HTTP 또는 HTTPS URL만 허용됩니다.';
			}
		} catch {
			errors.action_url = '올바른 URL 형식을 입력해주세요. (예: https://example.com)';
		}
	}

	return {
		isValid: Object.keys(errors).length === 0,
		errors
	};
}

/**
 * 알림 취소 가능 여부 확인
 */
export function canCancel(notification: Notification): boolean {
	return notification.status === 'sent' && !notification.sent_at;
}

/**
 * 읽음률 계산
 */
export function calculateReadRate(notification: Notification): number {
	if (!notification.recipients_count || notification.recipients_count === 0) {
		return 0;
	}

	const readCount = notification.read_count || 0;
	return Math.round((readCount / notification.recipients_count) * 100);
}

/**
 * 전송 완료 여부 확인
 */
export function isDelivered(notification: Notification): boolean {
	return notification.status === 'sent' && !!notification.sent_at;
}

/**
 * 알림 우선순위 숫자 변환 (정렬용)
 */
export function getPriorityWeight(priority: string): number {
	const weights = {
		low: 1,
		normal: 2,
		high: 3,
		urgent: 4
	};
	return weights[priority as keyof typeof weights] || 0;
}

/**
 * 알림 내용 미리보기 생성
 */
export function getPreview(content: string, maxLength: number = 100): string {
	if (!content) return '';

	const cleanContent = content.replace(/<[^>]*>/g, '').trim();
	if (cleanContent.length <= maxLength) {
		return cleanContent;
	}

	return cleanContent.substring(0, maxLength) + '...';
}

/**
 * 전송 예상 시간 계산 (수신자 수 기반)
 */
export function estimateDeliveryTime(recipientCount: number): string {
	if (recipientCount <= 10) {
		return '즉시';
	} else if (recipientCount <= 50) {
		return '1-2초';
	} else if (recipientCount <= 100) {
		return '2-3초';
	} else {
		return '3초 이상';
	}
}
