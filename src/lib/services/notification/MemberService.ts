import type { User } from '$lib/User';
import type { Member } from '$lib/types/types';
import { authClient } from '$lib/services/AxiosBackend';
import { API_ENDPOINTS, ERROR_CODES, ERROR_MESSAGES } from '$lib/constants/notification';
import { MEMBER_STATUS_ACTIVE } from '$lib/stores/memberStore';

/**
 * 직원 관리 서비스 클래스 (알림 시스템용)
 */
export class MemberService {
	/**
	 * 활성 직원 목록 조회
	 */
	static async getActiveMembers(user: User): Promise<Member[]> {
		try {
			const params = {
				userId: user.id,
				keyword: '',
				status: MEMBER_STATUS_ACTIVE
			};

			const { status, data } = await authClient.get(API_ENDPOINTS.ACTIVE_MEMBERS, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data.items as Member[];
			} else {
				throw new MemberServiceError(
					data?.message || ERROR_MESSAGES.NETWORK_ERROR,
					ERROR_CODES.NETWORK_ERROR,
					data
				);
			}
		} catch (error) {
			if (error instanceof MemberServiceError) {
				throw error;
			}
			throw new MemberServiceError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
		}
	}

	/**
	 * 직원 검색
	 */
	static async searchMembers(user: User, query: string): Promise<Member[]> {
		try {
			const params = {
				userId: user.id,
				search: query,
				status: MEMBER_STATUS_ACTIVE
			};

			const { status, data } = await authClient.get(API_ENDPOINTS.MEMBERS, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data as Member[];
			} else {
				throw new MemberServiceError(
					data?.message || ERROR_MESSAGES.NETWORK_ERROR,
					ERROR_CODES.NETWORK_ERROR,
					data
				);
			}
		} catch (error) {
			if (error instanceof MemberServiceError) {
				throw error;
			}
			throw new MemberServiceError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
		}
	}

	/**
	 * 특정 직원 조회
	 */
	static async getMemberById(user: User, memberId: number): Promise<Member> {
		try {
			const params = {
				userId: user.id
			};

			const { status, data } = await authClient.get(`${API_ENDPOINTS.MEMBERS}/${memberId}`, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data as Member;
			} else {
				throw new MemberServiceError(
					data?.message || '직원 정보를 찾을 수 없습니다.',
					ERROR_CODES.NETWORK_ERROR,
					data
				);
			}
		} catch (error) {
			if (error instanceof MemberServiceError) {
				throw error;
			}
			throw new MemberServiceError(ERROR_MESSAGES.NETWORK_ERROR, ERROR_CODES.NETWORK_ERROR, error);
		}
	}

	/**
	 * 온라인 직원 수 조회
	 */
	static async getOnlineCount(user: User): Promise<number> {
		try {
			const params = {
				userId: user.id,
				count_only: true,
				online_only: true
			};

			const { status, data } = await authClient.get(API_ENDPOINTS.MEMBERS, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data?.count || 0;
			} else {
				return 0; // 실패 시 0 반환
			}
		} catch (error) {
			console.error('온라인 직원 수 조회 실패:', error);
			return 0;
		}
	}

	/**
	 * 직원 그룹 소속 정보 조회
	 */
	static async getMemberGroups(user: User, memberId: number): Promise<any[]> {
		try {
			const params = {
				userId: user.id
			};

			const { status, data } = await authClient.get(`${API_ENDPOINTS.MEMBERS}/${memberId}/groups`, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data as any[];
			} else {
				return [];
			}
		} catch (error) {
			console.error('직원 그룹 정보 조회 실패:', error);
			return [];
		}
	}

	/**
	 * 그룹 멤버 목록 조회
	 */
	static async getGroupMembers(user: User, groupId: number): Promise<Member[]> {
		try {
			const params = {
				userId: user.id
			};

			const { status, data } = await authClient.get(`${API_ENDPOINTS.GROUPS}/${groupId}/members`, {
				params
			});

			if (status === 200 && data?.success) {
				return data.data as Member[];
			} else {
				// API 실패 시 더미 데이터 반환 (개발 중에만)
				console.warn('그룹 멤버 API 실패, 더미 데이터 사용');
				return [
					{
						id: 1,
						name: '홍길동',
						username: 'hong',
						email: '<EMAIL>',
						part: '개발팀',
						position: '개발자',
						status: 1,
						role: 'Employee',
						login_at: new Date().toISOString()
					},
					{
						id: 2,
						name: '김철수',
						username: 'kim',
						email: '<EMAIL>',
						part: '개발팀',
						position: '시니어 개발자',
						status: 1,
						role: 'Employee',
						login_at: new Date().toISOString()
					}
				] as Member[];
			}
		} catch (error) {
			console.error('그룹 멤버 조회 실패:', error);
			// 에러 시에도 더미 데이터 반환
			return [
				{
					id: 1,
					name: '홍길동',
					username: 'hong',
					email: '<EMAIL>',
					part: '개발팀',
					position: '개발자',
					status: 1,
					role: 'Employee',
					login_at: new Date().toISOString()
				}
			] as Member[];
		}
	}
}

/**
 * 직원 서비스 관련 커스텀 에러 클래스
 */
export class MemberServiceError extends Error {
	constructor(
		message: string,
		public code: string,
		public details?: any
	) {
		super(message);
		this.name = 'MemberServiceError';
	}
}

/**
 * 직원 관련 유틸리티
 */
export class MemberUtils {
	/**
	 * 직원 표시명 생성
	 */
	static getDisplayName(member: Member): string {
		if (member.name && member.username) {
			return `${member.name} (${member.username})`;
		}
		return member.name || member.username || '이름 없음';
	}

	/**
	 * 직원 상태 확인
	 */
	static isActive(member: Member): boolean {
		return member.status === MEMBER_STATUS_ACTIVE;
	}

	/**
	 * 최근 로그인 여부 확인 (24시간 이내)
	 */
	static isRecentlyActive(member: Member): boolean {
		if (!member.login_at) return false;

		const loginTime = new Date(member.login_at);
		const now = new Date();
		const diffHours = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60);

		return diffHours <= 24;
	}

	/**
	 * 직원 검색 필터링
	 */
	static filterMembers(members: Member[], query: string): Member[] {
		if (!query.trim()) return members;

		const searchTerm = query.toLowerCase().trim();

		return members.filter(
			(member) =>
				member.name?.toLowerCase().includes(searchTerm) ||
				member.username?.toLowerCase().includes(searchTerm) ||
				member.email?.toLowerCase().includes(searchTerm) ||
				member.part?.toLowerCase().includes(searchTerm) ||
				member.position?.toLowerCase().includes(searchTerm)
		);
	}

	/**
	 * 직원 목록 정렬
	 */
	static sortMembers(
		members: Member[],
		sortBy: 'name' | 'username' | 'login_at' = 'name'
	): Member[] {
		return [...members].sort((a, b) => {
			switch (sortBy) {
				case 'name':
					return (a.name || '').localeCompare(b.name || '');
				case 'username':
					return (a.username || '').localeCompare(b.username || '');
				case 'login_at': {
					const aTime = a.login_at ? new Date(a.login_at).getTime() : 0;
					const bTime = b.login_at ? new Date(b.login_at).getTime() : 0;
					return bTime - aTime;
				} // 최근 로그인 순
				default:
					return 0;
			}
		});
	}

	/**
	 * 직원 역할별 그룹화
	 */
	static groupByRole(members: Member[]): Record<string, Member[]> {
		return members.reduce(
			(groups, member) => {
				const role = member.role || '기타';
				if (!groups[role]) {
					groups[role] = [];
				}
				groups[role].push(member);
				return groups;
			},
			{} as Record<string, Member[]>
		);
	}

	/**
	 * 직원 부서별 그룹화
	 */
	static groupByPart(members: Member[]): Record<string, Member[]> {
		return members.reduce(
			(groups, member) => {
				const part = member.part || '미지정';
				if (!groups[part]) {
					groups[part] = [];
				}
				groups[part].push(member);
				return groups;
			},
			{} as Record<string, Member[]>
		);
	}
}
