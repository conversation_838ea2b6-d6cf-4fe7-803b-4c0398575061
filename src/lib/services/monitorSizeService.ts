import { authClient } from '$lib/services/AxiosBackend';
import type { MonitorBrand, MonitorSizeItem, MonitorSizeSearchParams, MonitorSizeSearchResponse, MonitorUnit } from '$lib/types/monitorTypes';

// 서버 표준 성공 래퍼 타입
interface ApiSuccess<T> {
  success: true;
  message: string;
  data: T;
}

// 서버 표준 에러 타입
export interface ApiErrorResponse {
  success: false;
  message: string;
  error_code?: string;
  errors?: Record<string, string[]>;
}

const BASE = '/wms/settings/repairs/monitor-sizes';

// 목록 조회
export async function fetchMonitorSizes(params: MonitorSizeSearchParams): Promise<MonitorSizeSearchResponse> {
  const { data } = await authClient.get<ApiSuccess<MonitorSizeSearchResponse>>(BASE, { params });
  return data.data;
}

// 항목 수정
export async function updateMonitorSizeItem(id: number, payload: { brand: MonitorBrand; size: number; unit: MonitorUnit; }): Promise<MonitorSizeItem> {
  const { data } = await authClient.put<ApiSuccess<{ item: MonitorSizeItem }>>(`${BASE}/${id}`, payload);
  return data.data.item;
}


