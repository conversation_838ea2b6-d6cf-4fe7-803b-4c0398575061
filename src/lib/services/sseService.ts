/**
 * SSE(Server-Sent Events) 연결 서비스
 *
 * 기존 Pusher 기반 알림 시스템을 SSE로 전환하기 위한 서비스입니다.
 * 함수형 프로그래밍 패턴을 사용하여 구현되었으며, 기존 authClient 패턴을 참고했습니다.
 */

import { browser } from '$app/environment';
import { getUser, isUser } from '$lib/User';
import type {
	SSENotification,
	SSEConnectionConfig,
	SSEConnectionState,
	SSEError,
	SSEEventListeners,
	SSEEventHandler
} from '$lib/types/sse';
import { SSEConnectionStatus as ConnectionStatus, SSEErrorType as ErrorType } from '$lib/types/sse';

// ============================================================================
// 전역 상태 관리
// ============================================================================

let eventSource: EventSource | null = null;
let connectionState: SSEConnectionState = {
	isConnected: false,
	reconnectAttempts: 0,
	status: ConnectionStatus.DISCONNECTED
};
let config: SSEConnectionConfig;
let eventListeners: SSEEventListeners = {};
let reconnectTimer: number | null = null;
let heartbeatTimer: number | null = null;

// ============================================================================
// 설정 및 초기화
// ============================================================================

/**
 * SSE 연결 설정을 초기화합니다.
 */
function initializeConfig(): SSEConnectionConfig {
	const baseUrl = import.meta.env.VITE_HOME_URL as string;
	const sseEndpoint = import.meta.env.VITE_SSE_NOTIFICATION_ENDPOINT as string;

	return {
		url: sseEndpoint || `${baseUrl}/api/sse/notifications`,
		reconnectDelay: 1000, // 1초
		maxReconnectAttempts: 5,
		heartbeatInterval: 30000, // 30초
		connectionTimeout: 10000 // 10초
	};
}

/**
 * SSE 서비스를 초기화합니다.
 */
function initialize(): void {
	if (!browser) return;

	config = initializeConfig();

	// 브라우저 이벤트 리스너 등록
	window.addEventListener('beforeunload', cleanup);
	window.addEventListener('online', handleNetworkOnline);
	window.addEventListener('offline', handleNetworkOffline);
}

// ============================================================================
// 연결 관리 함수들
// ============================================================================

/**
 * SSE 연결을 시작합니다.
 */
async function connect(): Promise<void> {
	if (!browser) {
		throw createError(ErrorType.CONNECTION_FAILED, 'Browser environment required');
	}

	// 사용자 인증 확인
	if (!isUser()) {
		throw createError(ErrorType.AUTHENTICATION_FAILED, 'User not authenticated');
	}

	// 기존 연결이 있으면 해제
	if (eventSource) {
		disconnect();
	}

	try {
		updateConnectionState({
			status: ConnectionStatus.CONNECTING,
			isConnected: false
		});

		// EventSource 생성
		eventSource = new EventSource(config.url, {
			withCredentials: true
		});

		// 이벤트 리스너 등록
		setupEventListeners();

		// 연결 타임아웃 설정
		const timeoutId = setTimeout(() => {
			if (connectionState.status === ConnectionStatus.CONNECTING) {
				handleConnectionError(createError(ErrorType.TIMEOUT, 'Connection timeout'));
			}
		}, config.connectionTimeout);

		// 연결 성공 시 타임아웃 해제
		eventSource.addEventListener('open', () => {
			clearTimeout(timeoutId);
		});
	} catch (error) {
		handleConnectionError(
			createError(ErrorType.CONNECTION_FAILED, 'Failed to create EventSource', error as Error)
		);
	}
}

/**
 * SSE 연결을 해제합니다.
 */
function disconnect(): void {
	// 타이머들 정리
	clearReconnectTimer();
	clearHeartbeatTimer();

	// EventSource 해제
	if (eventSource) {
		eventSource.close();
		eventSource = null;
	}

	updateConnectionState({
		status: ConnectionStatus.DISCONNECTED,
		isConnected: false,
		connectionId: undefined,
		lastHeartbeat: undefined,
		connectedAt: undefined
	});

	// 연결 해제 이벤트 발생
	if (eventListeners.onDisconnect) {
		eventListeners.onDisconnect();
	}
}

/**
 * SSE 연결을 재시도합니다.
 */
async function reconnect(): Promise<void> {
	if (connectionState.reconnectAttempts >= config.maxReconnectAttempts) {
		const error = createError(
			ErrorType.CONNECTION_FAILED,
			`Maximum reconnection attempts (${config.maxReconnectAttempts}) exceeded`
		);
		handleConnectionError(error);
		return;
	}

	updateConnectionState({
		status: ConnectionStatus.RECONNECTING,
		reconnectAttempts: connectionState.reconnectAttempts + 1
	});

	// 재연결 시도 이벤트 발생
	if (eventListeners.onReconnectAttempt) {
		eventListeners.onReconnectAttempt({
			attempt: connectionState.reconnectAttempts,
			maxAttempts: config.maxReconnectAttempts
		});
	}

	// 지수 백오프로 재연결 지연
	const delay = Math.min(
		config.reconnectDelay * Math.pow(2, connectionState.reconnectAttempts - 1),
		30000
	);

	reconnectTimer = window.setTimeout(async () => {
		try {
			await connect();
		} catch (error) {
			console.error('Reconnection failed:', error);
			// 재연결 실패 시 다시 재시도
			await reconnect();
		}
	}, delay);
}

// ============================================================================
// 이벤트 처리 함수들
// ============================================================================

/**
 * EventSource 이벤트 리스너를 설정합니다.
 */
function setupEventListeners(): void {
	if (!eventSource) return;

	// 연결 성공
	eventSource.addEventListener('open', handleConnectionOpen);

	// 메시지 수신
	eventSource.addEventListener('message', handleMessage);

	// 연결 오류
	eventSource.addEventListener('error', handleConnectionError);

	// 하트비트 이벤트 (서버에서 전송하는 경우)
	eventSource.addEventListener('heartbeat', handleHeartbeat);

	// 알림 이벤트 (커스텀 이벤트 타입)
	eventSource.addEventListener('notification', handleNotificationEvent);
}

/**
 * 연결 성공을 처리합니다.
 */
function handleConnectionOpen(): void {
	const now = new Date().toISOString();

	updateConnectionState({
		status: ConnectionStatus.CONNECTED,
		isConnected: true,
		reconnectAttempts: 0,
		lastError: undefined,
		connectionId: generateConnectionId(),
		connectedAt: now,
		lastHeartbeat: now
	});

	// 하트비트 타이머 시작
	startHeartbeatTimer();

	// 연결 성공 이벤트 발생
	if (eventListeners.onConnect) {
		eventListeners.onConnect();
	}

	if (eventListeners.onConnectionChange) {
		eventListeners.onConnectionChange(connectionState);
	}
}

/**
 * 메시지를 처리합니다.
 */
function handleMessage(event: MessageEvent): void {
	try {
		const data = JSON.parse(event.data);

		// 하트비트 메시지 처리
		if (data.type === 'heartbeat') {
			handleHeartbeat();
			return;
		}

		// 알림 메시지 처리
		if (data.type === 'notification') {
			const notification = parseNotification(data);
			if (notification && isValidNotificationForUser(notification)) {
				processNotification(notification);
			}
		}
	} catch (error) {
		const sseError = createError(
			ErrorType.PARSE_ERROR,
			'Failed to parse SSE message',
			error as Error
		);

		if (eventListeners.onError) {
			eventListeners.onError(sseError);
		}
	}
}

/**
 * 알림 이벤트를 처리합니다 (커스텀 이벤트 타입).
 */
function handleNotificationEvent(event: MessageEvent): void {
	try {
		const notification = parseNotification(JSON.parse(event.data));
		if (notification && isValidNotificationForUser(notification)) {
			processNotification(notification);
		}
	} catch (error) {
		const sseError = createError(
			ErrorType.PARSE_ERROR,
			'Failed to parse notification event',
			error as Error
		);

		if (eventListeners.onError) {
			eventListeners.onError(sseError);
		}
	}
}

/**
 * 연결 오류를 처리합니다.
 */
function handleConnectionError(error?: Event | SSEError): void {
	let sseError: SSEError;

	if (error && 'type' in error && 'message' in error) {
		// 이미 SSEError 객체인 경우
		sseError = error as SSEError;
	} else {
		// Event 객체이거나 다른 오류인 경우
		sseError = createError(ErrorType.CONNECTION_FAILED, 'SSE connection error occurred');
	}

	updateConnectionState({
		status: ConnectionStatus.FAILED,
		isConnected: false,
		lastError: sseError.message
	});

	// 에러 이벤트 발생
	if (eventListeners.onError) {
		eventListeners.onError(sseError);
	}

	if (eventListeners.onConnectionChange) {
		eventListeners.onConnectionChange(connectionState);
	}

	// 재연결 시도 (인증 오류가 아닌 경우)
	if (sseError.type !== ErrorType.AUTHENTICATION_FAILED && sseError.retryable) {
		reconnect();
	}
}

/**
 * 하트비트를 처리합니다.
 */
function handleHeartbeat(): void {
	const now = new Date().toISOString();

	updateConnectionState({
		lastHeartbeat: now
	});

	if (eventListeners.onHeartbeat) {
		eventListeners.onHeartbeat(now);
	}
}

/**
 * 네트워크 온라인 상태를 처리합니다.
 */
function handleNetworkOnline(): void {
	if (!connectionState.isConnected && connectionState.status !== ConnectionStatus.CONNECTING) {
		connect().catch((error) => {
			console.error('Failed to reconnect after network online:', error);
		});
	}
}

/**
 * 네트워크 오프라인 상태를 처리합니다.
 */
function handleNetworkOffline(): void {
	if (connectionState.isConnected) {
		updateConnectionState({
			status: ConnectionStatus.DISCONNECTED,
			isConnected: false,
			lastError: 'Network offline'
		});

		if (eventListeners.onConnectionChange) {
			eventListeners.onConnectionChange(connectionState);
		}
	}
}

// ============================================================================
// 알림 처리 함수들
// ============================================================================

/**
 * 수신된 데이터를 SSENotification 객체로 파싱합니다.
 */
function parseNotification(data: any): SSENotification | null {
	try {
		// 필수 필드 검증
		if (!data.id || !data.type || !data.message) {
			return null;
		}

		// 개행 문자 처리 (기존 Pusher 코드와 호환)
		const message = data.message.replace(/\\n/g, '\n').replace(/<br>/g, '\n');

		const notification: SSENotification = {
			id: data.id,
			type: 'notification',
			priority: data.priority || 'normal',
			title: data.title || '',
			message: message,
			redirect: data.redirect,
			timestamp: data.timestamp || new Date().toISOString(),
			user_id: data.user_id,
			success: data.success !== false,
			// 클라이언트 상태
			isRead: false,
			receivedAt: new Date().toISOString()
		};

		return notification;
	} catch (error) {
		console.error('Failed to parse notification:', error);
		return null;
	}
}

/**
 * 알림이 현재 사용자에게 유효한지 확인합니다.
 */
function isValidNotificationForUser(notification: SSENotification): boolean {
	try {
		// 전체 알림인 경우 (user_id가 없는 경우)
		if (!notification.user_id) {
			return true;
		}

		// 사용자별 알림인 경우 현재 사용자 ID와 비교
		const currentUser = getUser();
		return currentUser.id === notification.user_id;
	} catch (error) {
		console.error('Failed to check user validity for notification:', error);
		// 사용자 정보를 가져올 수 없는 경우 false 반환
		return false;
	}
}

/**
 * 알림을 처리하고 이벤트를 발생시킵니다.
 */
function processNotification(notification: SSENotification): void {
	if (eventListeners.onNotification) {
		eventListeners.onNotification(notification);
	}
}

// ============================================================================
// 유틸리티 함수들
// ============================================================================

/**
 * SSE 에러 객체를 생성합니다.
 */
function createError(type: SSEErrorType, message: string, originalError?: Error): SSEError {
	return {
		type,
		message,
		originalError,
		timestamp: new Date().toISOString(),
		retryable: type !== ErrorType.AUTHENTICATION_FAILED
	};
}

/**
 * 연결 상태를 업데이트합니다.
 */
function updateConnectionState(updates: Partial<SSEConnectionState>): void {
	connectionState = { ...connectionState, ...updates };
}

/**
 * 고유한 연결 ID를 생성합니다.
 */
function generateConnectionId(): string {
	return `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 재연결 타이머를 정리합니다.
 */
function clearReconnectTimer(): void {
	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
		reconnectTimer = null;
	}
}

/**
 * 하트비트 타이머를 시작합니다.
 */
function startHeartbeatTimer(): void {
	clearHeartbeatTimer();

	heartbeatTimer = window.setInterval(() => {
		// 마지막 하트비트로부터 일정 시간이 지나면 연결 문제로 간주
		if (connectionState.lastHeartbeat) {
			const lastHeartbeat = new Date(connectionState.lastHeartbeat);
			const now = new Date();
			const timeDiff = now.getTime() - lastHeartbeat.getTime();

			// 하트비트 간격의 2배 이상 지나면 연결 문제로 간주
			if (timeDiff > config.heartbeatInterval * 2) {
				handleConnectionError(
					createError(ErrorType.TIMEOUT, 'Heartbeat timeout - connection may be lost')
				);
			}
		}
	}, config.heartbeatInterval);
}

/**
 * 하트비트 타이머를 정리합니다.
 */
function clearHeartbeatTimer(): void {
	if (heartbeatTimer) {
		clearInterval(heartbeatTimer);
		heartbeatTimer = null;
	}
}

/**
 * 모든 리소스를 정리합니다.
 */
function cleanup(): void {
	disconnect();

	if (browser) {
		window.removeEventListener('beforeunload', cleanup);
		window.removeEventListener('online', handleNetworkOnline);
		window.removeEventListener('offline', handleNetworkOffline);
	}
}

// ============================================================================
// 공개 API
// ============================================================================

/**
 * SSE 서비스 객체
 * 기존 authClient 패턴을 참고한 함수형 서비스 구조
 */
export const sseService = {
	/**
	 * SSE 연결을 시작합니다.
	 */
	connect,

	/**
	 * SSE 연결을 해제합니다.
	 */
	disconnect,

	/**
	 * SSE 연결을 재시도합니다.
	 */
	reconnect,

	/**
	 * 현재 연결 상태를 반환합니다.
	 */
	getConnectionState: (): SSEConnectionState => ({ ...connectionState }),

	/**
	 * 연결 여부를 확인합니다.
	 */
	isConnected: (): boolean => connectionState.isConnected,

	/**
	 * 이벤트 리스너를 등록합니다.
	 */
	addEventListener: <T = any>(
		eventType: keyof SSEEventListeners,
		handler: SSEEventHandler<T>
	): void => {
		eventListeners[eventType] = handler;
	},

	/**
	 * 이벤트 리스너를 제거합니다.
	 */
	removeEventListener: (eventType: keyof SSEEventListeners): void => {
		delete eventListeners[eventType];
	},

	/**
	 * 모든 이벤트 리스너를 제거합니다.
	 */
	removeAllEventListeners: (): void => {
		eventListeners = {};
	},

	/**
	 * 서비스를 초기화합니다.
	 */
	initialize,

	/**
	 * 서비스를 정리합니다.
	 */
	cleanup
};

// 개별 함수로도 export (선택적 사용)
export { connect as connectSSE, disconnect as disconnectSSE, reconnect as reconnectSSE };

// 브라우저 환경에서 자동 초기화
if (browser) {
	initialize();
}
