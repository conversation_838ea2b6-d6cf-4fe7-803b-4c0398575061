import { describe, it, expect, vi, beforeEach } from 'vitest';

// authClient 모킹
vi.mock('../AxiosBackend', () => ({
	authClient: {
		delete: vi.fn()
	}
}));

// Functions 모킹
vi.mock('$lib/Functions', () => ({
	handleCatch: vi.fn(),
	executeMessage: vi.fn()
}));

import { deleteNotificationGroup } from '../NotificationService';
import { authClient } from '../AxiosBackend';

describe('NotificationService - 그룹 삭제', () => {
	const mockAuthClient = vi.mocked(authClient);

	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('그룹 삭제 성공', async () => {
		// 성공 응답 모킹
		mockAuthClient.delete.mockResolvedValueOnce({
			status: 200,
			data: {
				success: true,
				message: '그룹이 삭제되었습니다.'
			}
		});

		const result = await deleteNotificationGroup(1);

		expect(mockAuthClient.delete).toHaveBeenCalledWith('/api/admin/notification-groups/1');
		expect(result).toEqual({
			success: true,
			message: '그룹이 삭제되었습니다.'
		});
	});

	it('그룹 삭제 실패', async () => {
		// 실패 응답 모킹
		mockAuthClient.delete.mockResolvedValueOnce({
			status: 400,
			data: {
				success: false,
				message: '그룹을 삭제할 수 없습니다.'
			}
		});

		await expect(deleteNotificationGroup(1)).rejects.toThrow('그룹을 삭제할 수 없습니다.');
	});

	it('네트워크 오류 처리', async () => {
		// 네트워크 오류 모킹
		mockAuthClient.delete.mockRejectedValueOnce(new Error('네트워크 연결 실패'));

		await expect(deleteNotificationGroup(1)).rejects.toThrow('네트워크 연결 실패');
	});
});
