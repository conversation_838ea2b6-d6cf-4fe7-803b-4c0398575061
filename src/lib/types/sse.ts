/**
 * SSE(Server-Sent Events) 실시간 알림 시스템 타입 정의
 *
 * 이 파일은 SSE 기반 실시간 알림 시스템에서 사용되는 모든 타입과 인터페이스를 정의합니다.
 * 기존 Pusher 시스템을 대체하며, 읽음 확인 기능을 포함합니다.
 */

// ============================================================================
// 기본 SSE 알림 인터페이스
// ============================================================================

/**
 * SSE를 통해 수신되는 알림 메시지 인터페이스
 * 서버에서 전송되는 실시간 알림의 기본 구조를 정의합니다.
 */
export interface SSENotification {
	/** 알림 고유 식별자 */
	id: string;

	/** 메시지 타입 (항상 'notification') */
	type: 'notification';

	/** 알림 우선순위 */
	priority: NotificationPriority;

	/** 알림 제목 */
	title: string;

	/** 알림 내용 (개행 문자 포함 가능) */
	message: string;

	/** 리다이렉트 URL (선택사항) */
	redirect?: string;

	/** 서버 전송 시간 (ISO 8601 형식) */
	timestamp: string;

	/** 대상 사용자 ID (선택사항, 전체 알림의 경우 없음) */
	user_id?: number;

	/** 서버 처리 성공 여부 */
	success: boolean;

	// 클라이언트 측에서 관리하는 상태
	/** 읽음 상태 (클라이언트에서 관리) */
	isRead?: boolean;

	/** 클라이언트 수신 시간 (ISO 8601 형식) */
	receivedAt?: string;
}

/**
 * 알림 우선순위 타입
 * - low: 조용한 알림 (목록에만 추가)
 * - normal: 일반 토스트 알림
 * - high: 눈에 띄는 확인 요구 알림
 * - urgent: 강제 모달 표시 (무시 불가)
 */
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

// ============================================================================
// SSE 연결 관리 인터페이스
// ============================================================================

/**
 * SSE 연결 설정 인터페이스
 */
export interface SSEConnectionConfig {
	/** SSE 엔드포인트 URL */
	url: string;

	/** 재연결 시도 간격 (밀리초) */
	reconnectDelay: number;

	/** 최대 재연결 시도 횟수 */
	maxReconnectAttempts: number;

	/** 하트비트 간격 (밀리초) */
	heartbeatInterval: number;

	/** 연결 타임아웃 (밀리초) */
	connectionTimeout: number;
}

/**
 * SSE 연결 상태 인터페이스
 */
export interface SSEConnectionState {
	/** 현재 연결 상태 */
	isConnected: boolean;

	/** 현재 재연결 시도 횟수 */
	reconnectAttempts: number;

	/** 마지막 오류 메시지 */
	lastError?: string;

	/** 연결 고유 식별자 */
	connectionId?: string;

	/** 마지막 하트비트 시간 (ISO 8601 형식) */
	lastHeartbeat?: string;

	/** 연결 시작 시간 (ISO 8601 형식) */
	connectedAt?: string;

	/** 연결 상태 (세부) */
	status: SSEConnectionStatus;
}

/**
 * SSE 연결 상태 열거형
 */
export enum SSEConnectionStatus {
	/** 연결 해제됨 */
	DISCONNECTED = 'DISCONNECTED',

	/** 연결 중 */
	CONNECTING = 'CONNECTING',

	/** 연결됨 */
	CONNECTED = 'CONNECTED',

	/** 재연결 중 */
	RECONNECTING = 'RECONNECTING',

	/** 연결 실패 */
	FAILED = 'FAILED',

	/** 인증 실패 */
	AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED'
}

// ============================================================================
// 읽음 확인 관련 인터페이스
// ============================================================================

/**
 * 읽음 확인 요청 인터페이스
 */
export interface ReadReceiptRequest {
	/** 알림 ID */
	notification_id: string;

	/** 읽음 시간 (ISO 8601 형식) */
	read_at: string;

	/** 사용자 ID */
	user_id: number;
}

/**
 * 읽음 확인 응답 인터페이스
 */
export interface ReadReceiptResponse {
	/** 처리 성공 여부 */
	success: boolean;

	/** 응답 메시지 */
	message?: string;

	/** 오류 코드 (실패 시) */
	error_code?: string;

	/** 처리된 알림 ID */
	notification_id?: string;
}

/**
 * 배치 읽음 확인 요청 인터페이스
 */
export interface BatchReadReceiptRequest {
	/** 읽음 확인 요청 목록 */
	receipts: ReadReceiptRequest[];

	/** 배치 처리 ID */
	batch_id: string;
}

/**
 * 배치 읽음 확인 응답 인터페이스
 */
export interface BatchReadReceiptResponse {
	/** 전체 처리 성공 여부 */
	success: boolean;

	/** 성공한 항목 수 */
	success_count: number;

	/** 실패한 항목 수 */
	failure_count: number;

	/** 실패한 항목들의 상세 정보 */
	failures?: Array<{
		notification_id: string;
		error_message: string;
		error_code?: string;
	}>;

	/** 배치 처리 ID */
	batch_id: string;
}

// ============================================================================
// 에러 처리 관련 인터페이스
// ============================================================================

/**
 * SSE 에러 타입 열거형
 */
export enum SSEErrorType {
	/** 연결 실패 */
	CONNECTION_FAILED = 'CONNECTION_FAILED',

	/** 인증 실패 */
	AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',

	/** 네트워크 오류 */
	NETWORK_ERROR = 'NETWORK_ERROR',

	/** 메시지 파싱 오류 */
	PARSE_ERROR = 'PARSE_ERROR',

	/** 읽음 확인 전송 실패 */
	READ_RECEIPT_FAILED = 'READ_RECEIPT_FAILED',

	/** 서버 오류 */
	SERVER_ERROR = 'SERVER_ERROR',

	/** 타임아웃 */
	TIMEOUT = 'TIMEOUT',

	/** 알 수 없는 오류 */
	UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * SSE 에러 인터페이스
 */
export interface SSEError {
	/** 에러 타입 */
	type: SSEErrorType;

	/** 에러 메시지 */
	message: string;

	/** 원본 에러 객체 */
	originalError?: Error;

	/** 에러 발생 시간 (ISO 8601 형식) */
	timestamp: string;

	/** 재시도 가능 여부 */
	retryable: boolean;

	/** 에러 코드 (서버에서 제공하는 경우) */
	errorCode?: string;

	/** 추가 컨텍스트 정보 */
	context?: Record<string, any>;
}

// ============================================================================
// 스토어 상태 관리 인터페이스
// ============================================================================

/**
 * SSE 알림 스토어 상태 인터페이스
 */
export interface SSENotificationState {
	/** 수신된 알림 목록 */
	notifications: SSENotification[];

	/** 미읽음 알림 개수 */
	unreadCount: number;

	/** SSE 연결 상태 */
	connectionState: SSEConnectionState;

	/** 읽음 확인 처리 중인 알림 ID 목록 */
	pendingReadReceipts: Set<string>;

	/** 오프라인 상태에서 쌓인 읽음 확인 큐 */
	offlineReadReceiptQueue: ReadReceiptRequest[];

	/** 마지막 동기화 시간 */
	lastSyncTime?: string;

	/** 스토어 로딩 상태 */
	isLoading: boolean;

	/** 스토어 에러 상태 */
	error?: SSEError;
}

/**
 * 알림 필터 옵션 인터페이스
 */
export interface NotificationFilter {
	/** 우선순위 필터 */
	priority?: NotificationPriority[];

	/** 읽음 상태 필터 */
	readStatus?: 'read' | 'unread' | 'all';

	/** 날짜 범위 필터 (시작) */
	dateFrom?: string;

	/** 날짜 범위 필터 (끝) */
	dateTo?: string;

	/** 검색 키워드 */
	searchKeyword?: string;

	/** 최대 표시 개수 */
	limit?: number;
}

// ============================================================================
// 이벤트 핸들러 관련 인터페이스
// ============================================================================

/**
 * SSE 이벤트 핸들러 타입 정의
 */
export type SSEEventHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * SSE 이벤트 리스너 인터페이스
 */
export interface SSEEventListeners {
	/** 알림 수신 이벤트 */
	onNotification?: SSEEventHandler<SSENotification>;

	/** 연결 상태 변경 이벤트 */
	onConnectionChange?: SSEEventHandler<SSEConnectionState>;

	/** 에러 발생 이벤트 */
	onError?: SSEEventHandler<SSEError>;

	/** 연결 성공 이벤트 */
	onConnect?: SSEEventHandler<void>;

	/** 연결 해제 이벤트 */
	onDisconnect?: SSEEventHandler<void>;

	/** 재연결 시도 이벤트 */
	onReconnectAttempt?: SSEEventHandler<{ attempt: number; maxAttempts: number }>;

	/** 하트비트 이벤트 */
	onHeartbeat?: SSEEventHandler<string>;
}

// ============================================================================
// 알림 처리 관련 인터페이스
// ============================================================================

/**
 * 알림 액션 타입
 */
export type NotificationAction = 'dismiss' | 'redirect' | 'markAsRead' | 'showDetails';

/**
 * 알림 액션 결과 인터페이스
 */
export interface NotificationActionResult {
	/** 액션 성공 여부 */
	success: boolean;

	/** 결과 메시지 */
	message?: string;

	/** 추가 데이터 */
	data?: any;
}

/**
 * 우선순위별 알림 처리 설정 인터페이스
 */
export interface NotificationHandlerConfig {
	/** urgent 우선순위 처리 함수 */
	handleUrgent: (notification: SSENotification) => Promise<NotificationActionResult>;

	/** high 우선순위 처리 함수 */
	handleHigh: (notification: SSENotification) => Promise<NotificationActionResult>;

	/** normal 우선순위 처리 함수 */
	handleNormal: (notification: SSENotification) => Promise<NotificationActionResult>;

	/** low 우선순위 처리 함수 */
	handleLow: (notification: SSENotification) => Promise<NotificationActionResult>;
}

// ============================================================================
// 유틸리티 타입 및 상수
// ============================================================================

/**
 * 알림 우선순위 옵션 (UI 표시용)
 */
export const SSE_NOTIFICATION_PRIORITIES = [
	{ value: 'low', text: '낮음', color: 'badge-ghost', icon: 'fa-info-circle' },
	{ value: 'normal', text: '보통', color: 'badge-info', icon: 'fa-bell' },
	{ value: 'high', text: '높음', color: 'badge-warning', icon: 'fa-exclamation-triangle' },
	{ value: 'urgent', text: '긴급', color: 'badge-error', icon: 'fa-exclamation-circle' }
] as const;

/**
 * SSE 연결 상태 옵션 (UI 표시용)
 */
export const SSE_CONNECTION_STATUSES = [
	{ value: SSEConnectionStatus.DISCONNECTED, text: '연결 해제', color: 'text-gray-500' },
	{ value: SSEConnectionStatus.CONNECTING, text: '연결 중', color: 'text-blue-500' },
	{ value: SSEConnectionStatus.CONNECTED, text: '연결됨', color: 'text-green-500' },
	{ value: SSEConnectionStatus.RECONNECTING, text: '재연결 중', color: 'text-yellow-500' },
	{ value: SSEConnectionStatus.FAILED, text: '연결 실패', color: 'text-red-500' },
	{ value: SSEConnectionStatus.AUTHENTICATION_FAILED, text: '인증 실패', color: 'text-red-600' }
] as const;

/**
 * 기본 SSE 연결 설정
 */
export const DEFAULT_SSE_CONFIG: SSEConnectionConfig = {
	url: '', // 런타임에 설정됨
	reconnectDelay: 1000, // 1초
	maxReconnectAttempts: 5,
	heartbeatInterval: 30000, // 30초
	connectionTimeout: 10000 // 10초
};

/**
 * 알림 메모리 관리 설정
 */
export const NOTIFICATION_MEMORY_CONFIG = {
	/** 메모리에 유지할 최대 알림 개수 */
	MAX_NOTIFICATIONS: 100,

	/** 자동 정리할 읽은 알림의 최대 보관 기간 (일) */
	MAX_READ_NOTIFICATION_DAYS: 7,

	/** 오프라인 읽음 확인 큐 최대 크기 */
	MAX_OFFLINE_QUEUE_SIZE: 50
} as const;

// ============================================================================
// 유틸리티 함수 타입
// ============================================================================

/**
 * 우선순위 텍스트 가져오기 함수 타입
 */
export type GetPriorityTextFn = (priority: NotificationPriority) => string;

/**
 * 우선순위 색상 가져오기 함수 타입
 */
export type GetPriorityColorFn = (priority: NotificationPriority) => string;

/**
 * 연결 상태 텍스트 가져오기 함수 타입
 */
export type GetConnectionStatusTextFn = (status: SSEConnectionStatus) => string;

/**
 * 메시지 포맷팅 함수 타입
 */
export type FormatMessageFn = (message: string) => string;

/**
 * 시간 포맷팅 함수 타입
 */
export type FormatTimeFn = (timestamp: string) => string;
