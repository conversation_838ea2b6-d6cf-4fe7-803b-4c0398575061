import type { Member, PaginationStore } from './types';

// 그룹 멤버 인터페이스 (서버에서 넘어오는 실제 구조)
export interface GroupMember {
	id: number;
	group_id: number;
	user_id: number;
	created_at: string;
	updated_at: string;
	user: Member;
}

// 알림 그룹 인터페이스
export interface NotificationGroup {
	id: number;
	is_active: boolean;
	name: string;
	description?: string;
	members_count: number;
	created_by: number;
	created_at: string;
	updated_at: string;
	members?: GroupMember[];
}

// 알림 인터페이스
export interface Notification {
	id: string;
	title: string;
	content: string;
	priority: NotificationPriority;
	action_url?: string;
	sender_id: number;
	target_type: NotificationTargetType;
	target_id?: number;
	status: NotificationStatus;
	sent_at?: string;
	created_at: string;
	updated_at: string;
	sender?: Member;
	target_group?: NotificationGroup;
	target_user?: Member;
	recipients_count?: number;
	read_count?: number;
	delivery_rate?: number;
}

// 알림 템플릿 인터페이스
export interface NotificationTemplate {
	id: number;
	name: string;
	title: string;
	content: string;
	priority: NotificationPriority;
	usage_count: number;
	created_by: number;
	created_at: string;
	updated_at: string;
}

// 알림 수신자 인터페이스
export interface NotificationRecipient {
	id: number;
	notification_id: string;
	user_id: number;
	delivered_at?: string;
	read_at?: string;
	user?: Member;
}

// 그룹별 통계 인터페이스
export interface NotificationGroupStats {
	id: number;
	name: string;
	description?: string;
	members_count: number;
	sent_count: number;
	read_count: number;
	read_rate: number;
	last_activity?: string;
}

// 대시보드 통계 인터페이스
export interface NotificationStats {
	today_sent: number;
	weekly_trend: Array<{ date: string; count: number }>;
	priority_read_rates: Array<{ priority: string; read_rate: number }>;
	online_users: number;
	group_stats: NotificationGroupStats[];
}

// API 응답 인터페이스
export interface ApiResponse<T = any> {
	success: boolean;
	message: string;
	data?: T;
	error_code?: string;
	details?: any;
}

// 페이지네이션 API 응답 인터페이스
export interface PaginatedApiResponse<T = any> {
	success: boolean;
	message: string;
	data: {
		items: T[];
		pagination: {
			total: number;
			current_page: number;
			last_page: number;
			per_page: number;
			from: number;
			to: number;
			has_prev: boolean;
			has_next: boolean;
		};
	};
}

// 알림 필터 인터페이스
export interface NotificationFilter {
	search?: string;
	priority?: string;
	status?: string;
	target_type?: string;
	date_from?: string;
	date_to?: string;
}

// 그룹 필터 인터페이스
export interface GroupFilter {
	search?: string;
	created_by?: number;
}

// 템플릿 필터 인터페이스
export interface TemplateFilter {
	search?: string;
	priority?: string;
	created_by?: number;
}

// 알림 스토어 데이터 (기존 NotificationHistoryStoreData를 확장)
export interface NotificationStoreData extends PaginationStore {
	notifications?: Notification[];
	loading?: boolean;
	error?: string | null;
}

// 알림 그룹 스토어 데이터
export interface NotificationGroupStoreData extends PaginationStore {
	groups?: NotificationGroup[];
	loading?: boolean;
	error?: string | null;
}

// 알림 히스토리 스토어 데이터
export interface NotificationHistoryStoreData extends PaginationStore {
	notifications?: Notification[];
	loading?: boolean;
	error?: string | null;
}

// 알림 템플릿 스토어 데이터
export interface NotificationTemplateStoreData extends PaginationStore {
	templates?: NotificationTemplate[];
	loading?: boolean;
	error?: string | null;
}

// 대시보드 스토어 데이터
export interface DashboardStoreData {
	stats?: NotificationStats;
	loading: boolean;
	error?: string;
}

// 그룹 스토어 데이터 (PaginationStore 확장)
export interface GroupStoreData extends PaginationStore {
	groups?: NotificationGroup[];
	loading?: boolean;
	error?: string | null;
}

// 알림 폼 데이터
export interface NotificationFormData {
	title: string;
	content: string;
	priority: NotificationPriority;
	action_url?: string;
	target_type: NotificationTargetType;
	target_ids?: number[];
	template_id?: number;
}

// 그룹 폼 데이터
export interface GroupFormData {
	name: string;
	description?: string;
	is_active: boolean;
	member_ids: number[];
}

// 템플릿 폼 데이터
export interface TemplateFormData {
	name: string;
	title: string;
	content: string;
	priority: NotificationPriority;
}

// 타입 정의
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationStatus = 'draft' | 'sent' | 'cancelled';
export type NotificationTargetType = 'all' | 'group' | 'individual';

// 우선순위 옵션
export const NOTIFICATION_PRIORITIES = [
	{ value: 'low', text: '낮음', color: 'badge-ghost' },
	{ value: 'normal', text: '보통', color: 'badge-info' },
	{ value: 'high', text: '높음', color: 'badge-warning' },
	{ value: 'urgent', text: '긴급', color: 'badge-error' }
] as const;

// 알림 상태 옵션
export const NOTIFICATION_STATUSES = [
	{ value: 'draft', text: '임시저장', color: 'badge-ghost' },
	{ value: 'sent', text: '전송완료', color: 'badge-success' },
	{ value: 'cancelled', text: '취소됨', color: 'badge-error' }
] as const;

// 대상 타입 옵션
export const TARGET_TYPES = [
	{ value: 'all', text: '전체 직원' },
	{ value: 'group', text: '특정 그룹' },
	{ value: 'individual', text: '개별 직원' }
] as const;

// 우선순위 텍스트 가져오기
export function getPriorityText(priority: string): string {
	const found = NOTIFICATION_PRIORITIES.find((p) => p.value === priority);
	return found ? found.text : '알 수 없음';
}

// 우선순위 색상 가져오기
export function getPriorityColor(priority: string): string {
	const found = NOTIFICATION_PRIORITIES.find((p) => p.value === priority);
	return found ? found.color : 'badge-ghost';
}

// 상태 텍스트 가져오기
export function getStatusText(status: string): string {
	const found = NOTIFICATION_STATUSES.find((s) => s.value === status);
	return found ? found.text : '알 수 없음';
}

// 상태 색상 가져오기
export function getStatusColor(status: string): string {
	const found = NOTIFICATION_STATUSES.find((s) => s.value === status);
	return found ? found.color : 'badge-ghost';
}

// 대상 타입 텍스트 가져오기
export function getTargetTypeText(targetType: string): string {
	const found = TARGET_TYPES.find((t) => t.value === targetType);
	return found ? found.text : '알 수 없음';
}
