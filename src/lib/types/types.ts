export type Breadcrumb = {
	title: string; // 제목
	url: string;   // URL
};

export interface Member {
	id: number;
	role: string;
	part: string;
	position: string;
	status: number;
	username: string;
	caps_id: string;
	name: string;
	email: string;
	cellphone: string;
	department: string | null;
	menu: string;
	created_at: string;
	updated_at: string;
	login_at: string | null;
	deleted_at: string | null;
}

// 페이지네이션 데이터를 포함하는 스토어 타입
export interface PaginationStore {
	pageTotal?: number;
	pageCurrentPage?: number;
	pageFrom?: number;
	pageTo?: number;
	pageLastPage?: number;
	pagePerPage?: number;
	pageStartNo?: number;
	hasNext?: boolean;
	hasPrev?: boolean;
	pageLinks?: Array<{label: string, active: boolean, page: number|null}>;
}

// 상품 데이터 타입 정의
export interface Product {
	id: number;
	name: string;
	barcode: string;
	qaid: string;
	amount: number;
	status: number;
	checked_status: number;
	checked_at: string | null;
	duplicated: 'Y' | 'N';
	rg: 'Y' | 'N';
	lot: {
		name: string;
	};
	cate4: {
		name: string;
	};
	cate5?: {
		name: string;
	};
	req: {
		req_at: string;
	};
	link?: {
		product_id: string;
		item_id: string;
		vendor_item_id: string;
	};
	repair_product?: {
		id: number;
		status: string;
		memo: string;
		repair_symptom: RepairSymptom;
		repair_process: RepairProcess;
		repair_grade: RepairGrade;
	};
	pallet_products: Array<{
		id: number;
		repair_grade: RepairGrade;
		pallet: {
			exported_at: string | null;
		};
	}>;
}

// ProductStore 타입 정의
export interface ProductStoreData extends PaginationStore {
	products?: Product[];
	requestAt?: string;
	inspects?: any[];
	cate4?: any;
}

// WorkHistoryStore 타입 정의
export interface WorkHistoryStoreData extends PaginationStore {
	workLogs?: any[];
	summary?: any;
}

export interface RepairPart {
	id: number;
	name: string;
	price: number;
	is_purchasable: 'Y' | 'N';
}

export interface RepairSymptom {
	id: number;
	code: string;
	name: string;
	default_repair_process_id?: number;
	default_repair_grade_id?: number;
}

export interface RepairProcess {
	id: number;
	code: string;
	name: string;
}

export interface RepairGrade {
	id: number;
	code: string;
	name: string;
}

export interface RepairProduct {
	id: number;
	qaid: string;
	name: string;
	barcode: string;
	repair_product?: {
		id: number;
		memo: string;
		repair_symptom: RepairSymptom;
		repair_process: RepairProcess;
		repair_grade: RepairGrade;
	};
}

// 저장될 구성품 데이터 타입 정의
export interface AddPart {
	id: number;
	quantity: number;
	price: number;
}

// 화면 표시용 데이터 타입 정의 (기존 SelectedPart 수정)
export interface SelectedPart {
	id: number;
	product_id: number;
	parts_id: number;
	name: string;
	price: number;
	quantity: number;
}