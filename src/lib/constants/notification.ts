import { readable } from 'svelte/store';
import type {
	NotificationPriority,
	NotificationTargetType,
	NotificationStatus
} from '$lib/types/notification';

/**
 * API 엔드포인트 상수
 */
export const API_ENDPOINTS = {
	// 알림 관련
	NOTIFICATION_SEND: '/wms/notifications/send',

	NOTIFICATIONS: '/wms/settings/notifications',
	NOTIFICATION_CANCEL: '/wms/settings/notifications/cancel',

	// 그룹 관련
	GROUPS: '/wms/settings/notifications/groups',

	// 템플릿 관련
	TEMPLATES: '/wms/settings/notifications/templates',

	// 대시보드 관련
	DASHBOARD_STATS: '/wms/settings/notifications/dashboard',

	// 직원 관련
	MEMBERS: '/wms/settings/members',
	ACTIVE_MEMBERS: '/wms/settings/members/active',

	// 통계 관련
	STATS: '/wms/settings/notifications/stats',

	// 히스토리 관련
	HISTORY: '/wms/notifications/histories'
} as const;

/**
 * 알림 우선순위 상수
 */
export const NOTIFICATION_PRIORITY_LOW = 'low' as const;
export const NOTIFICATION_PRIORITY_NORMAL = 'normal' as const;
export const NOTIFICATION_PRIORITY_HIGH = 'high' as const;
export const NOTIFICATION_PRIORITY_URGENT = 'urgent' as const;

/**
 * 알림 대상 타입 상수
 */
export const NOTIFICATION_TARGET_ALL = 'all' as const;
export const NOTIFICATION_TARGET_GROUP = 'group' as const;
export const NOTIFICATION_TARGET_INDIVIDUAL = 'individual' as const;

/**
 * 알림 상태 상수
 */
export const NOTIFICATION_STATUS_DRAFT = 'draft' as const;
export const NOTIFICATION_STATUS_SENT = 'sent' as const;
export const NOTIFICATION_STATUS_CANCELLED = 'cancelled' as const;

/**
 * 알림 우선순위 옵션 스토어
 */
export const notificationPriorities = readable([
	{ value: NOTIFICATION_PRIORITY_LOW, text: '낮음', color: 'badge-info' },
	{ value: NOTIFICATION_PRIORITY_NORMAL, text: '보통', color: 'badge-success' },
	{ value: NOTIFICATION_PRIORITY_HIGH, text: '높음', color: 'badge-warning' },
	{ value: NOTIFICATION_PRIORITY_URGENT, text: '긴급', color: 'badge-error' }
]);

/**
 * 알림 대상 타입 옵션 스토어
 */
export const notificationTargetTypes = readable([
	{ value: NOTIFICATION_TARGET_ALL, text: '전체 직원', icon: 'fas fa-users' },
	{ value: NOTIFICATION_TARGET_GROUP, text: '특정 그룹', icon: 'fas fa-user-friends' },
	{ value: NOTIFICATION_TARGET_INDIVIDUAL, text: '개별 직원', icon: 'fas fa-user' }
]);

/**
 * 알림 상태 옵션 스토어
 */
export const notificationStatuses = readable([
	{ value: NOTIFICATION_STATUS_DRAFT, text: '임시저장', color: 'badge-ghost' },
	{ value: NOTIFICATION_STATUS_SENT, text: '전송완료', color: 'badge-success' },
	{ value: NOTIFICATION_STATUS_CANCELLED, text: '취소됨', color: 'badge-error' }
]);

/**
 * 우선순위별 색상 매핑
 */
export const PRIORITY_COLORS: Record<NotificationPriority, string> = {
	low: 'badge-info',
	normal: 'badge-success',
	high: 'badge-warning',
	urgent: 'badge-error'
};

/**
 * 상태별 색상 매핑
 */
export const STATUS_COLORS: Record<NotificationStatus, string> = {
	draft: 'badge-ghost',
	sent: 'badge-success',
	cancelled: 'badge-error'
};

/**
 * 대상 타입별 아이콘 매핑
 */
export const TARGET_TYPE_ICONS: Record<NotificationTargetType, string> = {
	all: 'fas fa-users',
	group: 'fas fa-user-friends',
	individual: 'fas fa-user'
};

/**
 * 알림 우선순위 이름 가져오기
 */
export function getPriorityName(priority: NotificationPriority): string {
	const priorities = {
		low: '낮음',
		normal: '보통',
		high: '높음',
		urgent: '긴급'
	};
	return priorities[priority] || '알 수 없음';
}

/**
 * 알림 상태 이름 가져오기
 */
export function getStatusName(status: NotificationStatus): string {
	const statuses = {
		draft: '임시저장',
		sent: '전송완료',
		cancelled: '취소됨'
	};
	return statuses[status] || '알 수 없음';
}

/**
 * 대상 타입 이름 가져오기
 */
export function getTargetTypeName(targetType: NotificationTargetType): string {
	const targetTypes = {
		all: '전체 직원',
		group: '특정 그룹',
		individual: '개별 직원'
	};
	return targetTypes[targetType] || '알 수 없음';
}

/**
 * 우선순위별 색상 클래스 가져오기
 */
export function getPriorityColor(priority: NotificationPriority): string {
	return PRIORITY_COLORS[priority] || 'badge-ghost';
}

/**
 * 상태별 색상 클래스 가져오기
 */
export function getStatusColor(status: NotificationStatus): string {
	return STATUS_COLORS[status] || 'badge-ghost';
}

/**
 * 대상 타입별 아이콘 클래스 가져오기
 */
export function getTargetTypeIcon(targetType: NotificationTargetType): string {
	return TARGET_TYPE_ICONS[targetType] || 'fas fa-question';
}

/**
 * 기본 알림 폼 데이터
 */
export const DEFAULT_NOTIFICATION_FORM = {
	title: '',
	content: '',
	priority: NOTIFICATION_PRIORITY_NORMAL,
	action_url: '',
	target_type: NOTIFICATION_TARGET_ALL,
	target_ids: [],
	template_id: undefined
};

/**
 * 기본 그룹 폼 데이터
 */
export const DEFAULT_GROUP_FORM = {
	name: '',
	description: '',
	member_ids: []
};

/**
 * 기본 템플릿 폼 데이터
 */
export const DEFAULT_TEMPLATE_FORM = {
	name: '',
	title: '',
	content: '',
	priority: NOTIFICATION_PRIORITY_NORMAL
};

/**
 * 페이지네이션 기본 설정
 */
export const DEFAULT_PAGINATION = {
	per_page: 20,
	current_page: 1
};

/**
 * 검색 디바운스 시간 (밀리초)
 */
export const SEARCH_DEBOUNCE_TIME = 300;

/**
 * 에러 코드 상수
 */
export const ERROR_CODES = {
	INVALID_TARGET: 'INVALID_TARGET',
	PERMISSION_DENIED: 'PERMISSION_DENIED',
	TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND',
	GROUP_NOT_FOUND: 'GROUP_NOT_FOUND',
	NETWORK_ERROR: 'NETWORK_ERROR',
	VALIDATION_ERROR: 'VALIDATION_ERROR',
	NOTIFICATION_NOT_FOUND: 'NOTIFICATION_NOT_FOUND',
	ALREADY_CANCELLED: 'ALREADY_CANCELLED',
	SEND_FAILED: 'SEND_FAILED'
} as const;

/**
 * 성공 메시지 상수
 */
export const SUCCESS_MESSAGES = {
	NOTIFICATION_SENT: '알림이 성공적으로 전송되었습니다.',
	NOTIFICATION_CANCELLED: '알림이 취소되었습니다.',
	GROUP_CREATED: '그룹이 생성되었습니다.',
	GROUP_UPDATED: '그룹이 수정되었습니다.',
	GROUP_DELETED: '그룹이 삭제되었습니다.',
	TEMPLATE_CREATED: '템플릿이 생성되었습니다.',
	TEMPLATE_UPDATED: '템플릿이 수정되었습니다.',
	TEMPLATE_DELETED: '템플릿이 삭제되었습니다.'
} as const;

/**
 * 에러 메시지 상수
 */
export const ERROR_MESSAGES = {
	INVALID_TARGET: '유효하지 않은 전송 대상입니다.',
	PERMISSION_DENIED: '권한이 없습니다.',
	TEMPLATE_NOT_FOUND: '템플릿을 찾을 수 없습니다.',
	GROUP_NOT_FOUND: '그룹을 찾을 수 없습니다.',
	NETWORK_ERROR: '네트워크 오류가 발생했습니다.',
	VALIDATION_ERROR: '입력 데이터가 올바르지 않습니다.',
	NOTIFICATION_NOT_FOUND: '알림을 찾을 수 없습니다.',
	ALREADY_CANCELLED: '이미 취소된 알림입니다.',
	SEND_FAILED: '알림 전송에 실패했습니다.',
	UNKNOWN_ERROR: '알 수 없는 오류가 발생했습니다.'
} as const;
