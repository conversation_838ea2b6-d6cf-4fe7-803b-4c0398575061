<script lang="ts">
	// 스캔 메시지 타입 정의
	type MessageType = 'error' | 'warning' | 'info' | 'success';

	// 컴포넌트 props 정의
	let {
		message = '',
		type = 'info',
		show = false,
		className = '',
		conditionalClasses = {}
	}: {
		message: string;
		type?: MessageType;
		show?: boolean;
		className?: string;
		conditionalClasses?: Record<string, boolean>;
	} = $props();

	// daisyUI alert 타입에 따른 클래스 매핑
	const alertClasses = {
		error: 'alert alert-error',
		warning: 'alert alert-warning',
		info: 'alert alert-info',
		success: 'alert alert-success'
	};

	// 조건부 클래스 문자열 생성
	function getConditionalClassString() {
		return Object.entries(conditionalClasses)
			.filter(([_, condition]) => condition)
			.map(([className, _]) => className)
			.join(' ');
	}
</script>

{#if show && message}
	<div class="alert {alertClasses[type]} {className} {getConditionalClassString()}">
		<svg
			xmlns="http://www.w3.org/2000/svg"
			class="stroke-current shrink-0 h-6 w-6 fill-none"
			viewBox="0 0 24 24"
		>
			{#if type === 'error'}
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
				/>
			{:else if type === 'warning'}
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
				/>
			{:else if type === 'success'}
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				/>
			{:else}
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				/>
			{/if}
		</svg>
		<span class="text-lg font-medium">{@html message}</span>
	</div>
{/if}

<style>
	/* 조건부 클래스 스타일 - 기존 스타일 유지 */
	.location_scan_messagebox {
		font-size: 14pt;
		font-weight: bold;
		color: #515151;
	}

	.messagebox_off {
		font-size: 12pt;
		color: #515151;
	}

	.messagebox_on {
		font-size: 14pt;
		font-weight: bold;
		color: #e07934;
	}

	/* daisyUI alert 커스터마이징 */
	.alert {
		@apply text-left;
	}

	/* 조건부 클래스가 적용될 때 기존 스타일 우선 적용 */
	.alert.location_scan_messagebox {
		font-size: 14pt;
		font-weight: bold;
		color: #f1f1f1;
	}

	.alert.messagebox_off {
		font-size: 12pt;
		color: #515151;
	}

	.alert.messagebox_on {
		font-size: 14pt;
		font-weight: bold;
		color: #e07934;
	}
</style>
