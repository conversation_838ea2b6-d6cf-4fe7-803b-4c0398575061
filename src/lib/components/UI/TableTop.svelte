<script lang="ts">
	import { getNumberFormat } from '$lib/Functions';

	interface Props {
		total: number;
		pageSize: string;
		onUpdate: any;
		left?: import('svelte').Snippet;
		right?: import('svelte').Snippet;
	}

	let { total, pageSize, onUpdate, left, right }: Props = $props();

	import { debounce } from '$lib/utils/performance';

	// 디바운스된 파라미터 업데이트 (성능 최적화)
	const debouncedUpdate = debounce((newPageSize: string) => {
		onUpdate({
			detail: {
				pageSize: newPageSize
			}
		});
	}, 200); // 200ms 디바운스

	function updateParams(event: Event) {
		debouncedUpdate(pageSize);
	}
</script>

<!-- 모던하고 컴팩트한 테이블 상단 -->
<div
	class="w-full bg-base-100/80 backdrop-blur-sm rounded-lg p-2 flex items-center justify-between shadow-sm"
>
	<!-- 왼쪽 영역: 전체 개수, 페이지 크기 선택, 왼쪽 버튼 그룹 -->
	<div class="flex items-center gap-4">
		<!-- 전체 개수 표시 -->
		<div class="flex items-center gap-2">
			<span class="text-sm font-medium text-base-content/70">전체</span>
			<span class="px-2 py-1 bg-primary/10 text-primary text-sm font-semibold rounded-md">
				{getNumberFormat(total)}
			</span>
		</div>

		<!-- 페이지 크기 선택 -->
		<div class="flex items-center gap-2">
			<span class="text-sm font-medium text-base-content/70">페이지 크기</span>
			<select
				bind:value={pageSize}
				class="select select-sm select-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary text-sm min-w-0"
				onchange={updateParams}
			>
				<option value="16">16개씩</option>
				<option value="32">32개씩</option>
				<option value="48">48개씩</option>
				<option value="64">64개씩</option>
				<option value="80">80개씩</option>
				<option value="96">96개씩</option>
				<option value="160">160개씩</option>
				<option value="320">320개씩</option>
			</select>
		</div>

		<!-- 왼쪽 버튼 그룹 -->
		{#if left}
			<div class="flex items-center gap-1">
				{@render left?.()}
			</div>
		{/if}
	</div>

	<!-- 오른쪽 영역: 오른쪽 버튼 그룹 -->
	{#if right}
		<div class="flex items-center gap-1">
			{@render right?.()}
		</div>
	{/if}
</div>
