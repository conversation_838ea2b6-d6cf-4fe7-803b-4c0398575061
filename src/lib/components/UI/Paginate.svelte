<script lang="ts">
	import { getNumberFormat, scrollToElement } from '$lib/Functions';
	import {
		generatePageLinks,
		getPaginationSummary,
		PaginationPerformanceMonitor,
		getRecommendedPageSize
	} from '$lib/utils/pagination';
	import type { OptimizedPageData } from '$lib/utils/pagination';
	import type { PaginationStore } from '$lib/types/types';
	import type { Readable } from 'svelte/store';
	import { throttle } from '$lib/utils/performance';

	import Icon from 'svelte-awesome';
	import { faAngleLeft } from '@fortawesome/free-solid-svg-icons/faAngleLeft';
	import { faAngleRight } from '@fortawesome/free-solid-svg-icons/faAngleRight';
	import { faAnglesLeft } from '@fortawesome/free-solid-svg-icons/faAnglesLeft';
	import { faAnglesRight } from '@fortawesome/free-solid-svg-icons/faAnglesRight';

	interface Props {
		store: Readable<PaginationStore>; // 페이지네이션 데이터를 포함하는 스토어
		localUrl: string;
		onPageChange: (page: number) => void;
		searchParams?: string;
		maxLinks?: number;
		enablePerformanceMonitoring?: boolean; // 성능 모니터링 활성화 여부
		enableSmartPageSize?: boolean; // 스마트 페이지 크기 추천 활성화 여부
	}

	let {
		store,
		localUrl,
		onPageChange,
		searchParams = '',
		maxLinks = 10,
		enablePerformanceMonitoring = false,
		enableSmartPageSize = false
	}: Props = $props();

	// 성능 모니터링 인스턴스
	const performanceMonitor = enablePerformanceMonitoring ? PaginationPerformanceMonitor : null;

	// 스토어에서 페이지네이션 데이터 추출
	const pageData = $derived({
		total: $store.pageTotal ?? 0,
		current_page: $store.pageCurrentPage ?? 1,
		last_page: $store.pageLastPage ?? 1,
		per_page: $store.pagePerPage ?? 16,
		from: $store.pageFrom ?? 1,
		to: $store.pageTo ?? 1,
		has_prev: $store.hasPrev ?? ($store.pageCurrentPage ?? 1) > 1,
		has_next: $store.hasNext ?? ($store.pageCurrentPage ?? 1) < ($store.pageLastPage ?? 1)
	});

	// 페이지네이션 정보 계산
	const pageLinks = $derived(
		generatePageLinks(pageData.current_page, pageData.last_page, maxLinks)
	);
	const paginationSummary = $derived(getPaginationSummary(pageData));

	// 스로틀된 페이지 변경 핸들러 (성능 최적화)
	const throttledPageChange = throttle((page: number) => {
		// 성능 측정 시작
		const endMeasure = performanceMonitor?.startMeasure(`pageChange-${page}`);

		onPageChange(page);
		scrollToElement('search-box-bottom');

		// 성능 측정 종료
		endMeasure?.();
	}, 100); // 100ms 스로틀

	function handlePageChange(page: number) {
		throttledPageChange(page);
	}

	// 스마트 페이지 크기 추천 (선택적 기능)
	let recommendedPageSize = $state(0);
	if (enableSmartPageSize && typeof window !== 'undefined') {
		recommendedPageSize = getRecommendedPageSize();
	}

	// URL 생성 함수
	function createPageUrl(page: number) {
		const params = searchParams ? `&${searchParams}` : '';
		return `${localUrl}?p=${page}${params}#search-box-bottom`;
	}
</script>

<!-- 페이지네이션 정보 표시 -->
<div class="flex flex-col px-2 sm:flex-row justify-between items-center gap-2">
	<div class="text-sm text-gray-600">
		{#if paginationSummary.hasData}
			<span class="font-semibold">{paginationSummary.showing}</span>
			/ 전체 <span class="font-semibold">{getNumberFormat(paginationSummary.total)}</span>개
		{:else}
			검색 결과가 없습니다.
		{/if}
	</div>

	<div class="text-sm text-gray-600">
		{#if paginationSummary.hasData}
			페이지 <span class="font-semibold">{paginationSummary.page}</span>
		{/if}
	</div>
</div>

<!-- 페이지네이션 버튼 -->
{#if paginationSummary.hasData && pageData.last_page > 1}
	<nav aria-label="페이지 네비게이션" class="flex justify-center pb-5">
		<div class="join">
			<!-- 첫 페이지 버튼 -->
			{#if pageData.current_page > 1}
				<a
					class="join-item btn btn-sm"
					href={createPageUrl(1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(1);
					}}
					title="첫 페이지"
				>
					<Icon data={faAnglesLeft} />
				</a>
			{/if}

			<!-- 이전 페이지 버튼 -->
			{#if pageData.has_prev}
				<a
					class="join-item btn btn-sm"
					href={createPageUrl(pageData.current_page - 1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.current_page - 1);
					}}
					title="이전 페이지"
				>
					<Icon data={faAngleLeft} />
				</a>
			{/if}

			<!-- 페이지 번호 버튼들 -->
			{#each pageLinks as link}
				{#if link.label === '...'}
					<span class="join-item btn btn-sm btn-disabled"> ... </span>
				{:else if link.active}
					<span class="join-item btn btn-sm btn-accent cursor-default">
						{getNumberFormat(Number(link.label))}
					</span>
				{:else if link.page}
					<a
						class="join-item btn btn-sm"
						href={createPageUrl(link.page)}
						onclick={(e) => {
							e.preventDefault();
							handlePageChange(link.page);
						}}
						title={`${link.label}페이지로 이동`}
					>
						{getNumberFormat(Number(link.label))}
					</a>
				{/if}
			{/each}

			<!-- 다음 페이지 버튼 -->
			{#if pageData.has_next}
				<a
					class="join-item btn btn-sm"
					href={createPageUrl(pageData.current_page + 1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.current_page + 1);
					}}
					title="다음 페이지"
				>
					<Icon data={faAngleRight} />
				</a>
			{/if}

			<!-- 마지막 페이지 버튼 -->
			{#if pageData.current_page < pageData.last_page}
				<a
					class="join-item btn btn-sm"
					href={createPageUrl(pageData.last_page)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.last_page);
					}}
					title="마지막 페이지"
				>
					<Icon data={faAnglesRight} />
				</a>
			{/if}
		</div>
	</nav>
{/if}

<!-- 성능 정보 표시 (개발 모드에서만) -->
{#if enablePerformanceMonitoring && performanceMonitor}
	<div class="text-xs text-base-content/50 mt-2 px-2">
		{#if recommendedPageSize > 0}
			<span class="mr-4">권장 페이지 크기: {recommendedPageSize}개</span>
		{/if}
		<span
			>렌더링 성능: {performanceMonitor
				.getMetrics(`pageChange-${pageData.current_page}`)
				?.renderTime?.toFixed(2) || 'N/A'}ms</span
		>
	</div>
{/if}

<!-- 페이지 크기 선택 (옵션) -->
<!-- 추후 필요시 추가 가능 -->
