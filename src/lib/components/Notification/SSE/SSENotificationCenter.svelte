<!--
	SSE 알림 센터 UI 컴포넌트
	
	이 컴포넌트는 SSE를 통해 수신된 알림들의 목록을 표시하고 관리합니다.
	- 알림 목록 표시 및 읽음 상태 관리
	- 연결 상태 표시 인디케이터
	- 미읽음 알림 개수 배지
	- 기존 DaisyUI 컴포넌트 활용
-->

<script lang="ts">
	// TODO: 다음 작업에서 구현될 예정
	// - SSE 알림 스토어 연동
	// - 알림 목록 표시 로직
	// - 읽음 상태 관리
	// - 연결 상태 인디케이터
	// - 미읽음 알림 개수 배지

	// 실제 구현은 작업 6.3에서 진행됩니다.

	// import type { SSENotification } from '$lib/types/sse';
	// import { sseNotificationStore } from '$lib/stores/notification';

	let isOpen = false;
	let notifications: any[] = [];
	let unreadCount = 0;
	let isConnected = false;

	function toggleNotificationCenter() {
		isOpen = !isOpen;
	}

	function markAsRead(notificationId: string) {
		// TODO: 읽음 처리 로직 구현
		console.log('읽음 처리:', notificationId);
	}
</script>

<!-- 알림 센터 버튼 -->
<div class="dropdown dropdown-end">
	<div
		tabindex="0"
		role="button"
		class="btn btn-ghost btn-circle"
		on:click={toggleNotificationCenter}
	>
		<div class="indicator">
			<i class="fas fa-bell text-lg"></i>
			{#if unreadCount > 0}
				<span class="badge badge-sm badge-error indicator-item">{unreadCount}</span>
			{/if}
		</div>
	</div>

	{#if isOpen}
		<div class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-80">
			<!-- 연결 상태 표시 -->
			<div class="flex items-center justify-between p-2 border-b">
				<h3 class="font-semibold">알림</h3>
				<div class="flex items-center gap-2">
					<div class="w-2 h-2 rounded-full {isConnected ? 'bg-green-500' : 'bg-red-500'}"></div>
					<span class="text-xs text-gray-500">
						{isConnected ? '연결됨' : '연결 해제'}
					</span>
				</div>
			</div>

			<!-- 알림 목록 -->
			<div class="max-h-96 overflow-y-auto">
				{#if notifications.length === 0}
					<div class="p-4 text-center text-gray-500">
						<i class="fas fa-bell-slash text-2xl mb-2"></i>
						<p>새로운 알림이 없습니다</p>
					</div>
				{:else}
					{#each notifications as notification (notification.id)}
						<div
							class="p-3 border-b hover:bg-base-200 cursor-pointer"
							on:click={() => markAsRead(notification.id)}
						>
							<!-- TODO: 알림 내용 표시 로직 구현 -->
							<div class="font-medium">{notification.title || '알림'}</div>
							<div class="text-sm text-gray-600">{notification.message || '내용'}</div>
							<div class="text-xs text-gray-400 mt-1">{notification.timestamp || '시간'}</div>
						</div>
					{/each}
				{/if}
			</div>

			<!-- 하단 액션 -->
			<div class="p-2 border-t">
				<button class="btn btn-sm btn-ghost w-full"> 모든 알림 보기 </button>
			</div>
		</div>
	{/if}
</div>

<style>
	/* 추가 스타일링이 필요한 경우 여기에 작성 */
</style>
