<!--
	긴급/높음 우선순위 알림 모달 컴포넌트
	
	이 컴포넌트는 urgent 및 high 우선순위 알림을 모달 형태로 표시합니다.
	- DaisyUI 모달 컴포넌트 활용
	- 무시 불가능한 urgent 알림 처리
	- 확인 버튼 클릭 시 읽음 확인 전송
	- 리다이렉트 기능 지원
-->

<script lang="ts">
	// TODO: 다음 작업에서 구현될 예정
	// - SSE 알림 타입 연동
	// - 읽음 확인 서비스 연동
	// - 리다이렉트 처리 로직
	// - 우선순위별 스타일링

	// 실제 구현은 작업 6.1에서 진행됩니다.

	// import type { SSENotification } from '$lib/types/sse';
	// import { readReceiptService } from '$lib/services/notification/readReceiptService';
	// import { goto } from '$app/navigation';

	export let notification: any = null;
	export let isOpen = false;

	let isProcessing = false;

	async function handleConfirm() {
		if (!notification) return;

		isProcessing = true;

		try {
			// TODO: 읽음 확인 전송 로직 구현
			console.log('읽음 확인 전송:', notification.id);

			// TODO: 리다이렉트 처리
			if (notification.redirect) {
				console.log('리다이렉트:', notification.redirect);
			}

			// 모달 닫기
			isOpen = false;
		} catch (error) {
			console.error('알림 처리 중 오류:', error);
		} finally {
			isProcessing = false;
		}
	}

	function handleDismiss() {
		// urgent 알림은 무시할 수 없음
		if (notification?.priority === 'urgent') {
			return;
		}

		isOpen = false;
	}

	function getPriorityClass(priority: string) {
		switch (priority) {
			case 'urgent':
				return 'border-error text-error';
			case 'high':
				return 'border-warning text-warning';
			default:
				return 'border-info text-info';
		}
	}

	function getPriorityIcon(priority: string) {
		switch (priority) {
			case 'urgent':
				return 'fa-exclamation-circle';
			case 'high':
				return 'fa-exclamation-triangle';
			default:
				return 'fa-bell';
		}
	}
</script>

<!-- 모달 -->
{#if isOpen && notification}
	<div class="modal modal-open">
		<div class="modal-box max-w-md">
			<!-- 헤더 -->
			<div class="flex items-center gap-3 mb-4">
				<div class="flex-shrink-0">
					<i
						class="fas {getPriorityIcon(notification.priority)} text-2xl {getPriorityClass(
							notification.priority
						)}"
					></i>
				</div>
				<div class="flex-1">
					<h3 class="font-bold text-lg">{notification.title || '알림'}</h3>
					<div class="text-sm opacity-70">
						{notification.priority === 'urgent' ? '긴급 알림' : '중요 알림'}
					</div>
				</div>
			</div>

			<!-- 내용 -->
			<div class="py-4">
				<div class="whitespace-pre-wrap text-sm leading-relaxed">
					{notification.message || '알림 내용이 없습니다.'}
				</div>
			</div>

			<!-- 시간 정보 -->
			<div class="text-xs text-gray-500 mb-4">
				{notification.timestamp || '시간 정보 없음'}
			</div>

			<!-- 액션 버튼 -->
			<div class="modal-action">
				{#if notification.priority !== 'urgent'}
					<button class="btn btn-ghost" on:click={handleDismiss} disabled={isProcessing}>
						나중에
					</button>
				{/if}

				<button class="btn btn-primary" on:click={handleConfirm} disabled={isProcessing}>
					{#if isProcessing}
						<span class="loading loading-spinner loading-sm"></span>
						처리 중...
					{:else if notification.redirect}
						확인 후 이동
					{:else}
						확인
					{/if}
				</button>
			</div>
		</div>

		<!-- 배경 클릭으로 닫기 (urgent가 아닌 경우만) -->
		{#if notification.priority !== 'urgent'}
			<div class="modal-backdrop" on:click={handleDismiss}></div>
		{/if}
	</div>
{/if}

<style>
	/* 긴급 알림의 경우 애니메이션 효과 추가 */
	.modal-open .modal-box {
		animation: modalPulse 0.5s ease-in-out;
	}

	@keyframes modalPulse {
		0%,
		100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.02);
		}
	}
</style>
