<!--
	일반 토스트 알림 컴포넌트
	
	이 컴포넌트는 normal 우선순위 알림을 토스트 형태로 표시합니다.
	- 기존 Svoast 라이브러리와 연동
	- 자동 사라짐 및 수동 닫기 기능
	- 클릭 시 상세 보기 또는 리다이렉트
	- 읽음 확인 처리
-->

<script lang="ts">
	// TODO: 다음 작업에서 구현될 예정
	// - Svoast 라이브러리 연동
	// - 자동 사라짐 타이머 구현
	// - 읽음 확인 처리
	// - 리다이렉트 기능

	// 실제 구현은 작업 6.2에서 진행됩니다.

	// import type { SSENotification } from '$lib/types/sse';
	// import { readReceiptService } from '$lib/services/notification/readReceiptService';

	export let notification: any = null;
	export let isVisible = false;
	export let autoHide = true;
	export let duration = 5000; // 5초 후 자동 숨김

	let timeoutId: NodeJS.Timeout | null = null;

	$: if (isVisible && autoHide) {
		startAutoHideTimer();
	}

	function startAutoHideTimer() {
		if (timeoutId) {
			clearTimeout(timeoutId);
		}

		timeoutId = setTimeout(() => {
			handleDismiss();
		}, duration);
	}

	function stopAutoHideTimer() {
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
	}

	async function handleClick() {
		if (!notification) return;

		try {
			// TODO: 읽음 확인 전송
			console.log('토스트 클릭 - 읽음 확인:', notification.id);

			// TODO: 리다이렉트 처리
			if (notification.redirect) {
				console.log('리다이렉트:', notification.redirect);
			}

			handleDismiss();
		} catch (error) {
			console.error('토스트 클릭 처리 중 오류:', error);
		}
	}

	function handleDismiss() {
		stopAutoHideTimer();
		isVisible = false;
	}

	function getPriorityClass(priority: string) {
		switch (priority) {
			case 'high':
				return 'alert-warning';
			case 'normal':
				return 'alert-info';
			case 'low':
				return 'alert-success';
			default:
				return 'alert-info';
		}
	}

	function getPriorityIcon(priority: string) {
		switch (priority) {
			case 'high':
				return 'fa-exclamation-triangle';
			case 'normal':
				return 'fa-bell';
			case 'low':
				return 'fa-info-circle';
			default:
				return 'fa-bell';
		}
	}
</script>

<!-- 토스트 알림 -->
{#if isVisible && notification}
	<div
		class="toast toast-top toast-end z-50"
		on:mouseenter={stopAutoHideTimer}
		on:mouseleave={() => autoHide && startAutoHideTimer()}
	>
		<div
			class="alert {getPriorityClass(notification.priority)} cursor-pointer shadow-lg max-w-sm"
			on:click={handleClick}
		>
			<div class="flex items-start gap-3 w-full">
				<!-- 아이콘 -->
				<div class="flex-shrink-0">
					<i class="fas {getPriorityIcon(notification.priority)} text-lg"></i>
				</div>

				<!-- 내용 -->
				<div class="flex-1 min-w-0">
					{#if notification.title}
						<div class="font-semibold text-sm mb-1 truncate">
							{notification.title}
						</div>
					{/if}

					<div class="text-sm leading-relaxed">
						{#if notification.message.length > 100}
							{notification.message.substring(0, 100)}...
						{:else}
							{notification.message}
						{/if}
					</div>

					{#if notification.redirect}
						<div class="text-xs opacity-70 mt-1">클릭하여 이동</div>
					{/if}
				</div>

				<!-- 닫기 버튼 -->
				<button
					class="btn btn-ghost btn-xs btn-circle flex-shrink-0"
					on:click|stopPropagation={handleDismiss}
				>
					<i class="fas fa-times"></i>
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.toast {
		position: fixed;
		top: 1rem;
		right: 1rem;
		z-index: 1000;
	}

	.alert {
		transition: all 0.3s ease;
		animation: slideInRight 0.3s ease-out;
	}

	@keyframes slideInRight {
		from {
			transform: translateX(100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	.alert:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	}
</style>
