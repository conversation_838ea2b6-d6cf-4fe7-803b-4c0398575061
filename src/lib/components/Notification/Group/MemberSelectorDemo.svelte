<script lang="ts">
	import { onMount } from 'svelte';
	import type { Member } from '$lib/types/types';
	import MemberSelector from './MemberSelector.svelte';

	// 데모용 상태
	let selectedMembers = $state<Member[]>([]);
	let excludeGroupId = $state<number | undefined>(undefined);
	let isDisabled = $state(false);

	/**
	 * 멤버 선택 변경 핸들러
	 */
	function handleSelectionChange(event: { selectedMembers: Member[] }) {
		selectedMembers = event.selectedMembers;
		console.log('선택된 멤버 변경:', selectedMembers);
	}

	/**
	 * 선택된 멤버 초기화
	 */
	function resetSelection() {
		selectedMembers = [];
	}

	/**
	 * 컴포넌트 비활성화 토글
	 */
	function toggleDisabled() {
		isDisabled = !isDisabled;
	}
</script>

<div class="p-6 max-w-4xl mx-auto">
	<h1 class="text-2xl font-bold mb-6">멤버 선택기 컴포넌트 데모</h1>

	<!-- 컨트롤 패널 -->
	<div class="mb-6 p-4 bg-base-200 rounded-lg">
		<h2 class="text-lg font-semibold mb-4">컨트롤 패널</h2>

		<div class="flex flex-wrap gap-4 items-center">
			<div class="form-control">
				<label class="label">
					<span class="label-text">제외할 그룹 ID</span>
				</label>
				<input
					type="number"
					class="input input-sm input-bordered w-32"
					bind:value={excludeGroupId}
					placeholder="그룹 ID"
				/>
			</div>

			<div class="form-control">
				<label class="cursor-pointer label gap-2">
					<input type="checkbox" class="checkbox checkbox-sm" bind:checked={isDisabled} />
					<span class="label-text">비활성화</span>
				</label>
			</div>

			<button type="button" class="btn btn-sm btn-secondary" onclick={resetSelection}>
				선택 초기화
			</button>
		</div>
	</div>

	<!-- 멤버 선택기 컴포넌트 -->
	<div class="mb-6">
		<h2 class="text-lg font-semibold mb-4">멤버 선택기</h2>
		<div class="max-w-2xl">
			<MemberSelector
				{selectedMembers}
				{excludeGroupId}
				disabled={isDisabled}
				onselectionchange={handleSelectionChange}
				placeholder="직원 이름, 아이디, 이메일로 검색..."
				maxHeight="500px"
			/>
		</div>
	</div>

	<!-- 선택 결과 표시 -->
	<div class="mb-6">
		<h2 class="text-lg font-semibold mb-4">선택 결과</h2>
		<div class="p-4 bg-base-100 border border-base-300 rounded-lg">
			{#if selectedMembers.length === 0}
				<p class="text-base-content/60">선택된 멤버가 없습니다.</p>
			{:else}
				<div class="space-y-2">
					<p class="font-medium">총 {selectedMembers.length}명 선택됨:</p>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-2">
						{#each selectedMembers as member (member.id)}
							<div class="flex items-center gap-2 p-2 bg-base-200 rounded">
								<div class="avatar placeholder">
									<div class="bg-primary text-primary-content rounded-full w-8 h-8">
										<span class="text-xs">{member.name.charAt(0)}</span>
									</div>
								</div>
								<div class="flex-1 min-w-0">
									<div class="font-medium text-sm truncate">{member.name}</div>
									<div class="text-xs text-base-content/60 truncate">
										@{member.username} • {member.email}
									</div>
								</div>
							</div>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	</div>

	<!-- JSON 출력 (개발용) -->
	<div class="mb-6">
		<h2 class="text-lg font-semibold mb-4">JSON 출력 (개발용)</h2>
		<div class="mockup-code">
			<pre><code
					>{JSON.stringify(
						selectedMembers.map((m) => ({
							id: m.id,
							name: m.name,
							username: m.username,
							email: m.email,
							part: m.part,
							position: m.position
						})),
						null,
						2
					)}</code
				></pre>
		</div>
	</div>
</div>
