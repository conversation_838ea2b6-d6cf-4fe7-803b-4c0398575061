<script lang="ts">
	import { onMount } from 'svelte';
	import type { Member } from '$lib/types/types';
	import { getUser, type User } from '$lib/User';
	import { getActiveMembers } from '$lib/stores/notification/memberStore';
	import { handleCatch } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import type { GroupMember } from '$lib/types/notification';

	// Props 인터페이스 정의
	interface Props {
		show?: boolean;
		selectedMembers?: GroupMember[];
		excludeUserIds?: number[];
		excludeGroupId?: number;
		onclose?: () => void;
		onselected?: (event: { members: GroupMember[] }) => void;
		onselectionchange?: (event: { selectedMembers: GroupMember[] }) => void;
		disabled?: boolean;
		placeholder?: string;
		maxHeight?: string;
		modalMode?: boolean;
	}

	// Props 받기 (Svelte 5 방식)
	let {
		show = false,
		selectedMembers = [],
		excludeUserIds = [],
		excludeGroupId,
		onclose,
		onselected,
		onselectionchange,
		disabled = false,
		placeholder = '직원을 검색하세요...',
		maxHeight = '400px',
		modalMode = false
	} = $props();

	// 상태 변수들
	let user: User = getUser();
	let searchKeyword = $state('');
	let availableMembers = $state<Member[]>([]);
	let filteredMembers = $state<Member[]>([]);
	let isLoading = $state(false);
	let localSelectedMembers = $state<GroupMember[]>([]);

	// 검색 디바운스를 위한 타이머
	let searchTimer: NodeJS.Timeout | null = null;

	// 모달 모드일 때 로컬 상태 초기화 (중복 방지)
	let lastModalState = false;
	$effect(() => {
		if (modalMode && show && !lastModalState) {
			lastModalState = true;
			localSelectedMembers = [...selectedMembers];
		} else if (!show) {
			lastModalState = false;
		}
	});

	// 검색어 변경 시 필터링
	$effect(() => {
		let filtered = availableMembers;

		// 제외할 사용자 ID 필터링
		if (excludeUserIds.length > 0) {
			filtered = filtered.filter((member) => !excludeUserIds.includes(member.id));
		}

		// 검색어 필터링
		if (searchKeyword.trim() !== '') {
			const keyword = searchKeyword.toLowerCase();
			filtered = filtered.filter(
				(member) =>
					member.name.toLowerCase().includes(keyword) ||
					member.username.toLowerCase().includes(keyword) ||
					member.email.toLowerCase().includes(keyword) ||
					(member.part && member.part.toLowerCase().includes(keyword)) ||
					(member.position && member.position.toLowerCase().includes(keyword))
			);
		}

		filteredMembers = filtered;
	});

	/**
	 * 활성 직원 목록 로드
	 */
	async function loadActiveMembers() {
		try {
			isLoading = true;
			const params: {
				exclude_group_id?: number;
				role?: string;
				status?: string;
				keyword?: string;
				pageSize?: number;
			} = {
				exclude_group_id: excludeGroupId ?? null,
				role: '',
				status: '1',
				keyword: '',
				pageSize: 100
			};

			const response = await getActiveMembers(user, params);

			if (response.success && response.data) {
				availableMembers = response.data.items || [];
			} else {
				availableMembers = [];
			}
		} catch (error) {
			await handleCatch(error);
			availableMembers = [];
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 검색어 변경 핸들러 (디바운스 적용)
	 */
	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// 기존 타이머 클리어
		if (searchTimer) {
			clearTimeout(searchTimer);
		}

		// 300ms 후 검색 실행
		searchTimer = setTimeout(() => {
			searchKeyword = target.value;
		}, 300);
	}

	/**
	 * 검색어 초기화
	 */
	function clearSearch() {
		searchKeyword = '';
	}

	/**
	 * 멤버 선택/해제 토글
	 */
	function toggleMemberSelection(member: Member) {
		if (disabled) return;
		
		const currentMembers = modalMode ? localSelectedMembers : selectedMembers;
		const selectedIds = new Set(currentMembers.map((m) => m.user_id));
		let newSelectedMembers: GroupMember[];

		if (selectedIds.has(member.id)) {
			// 선택 해제
			newSelectedMembers = currentMembers.filter((m) => m.user_id !== member.id);
		} else {
			// 선택 추가
			const newMember = { id: null, group_id: null, user_id: member.id, created_at: '', updated_at: '', user: member };
			newSelectedMembers = [...currentMembers, newMember];
		}

		if (modalMode) {
			localSelectedMembers = newSelectedMembers;
		} else {
			// 부모 컴포넌트에 변경 사항 알림
			onselectionchange?.({ selectedMembers: newSelectedMembers });
		}
	}

	/**
	 * 전체 선택/해제
	 */
	function toggleAllSelection() {
		if (disabled) return;

		const currentMembers = modalMode ? localSelectedMembers : selectedMembers;
		const selectedIds = new Set(currentMembers.map((m) => m.user_id));
		const allSelected = filteredMembers.every((member) => selectedIds.has(member.id));
		let newSelectedMembers: GroupMember[];

		console.log('filteredMembers: ', filteredMembers);
		if (allSelected) {
			// 전체 해제 - 현재 필터된 멤버들만 제거
			const filteredMemberIds = new Set(filteredMembers.map((member) => member.id));
			newSelectedMembers = currentMembers.filter((m) => !filteredMemberIds.has(m.user_id));
		} else {
			// 전체 선택 - 현재 필터된 멤버들 중 선택되지 않은 것들 추가
			const newMembers = filteredMembers.filter((member) => !selectedIds.has(member.id));
			newSelectedMembers = [...currentMembers, ...newMembers];
		}

		if (modalMode) {
			localSelectedMembers = newSelectedMembers;
		} else {
			onselectionchange?.({ selectedMembers: newSelectedMembers });
		}
	}

	/**
	 * 선택된 멤버 제거
	 */
	function removeMember(memberId: number) {
		if (disabled) return;
		
		const currentMembers = modalMode ? localSelectedMembers : selectedMembers;
		const newSelectedMembers = currentMembers.filter((m) => m.user_id !== memberId);

		if (modalMode) {
			localSelectedMembers = newSelectedMembers;
		} else {
			onselectionchange?.({ selectedMembers: newSelectedMembers });
		}
	}

	/**
	 * 전체 선택 해제
	 */
	function clearAllSelections() {
		if (disabled) return;

		if (modalMode) {
			localSelectedMembers = [];
		} else {
			onselectionchange?.({ selectedMembers: [] });
		}
	}

	/**
	 * 모달 닫기
	 */
	function handleClose() {
		onclose?.();
	}

	/**
	 * 선택 완료
	 */
	function handleConfirm() {
		onselected?.({ members: localSelectedMembers });
	}

	/**
	 * ESC 키로 모달 닫기
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (modalMode && event.key === 'Escape') {
			handleClose();
		}
	}

	// 현재 선택된 멤버 목록 (모달 모드에 따라 다름)
	const currentSelectedMembers = $derived(modalMode ? localSelectedMembers : selectedMembers);
	const selectedIds = $derived(new Set(currentSelectedMembers.map((m) => m.user_id)));

	// 전체 선택 상태 계산
	const isAllSelected = $derived(
		filteredMembers.length > 0 && filteredMembers.every((member) => selectedIds.has(member.id))
	);

	const isIndeterminate = $derived(
		filteredMembers.some((member) => selectedIds.has(member.id)) && !isAllSelected
	);

	// 컴포넌트 마운트 시 데이터 로드 (한 번만)
	let isMembersLoaded = false;
	onMount(() => {
		if (!isMembersLoaded) {
			isMembersLoaded = true;
			loadActiveMembers();
		}
	});
</script>

<svelte:window onkeydown={handleKeydown} />

{#if modalMode && show}
	<!-- 모달 모드 -->
	<div class="modal modal-open">
		<div class="modal-box max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
			<!-- 모달 헤더 -->
			<div class="flex justify-between items-center mb-4 flex-shrink-0">
				<div>
					<h3 class="font-bold text-lg">멤버 선택</h3>
					<p class="text-sm text-base-content/70">그룹에 추가할 멤버를 선택하세요</p>
				</div>
				<button class="btn btn-ghost btn-sm btn-circle" onclick={handleClose}>
					<Icon data={faXmark} />
				</button>
			</div>

			<!-- 멤버 선택기 내용 -->
			<div
				class="member-selector-content bg-base-100 border border-base-300 rounded-lg flex-1 overflow-hidden flex flex-col"
			>
				<!-- 검색 영역 -->
				<div class="p-4 border-b border-base-300 flex-shrink-0">
					<div class="flex items-center gap-2">
						<!-- 검색 입력 -->
						<div class="flex-1 relative">
							<label class="input input-sm input-bordered flex items-center gap-2">
								<Icon data={faSearch} class="w-4 h-4 text-base-content/60" />
								<input
									type="text"
									{placeholder}
									{disabled}
									class="grow bg-transparent"
									class:cursor-not-allowed={disabled}
									oninput={handleSearchInput}
								/>
								{#if searchKeyword}
									<button
										type="button"
										class="btn btn-ghost btn-xs"
										onclick={clearSearch}
										{disabled}
									>
										<Icon data={faXmark} class="w-3 h-3" />
									</button>
								{/if}
							</label>
						</div>

						<!-- 전체 선택/해제 버튼 -->
						<div class="flex items-center gap-2">
							<label class="label cursor-pointer gap-2" class:cursor-not-allowed={disabled}>
								<input
									type="checkbox"
									class="checkbox checkbox-sm"
									checked={isAllSelected}
									indeterminate={isIndeterminate}
									onchange={toggleAllSelection}
									{disabled}
								/>
								<span class="label-text text-sm">전체</span>
							</label>
						</div>
					</div>

					<!-- 선택된 멤버 수 표시 -->
					<div class="mt-2 flex items-center justify-between text-sm text-base-content/70">
						<span>
							<Icon data={faUsers} class="w-4 h-4 mr-1" />
							전체 {availableMembers.length}명 중 {currentSelectedMembers.length}명 선택
						</span>
						{#if currentSelectedMembers.length > 0}
							<button
								type="button"
								class="btn btn-ghost btn-xs text-error"
								onclick={clearAllSelections}
								{disabled}
							>
								전체 해제
							</button>
						{/if}
					</div>
				</div>

				<!-- 멤버 목록 영역 -->
				<div class="member-list flex-1 overflow-y-auto">
					{#if isLoading}
						<div class="p-4 text-center">
							<span class="loading loading-spinner loading-sm"></span>
							<span class="ml-2 text-sm text-base-content/70">직원 목록을 불러오는 중...</span>
						</div>
					{:else if filteredMembers.length === 0}
						<div class="p-4 text-center text-base-content/70">
							{#if searchKeyword}
								<Icon data={faSearch} class="w-8 h-8 mx-auto mb-2 opacity-50" />
								<p class="text-sm">'{searchKeyword}'에 대한 검색 결과가 없습니다.</p>
							{:else}
								<Icon data={faUser} class="w-8 h-8 mx-auto mb-2 opacity-50" />
								<p class="text-sm">선택 가능한 직원이 없습니다.</p>
							{/if}
						</div>
					{:else}
						{#each filteredMembers as member (member.id)}
							<div class="member-item border-b border-base-200 last:border-b-0">
								<label
									class="flex items-center p-3 hover:bg-base-200/50 cursor-pointer transition-colors"
									class:cursor-not-allowed={disabled}
									class:opacity-50={disabled}
								>
									<input
										type="checkbox"
										class="checkbox checkbox-sm mr-3"
										checked={selectedIds.has(member.id)}
										onchange={() => toggleMemberSelection(member)}
										{disabled}
									/>

									<div class="flex-1 min-w-0">
										<div class="flex items-center gap-2">
											<span class="font-medium text-sm truncate">
												{member.name}
											</span>
											{#if member.position}
												<span class="badge badge-ghost badge-xs">
													{member.position}
												</span>
											{/if}
										</div>

										<div class="text-xs text-base-content/60 mt-1">
											<div class="flex items-center gap-2">
												<span>@{member.username}</span>
												{#if member.part}
													<span>•</span>
													<span>{member.part}</span>
												{/if}
											</div>
											<div class="mt-1">
												{member.email}
											</div>
										</div>
									</div>

									<!-- 선택 상태 표시 -->
									{#if selectedIds.has(member.id)}
										<div class="ml-2">
											<div class="w-2 h-2 bg-primary rounded-full"></div>
										</div>
									{/if}
								</label>
							</div>
						{/each}
					{/if}
				</div>

				<!-- 선택된 멤버 미리보기 영역 -->
				{#if currentSelectedMembers.length > 0}
					<div class="p-4 border-t border-base-300 bg-base-50 flex-shrink-0">
						<div class="flex items-center justify-between mb-2">
							<h4 class="text-sm font-medium">선택된 멤버 ({currentSelectedMembers.length}명)</h4>
							<button
								type="button"
								class="btn btn-ghost btn-xs"
								onclick={clearAllSelections}
								{disabled}
							>
								<Icon data={faXmark} class="w-3 h-3 mr-1" />
								전체 해제
							</button>
						</div>

						<div class="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
							{#each currentSelectedMembers as member (member.user_id)}
								<div class="badge badge-primary badge-sm gap-1 pr-1">
									<span class="truncate max-w-20">{member.user.name}</span>
									{#if !disabled}
										<button
											type="button"
											class="btn btn-ghost btn-xs p-0 min-h-0 h-4 w-4"
											onclick={() => removeMember(member.user.id)}
										>
											<Icon data={faXmark} class="w-2 h-2" />
										</button>
									{/if}
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>

			<!-- 모달 액션 -->
			<div class="modal-action flex-shrink-0 mt-4">
				<button class="btn btn-ghost" onclick={handleClose}>취소</button>
				<button
					class="btn btn-primary"
					onclick={handleConfirm}
					disabled={currentSelectedMembers.length === 0}
				>
					선택 완료 ({currentSelectedMembers.length}명)
				</button>
			</div>
		</div>

		<!-- 모달 배경 -->
		<div
			class="modal-backdrop"
			onclick={handleClose}
			role="button"
			tabindex="-1"
			onkeydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					handleClose();
				}
			}}
		></div>
	</div>
{:else}
	<!-- 인라인 모드 -->
	<div class="member-selector bg-base-100 border border-base-300 rounded-lg">
		<!-- 검색 영역 -->
		<div class="p-4 border-b border-base-300">
			<div class="flex items-center gap-2">
				<!-- 검색 입력 -->
				<div class="flex-1 relative">
					<label class="input input-sm input-bordered flex items-center gap-2">
						<Icon data={faSearch} class="w-4 h-4 text-base-content/60" />
						<input
							type="text"
							{placeholder}
							{disabled}
							class="grow bg-transparent"
							class:cursor-not-allowed={disabled}
							oninput={handleSearchInput}
						/>
						{#if searchKeyword}
							<button type="button" class="btn btn-ghost btn-xs" onclick={clearSearch} {disabled}>
								<Icon data={faXmark} class="w-3 h-3" />
							</button>
						{/if}
					</label>
				</div>

				<!-- 전체 선택/해제 버튼 -->
				<div class="flex items-center gap-2">
					<label class="label cursor-pointer gap-2" class:cursor-not-allowed={disabled}>
						<input
							type="checkbox"
							class="checkbox checkbox-sm"
							checked={isAllSelected}
							indeterminate={isIndeterminate}
							onchange={toggleAllSelection}
							{disabled}
						/>
						<span class="label-text text-sm">전체</span>
					</label>
				</div>
			</div>

			<!-- 선택된 멤버 수 표시 -->
			<div class="mt-2 flex items-center justify-between text-sm text-base-content/70">
				<span>
					<Icon data={faUsers} class="w-4 h-4 mr-1" />
					전체 {availableMembers.length}명 중 {currentSelectedMembers.length}명 선택
				</span>
				{#if currentSelectedMembers.length > 0}
					<button
						type="button"
						class="btn btn-ghost btn-xs text-error"
						onclick={clearAllSelections}
						{disabled}
					>
						전체 해제
					</button>
				{/if}
			</div>
		</div>

		<!-- 멤버 목록 영역 -->
		<div class="member-list overflow-y-auto" style="max-height: {maxHeight};">
			{#if isLoading}
				<div class="p-4 text-center">
					<span class="loading loading-spinner loading-sm"></span>
					<span class="ml-2 text-sm text-base-content/70">직원 목록을 불러오는 중...</span>
				</div>
			{:else if filteredMembers.length === 0}
				<div class="p-4 text-center text-base-content/70">
					{#if searchKeyword}
						<Icon data={faSearch} class="w-8 h-8 mx-auto mb-2 opacity-50" />
						<p class="text-sm">'{searchKeyword}'에 대한 검색 결과가 없습니다.</p>
					{:else}
						<Icon data={faUser} class="w-8 h-8 mx-auto mb-2 opacity-50" />
						<p class="text-sm">선택 가능한 직원이 없습니다.</p>
					{/if}
				</div>
			{:else}
				{#each filteredMembers as member (member.id)}
					<div class="member-item border-b border-base-200 last:border-b-0">
						<label
							class="flex items-center p-3 hover:bg-base-200/50 cursor-pointer transition-colors"
							class:cursor-not-allowed={disabled}
							class:opacity-50={disabled}
						>
							<input
								type="checkbox"
								class="checkbox checkbox-sm mr-3"
								checked={selectedIds.has(member.id)}
								onchange={() => toggleMemberSelection(member)}
								{disabled}
							/>

							<div class="flex-1 min-w-0">
								<div class="flex items-center gap-2">
									<span class="font-medium text-sm truncate">
										{member.name}
									</span>
									{#if member.position}
										<span class="badge badge-ghost badge-xs">
											{member.position}
										</span>
									{/if}
								</div>

								<div class="text-xs text-base-content/60 mt-1">
									<div class="flex items-center gap-2">
										<span>@{member.username}</span>
										{#if member.part}
											<span>•</span>
											<span>{member.part}</span>
										{/if}
									</div>
									<div class="mt-1">
										{member.email}
									</div>
								</div>
							</div>

							<!-- 선택 상태 표시 -->
							{#if selectedIds.has(member.id)}
								<div class="ml-2">
									<div class="w-2 h-2 bg-primary rounded-full"></div>
								</div>
							{/if}
						</label>
					</div>
				{/each}
			{/if}
		</div>

		<!-- 선택된 멤버 미리보기 영역 -->
		{#if currentSelectedMembers.length > 0}
			<div class="p-4 border-t border-base-300 bg-base-50">
				<div class="flex items-center justify-between mb-2">
					<h4 class="text-sm font-medium">선택된 멤버 ({currentSelectedMembers.length}명)</h4>
					<button
						type="button"
						class="btn btn-ghost btn-xs"
						onclick={clearAllSelections}
						{disabled}
					>
						<Icon data={faXmark} class="w-3 h-3 mr-1" />
						전체 해제
					</button>
				</div>

				<div class="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
					{#each currentSelectedMembers as member (member.user?.id || member.id)}
						<div class="badge badge-primary badge-sm gap-1 pr-1">
							<span class="truncate max-w-20">{member.user?.name || member.name}</span>
							{#if !disabled}
								<button
									type="button"
									class="btn btn-ghost btn-xs p-0 min-h-0 h-4 w-4"
									onclick={() => removeMember(member.user?.id || member.id)}
								>
									<Icon data={faXmark} class="w-2 h-2" />
								</button>
							{/if}
						</div>
					{/each}
				</div>
			</div>
		{/if}
	</div>
{/if}

<style>
	.member-selector {
		min-height: 200px;
	}

	.member-list {
		min-height: 150px;
	}

	.member-item:hover {
		background-color: rgba(0, 0, 0, 0.02);
	}

	/* 체크박스 indeterminate 상태 스타일 */
	input[type='checkbox']:indeterminate {
		background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4 8h8'/%3e%3c/svg%3e");
	}
</style>
