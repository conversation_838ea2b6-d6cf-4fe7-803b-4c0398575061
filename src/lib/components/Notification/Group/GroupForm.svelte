<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import type { GroupMember } from '$lib/types/notification';
	import { createGroup, updateGroup, getGroup } from '$lib/stores/notification/groupStore';
	import MemberSelector from '$components/Notification/Group/MemberSelector.svelte';

	// Font Awesome 아이콘 import
	import Icon from 'svelte-awesome';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faSave } from '@fortawesome/free-solid-svg-icons/faSave';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';

	// Props 인터페이스 정의
	interface Props {
		show: boolean;
		groupId?: number | null;
		onclose?: () => void;
		onsaved?: () => void;
	}

	let { show, groupId = null, onclose, onsaved } = $props();

	let user: User = getUser();
	let loading = $state(false);
	let saving = $state(false);

	// 폼 데이터
	let formData = $state({
		name: '',
		description: '',
		is_active: true,
		member_ids: [] as number[]
	});

	// 유효성 검사 에러
	let errors = $state({
		name: '',
		description: '',
		is_active: '',
		member_ids: ''
	});

	// 멤버 관련 상태
	let selectedMembers: GroupMember[] = $state<GroupMember[]>([]);

	// 편집 모드 여부
	const isEditMode = $derived(groupId !== null && groupId !== undefined);
	const modalTitle = $derived(isEditMode ? '그룹 수정' : '새 그룹 생성');

	// 모달이 열릴 때 데이터 로드 (중복 요청 방지)
	let lastLoadedGroupId: number | null | undefined = undefined;
	let isFormLoaded = false;

	$effect(() => {
		if (show && !isFormLoaded) {
			// 처음 열릴 때만 로드
			isFormLoaded = true;
			lastLoadedGroupId = groupId;
			loadFormData();
		} else if (show && groupId !== lastLoadedGroupId) {
			// 그룹 ID가 변경된 경우에만 다시 로드
			lastLoadedGroupId = groupId;
			loadFormData();
		} else if (!show) {
			// 모달이 닫힐 때 초기화
			isFormLoaded = false;
			lastLoadedGroupId = undefined;
			resetForm();
		}
	});

	// 폼 데이터 로드
	async function loadFormData() {
		loading = true;
		try {
			// 편집 모드인 경우 그룹 데이터 로드
			if (isEditMode && groupId) {
				const group = await getGroup(user, groupId);
				if (group) {
					formData.name = group.name;
					formData.description = group.description || '';
					formData.is_active = group.is_active ?? true;
					formData.member_ids = group.members?.map((m: GroupMember) => m.user_id) || [];

					// 선택된 멤버 설정
					selectedMembers = group.members || [];
				}
			}
		} finally {
			loading = false;
		}
	}

	// 멤버 선택 변경 핸들러
	function handleMemberSelectionChange(event: { selectedMembers: GroupMember[] }) {
		selectedMembers = event.selectedMembers;
		formData.member_ids = selectedMembers.map((m) => m.user_id);

		// 에러 메시지 초기화
		if (errors.member_ids) {
			errors.member_ids = '';
		}
	}

	// 폼 유효성 검사
	function validateForm(): boolean {
		let isValid = true;

		// 그룹명 검사
		if (!formData.name.trim()) {
			errors.name = '그룹명을 입력해주세요.';
			isValid = false;
		} else if (formData.name.trim().length < 2) {
			errors.name = '그룹명은 2자 이상 입력해주세요.';
			isValid = false;
		} else if (formData.name.trim().length > 100) {
			errors.name = '그룹명은 100자 이하로 입력해주세요.';
			isValid = false;
		} else {
			errors.name = '';
		}

		// 설명 검사 (선택사항)
		if (formData.description && formData.description.length > 500) {
			errors.description = '설명은 500자 이하로 입력해주세요.';
			isValid = false;
		} else {
			errors.description = '';
		}

		// 멤버 선택 검사
		if (formData.member_ids.length === 0) {
			errors.member_ids = '최소 1명 이상의 멤버를 선택해주세요.';
			isValid = false;
		} else {
			errors.member_ids = '';
		}

		return isValid;
	}

	// 실시간 유효성 검사
	function handleNameInput() {
		if (errors.name) {
			if (formData.name.trim()) {
				errors.name = '';
			}
		}
	}

	function handleDescriptionInput() {
		if (errors.description) {
			if (!formData.description || formData.description.length <= 500) {
				errors.description = '';
			}
		}
	}

	// 폼 제출
	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		saving = true;
		try {
			let success = false;
			if (isEditMode && groupId) {
				// 그룹 수정
				success = await updateGroup(user, groupId, formData);
			} else {
				// 그룹 생성
				success = await createGroup(user, formData);
			}

			// 성공 시 콜백 호출
			if (success) {
				onsaved?.();
			}
		} catch (error) {
			console.error('그룹 저장 실패:', error);
		} finally {
			saving = false;
		}
	}

	// 폼 초기화
	function resetForm() {
		formData = {
			name: '',
			description: '',
			is_active: true,
			member_ids: []
		};
		errors = {
			name: '',
			description: '',
			is_active: '',
			member_ids: ''
		};
		selectedMembers = [];
	}

	// 모달 닫기
	function handleClose() {
		if (saving) return; // 저장 중에는 닫기 방지
		onclose?.();
	}

	// ESC 키로 모달 닫기
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && !saving) {
			handleClose();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if show}
	<div class="modal modal-open">
		<div class="modal-box w-11/12 max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
			<!-- 모달 헤더 -->
			<div class="flex justify-between items-center mb-6">
				<h3 class="font-bold text-lg">{modalTitle}</h3>
				<button
					class="btn btn-sm btn-circle btn-ghost"
					onclick={handleClose}
					disabled={saving}
					title="닫기"
				>
					<Icon data={faTimes} />
				</button>
			</div>

			{#if loading}
				<!-- 로딩 상태 -->
				<div class="flex justify-center items-center py-12">
					<span class="loading loading-spinner loading-lg"></span>
					<span class="ml-2">데이터를 불러오는 중...</span>
				</div>
			{:else}
				<!-- 폼 내용 - 좌우 분할 레이아웃 -->
				<div class="flex-1 overflow-hidden">
					<form
						onsubmit={(e) => {
							e.preventDefault();
							handleSubmit();
						}}
						class="h-full"
					>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
							<!-- 좌측: 기본 정보 -->
							<div class="card bg-base-200 p-4">
								<h4 class="font-bold text-lg mb-4">기본 정보</h4>
								<div class="space-y-4">
									<!-- 그룹명 -->
									<div class="form-control">
										<label class="label" for="group-name">
											<span class="label-text">그룹명 <span class="text-error">*</span></span>
										</label>
										<input
											id="group-name"
											type="text"
											placeholder="그룹명을 입력하세요"
											class="input input-bordered"
											class:input-error={errors.name}
											bind:value={formData.name}
											oninput={handleNameInput}
											maxlength="100"
											required
										/>
										{#if errors.name}
											<div class="label">
												<span class="label-text-alt text-error">{errors.name}</span>
											</div>
										{/if}
									</div>

									<!-- 설명 -->
									<div class="form-control">
										<label class="label" for="group-description">
											<span class="label-text">설명</span>
										</label>
										<textarea
											id="group-description"
											placeholder="그룹에 대한 설명을 입력하세요 (선택사항)"
											class="textarea textarea-bordered h-24"
											class:textarea-error={errors.description}
											bind:value={formData.description}
											oninput={handleDescriptionInput}
											maxlength="500"
										></textarea>
										<div class="label">
											<span class="label-text-alt">
												{formData.description.length}/500자
											</span>
											{#if errors.description}
												<span class="label-text-alt text-error">{errors.description}</span>
											{/if}
										</div>
									</div>

									<!-- 활성화 여부 -->
									<div class="form-control">
										<label class="label cursor-pointer">
											<span class="label-text">활성화 상태</span>
											<input
												type="checkbox"
												class="toggle toggle-primary"
												bind:checked={formData.is_active}
											/>
										</label>
										<div class="label">
											<span class="label-text-alt text-sm">
												{formData.is_active
													? '활성화됨 - 알림 전송 대상에 포함됩니다'
													: '비활성화됨 - 알림 전송 대상에서 제외됩니다'}
											</span>
										</div>
									</div>
								</div>
							</div>

							<!-- 우측: 멤버 선택 -->
							<div class="card bg-base-200 p-4">
								<h4 class="font-bold text-lg mb-4">
									멤버 선택 <span class="text-error">*</span>
								</h4>
								<div>
									<MemberSelector
										{selectedMembers}
										excludeGroupId={isEditMode ? groupId : undefined}
										onselectionchange={handleMemberSelectionChange}
										disabled={saving}
										placeholder="직원 이름, 아이디, 이메일로 검색..."
										maxHeight="350px"
										onclose={handleClose}
										onselected={undefined}
									/>

									{#if errors.member_ids}
										<div class="label">
											<span class="label-text-alt text-error">{errors.member_ids}</span>
										</div>
									{/if}
								</div>
							</div>
						</div>
					</form>
				</div>

				<!-- 모달 액션 -->
				<div class="modal-action mt-6">
					<button class="btn btn-ghost" onclick={handleClose} disabled={saving}> 취소 </button>
					<button
						class="btn btn-primary"
						onclick={handleSubmit}
						disabled={saving}
						class:loading={saving}
					>
						{#if saving}
							<Icon data={faSpinner} class="animate-spin" />
						{:else}
							<Icon data={faSave} />
						{/if}
						{isEditMode ? '수정' : '생성'}
					</button>
				</div>
			{/if}
		</div>
		<div
			class="modal-backdrop"
			onclick={handleClose}
			role="button"
			tabindex="-1"
			onkeydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					handleClose();
				}
			}}
		></div>
	</div>
{/if}
