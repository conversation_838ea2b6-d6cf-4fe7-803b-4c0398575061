<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import type { NotificationGroup, GroupMember } from '$lib/types/notification';
	import type { Member } from '$lib/types/types';
	import {
		getGroup,
		addGroupMembers,
		removeGroupMembers
	} from '$lib/stores/notification/groupStore';
	import MemberSelector from '$components/Notification/Group/MemberSelector.svelte';

	// Font Awesome 아이콘 import
	import Icon from 'svelte-awesome';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faCalendar } from '@fortawesome/free-solid-svg-icons/faCalendar';
	import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';

	interface Props {
		show: boolean;
		groupId: number | null;
		onclose?: () => void;
		onupdated?: () => void;
		embedded?: boolean;
	}

	let { show, groupId, onclose, onupdated, embedded = false } = $props();

	let user: User = getUser();
	let loading = $state(false);
	let group = $state<NotificationGroup | null>(null);
	let members = $state<GroupMember[]>([]);
	let showMemberSelector = $state(false);
	let memberToRemove = $state<GroupMember | null>(null);
	let showRemoveConfirm = $state(false);

	// 그룹 상세 정보 로드
	async function loadGroup() {
		if (!groupId) return;

		loading = true;
		try {
			const result = await getGroup(user, groupId);
			if (result) {
				group = result;
				members = result.members || [];
			}
		} catch (error) {
			console.error('그룹 상세 정보 로드 실패:', error);
		} finally {
			loading = false;
		}
	}

	// 그룹 ID 변경 시 데이터 로드 (중복 요청 방지)
	let lastLoadedGroupId: number | null = null;
	$effect(() => {
		if (show && groupId && groupId !== lastLoadedGroupId) {
			lastLoadedGroupId = groupId;
			loadGroup();
		}
	});

	// 모달 닫기
	function handleClose() {
		onclose?.();
	}

	// 멤버 추가 모달 열기
	function openMemberSelector() {
		showMemberSelector = true;
	}

	// 멤버 추가 모달 닫기
	function closeMemberSelector() {
		showMemberSelector = false;
	}

	// 멤버 추가 처리
	async function handleMemberAdded(event: { members: GroupMember[] }) {
		if (!groupId) return;

		const memberIds = event.members.map((m) => m.user.id);
		const success = await addGroupMembers(user, groupId, memberIds);

		if (success) {
			closeMemberSelector();
			await loadGroup(); // 멤버 목록 새로고침
			onupdated?.(); // 부모 컴포넌트에 업데이트 알림
		}
	}

	// 멤버 제거 확인 모달 열기
	function openRemoveConfirm(member: GroupMember) {
		memberToRemove = member;
		showRemoveConfirm = true;
	}

	// 멤버 제거 확인 모달 닫기
	function closeRemoveConfirm() {
		memberToRemove = null;
		showRemoveConfirm = false;
	}

	// 멤버 제거 처리
	async function handleRemoveMember() {
		if (!groupId || !memberToRemove) return;

		const success = await removeGroupMembers(user, groupId, memberToRemove.user.id);

		if (success) {
			closeRemoveConfirm();
			await loadGroup(); // 멤버 목록 새로고침
			onupdated?.(); // 부모 컴포넌트에 업데이트 알림
		}
	}

	// ESC 키로 모달 닫기
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if show}
	{#if embedded}
		<!-- Embedded 모드 (모달 없이 직접 렌더링) -->
		<div class="h-full flex flex-col">
			<!-- 헤더 -->
			<div class="flex justify-between items-center mb-4 flex-shrink-0">
				<div>
					<h3 class="font-bold text-lg text-base-content">그룹 상세 정보</h3>
					<p class="text-sm text-base-content/70 mt-1">그룹 정보 및 멤버 관리</p>
				</div>
			</div>

			{#if loading}
				<!-- 로딩 상태 -->
				<div class="flex justify-center items-center py-12">
					<span class="loading loading-spinner loading-lg"></span>
					<span class="ml-2">그룹 정보를 불러오는 중...</span>
				</div>
			{:else if group}
				<!-- 그룹 정보 -->
				<div class="flex-1 overflow-y-auto space-y-4">
					<!-- 그룹 기본 정보 -->
					<div class="card bg-base-100 border border-base-300">
						<div class="card-body p-4">
							<h4 class="card-title text-base">
								<Icon data={faUsers} class="w-4 h-4" />
								기본 정보
							</h4>
							<div class="space-y-2">
								<div>
									<label class="text-xs font-medium text-base-content/70">그룹명</label>
									<p class="text-sm font-medium">{group.name}</p>
								</div>
								<div>
									<label class="text-xs font-medium text-base-content/70">설명</label>
									<p class="text-xs text-base-content/80">
										{group.description || '설명이 없습니다'}
									</p>
								</div>
								<div>
									<label class="text-xs font-medium text-base-content/70">생성일</label>
									<p class="text-xs flex items-center gap-1">
										<Icon data={faCalendar} class="w-3 h-3" />
										{new Date(group.created_at).toLocaleDateString('ko-KR')}
									</p>
								</div>
							</div>
						</div>
					</div>

					<!-- 멤버 통계 -->
					<div class="grid grid-cols-3 gap-2">
						<div class="stat bg-base-100 border border-base-300 rounded-lg p-3">
							<div class="stat-title text-xs">총 멤버</div>
							<div class="stat-value text-lg text-primary">{members.length}</div>
						</div>
						<div class="stat bg-base-100 border border-base-300 rounded-lg p-3">
							<div class="stat-title text-xs">활성</div>
							<div class="stat-value text-lg text-success">
								{members.filter((m) => m.user.status === 1).length}
							</div>
						</div>
						<div class="stat bg-base-100 border border-base-300 rounded-lg p-3">
							<div class="stat-title text-xs">비활성</div>
							<div class="stat-value text-lg text-warning">
								{members.filter((m) => m.user.status !== 1).length}
							</div>
						</div>
					</div>

					<!-- 멤버 목록 -->
					<div class="card bg-base-100 border border-base-300 flex-1">
						<div class="card-body p-4">
							<div class="flex justify-between items-center mb-3">
								<h4 class="card-title text-base">
									<Icon data={faUsers} class="w-4 h-4" />
									멤버 목록 ({members.length}명)
								</h4>
								<button class="btn btn-primary btn-xs" onclick={openMemberSelector}>
									<Icon data={faPlus} class="w-3 h-3" />
									추가
								</button>
							</div>

							{#if members.length > 0}
								<div class="overflow-y-auto max-h-64">
									<table class="table table-zebra table-xs w-full">
										<thead class="sticky top-0 bg-base-100">
											<tr>
												<th class="w-8 text-center">#</th>
												<th>이름</th>
												<th>아이디</th>
												<th class="text-center w-24">상태</th>
												<th class="text-center w-12">작업</th>
											</tr>
										</thead>
										<tbody>
											{#each members as member, index}
												<tr class="hover:bg-base-200">
													<td class="text-center">{index + 1}</td>
													<td>
														<div class="font-medium text-xs">{member.user.name}</div>
													</td>
													<td>
														<div class="text-xs text-base-content/70 truncate max-w-[120px]">
															{member.user.username}
														</div>
													</td>
													<td class="text-center">
														{#if member.user.status === 1}
															<div class="badge badge-success badge-xs">활성</div>
														{:else}
															<div class="badge badge-warning badge-xs">비활성</div>
														{/if}
													</td>
													<td class="text-center">
														<button
															class="btn btn-ghost btn-xs text-error p-1"
															onclick={() => openRemoveConfirm(member)}
															title="그룹에서 제거"
														>
															<Icon data={faTrash} class="w-3 h-3" />
														</button>
													</td>
												</tr>
											{/each}
										</tbody>
									</table>
								</div>
							{:else}
								<div class="text-center py-6">
									<Icon data={faUsers} class="w-8 h-8 text-base-content/30 mb-2" />
									<p class="text-xs text-base-content/70 mb-2">아직 멤버가 없습니다</p>
									<button class="btn btn-primary btn-xs" onclick={openMemberSelector}>
										<Icon data={faPlus} class="w-3 h-3" />
										멤버 추가
									</button>
								</div>
							{/if}
						</div>
					</div>
				</div>
			{:else}
				<!-- 에러 상태 -->
				<div class="text-center py-12">
					<p class="text-error text-sm">그룹 정보를 불러올 수 없습니다</p>
					<button class="btn btn-ghost btn-xs mt-2" onclick={loadGroup}> 다시 시도 </button>
				</div>
			{/if}
		</div>
	{:else}
		<!-- 모달 모드 -->
		<div class="modal modal-open">
			<div class="modal-box max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
				<!-- 모달 헤더 -->
				<div class="flex justify-between items-center mb-6 flex-shrink-0">
					<div>
						<h3 class="font-bold text-xl text-base-content">그룹 상세 정보</h3>
						<p class="text-sm text-base-content/70 mt-1">그룹 정보 및 멤버 관리</p>
					</div>
					<button class="btn btn-ghost btn-sm btn-circle" onclick={handleClose} title="닫기">
						<Icon data={faTimes} />
					</button>
				</div>

				{#if loading}
					<!-- 로딩 상태 -->
					<div class="flex justify-center items-center py-12">
						<span class="loading loading-spinner loading-lg"></span>
						<span class="ml-2">그룹 정보를 불러오는 중...</span>
					</div>
				{:else if group}
					<!-- 그룹 정보 -->
					<div class="flex-1 overflow-y-auto">
						<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
							<!-- 그룹 기본 정보 -->
							<div class="card bg-base-100 border border-base-300">
								<div class="card-body">
									<h4 class="card-title text-lg">
										<Icon data={faUsers} class="w-5 h-5" />
										기본 정보
									</h4>
									<div class="space-y-3">
										<div>
											<label class="text-sm font-medium text-base-content/70">그룹명</label>
											<p class="text-base font-medium">{group.name}</p>
										</div>
										<div>
											<label class="text-sm font-medium text-base-content/70">설명</label>
											<p class="text-sm text-base-content/80">
												{group.description || '설명이 없습니다'}
											</p>
										</div>
										<div>
											<label class="text-sm font-medium text-base-content/70">생성일</label>
											<p class="text-sm flex items-center gap-1">
												<Icon data={faCalendar} class="w-4 h-4" />
												{new Date(group.created_at).toLocaleString('ko-KR')}
											</p>
										</div>
									</div>
								</div>
							</div>

							<!-- 멤버 통계 -->
							<div class="card bg-base-100 border border-base-300">
								<div class="card-body">
									<h4 class="card-title text-lg">
										<Icon data={faUser} class="w-5 h-5" />
										멤버 통계
									</h4>
									<div class="space-y-3">
										<div class="stat">
											<div class="stat-title text-sm">총 멤버 수</div>
											<div class="stat-value text-2xl text-primary">{members.length}명</div>
										</div>
										<div class="stat">
											<div class="stat-title text-sm">활성 멤버</div>
											<div class="stat-value text-lg text-success">
												{members.filter((m) => m.user.status === 1).length}명
											</div>
										</div>
										<div class="stat">
											<div class="stat-title text-sm">비활성 멤버</div>
											<div class="stat-value text-lg text-warning">
												{members.filter((m) => m.user.status !== 1).length}명
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 멤버 목록 -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<div class="flex justify-between items-center mb-4">
									<h4 class="card-title text-lg">
										<Icon data={faUsers} class="w-5 h-5" />
										멤버 목록 ({members.length}명)
									</h4>
									<button class="btn btn-primary btn-sm" onclick={openMemberSelector}>
										<Icon data={faPlus} />
										멤버 추가
									</button>
								</div>

								{#if members.length > 0}
									<div class="overflow-x-auto">
										<table class="table table-zebra w-full">
											<thead>
												<tr>
													<th class="w-16">#</th>
													<th>이름</th>
													<th>이메일</th>
													<th>부서</th>
													<th class="text-center w-20">상태</th>
													<th class="text-center w-24">작업</th>
												</tr>
											</thead>
											<tbody>
												{#each members as member, index}
													<tr class="hover:bg-base-200">
														<td class="text-center">{index + 1}</td>
														<td>
															<div class="font-medium">{member.name}</div>
														</td>
														<td>
															<div class="text-sm text-base-content/70">
																{member.email}
															</div>
														</td>
														<td>
															<div class="text-sm">
																{member.department || '-'}
															</div>
														</td>
														<td class="text-center">
															{#if member.status === 1}
																<div class="badge badge-success badge-sm">활성</div>
															{:else}
																<div class="badge badge-warning badge-sm">비활성</div>
															{/if}
														</td>
														<td class="text-center">
															<button
																class="btn btn-ghost btn-xs text-error"
																onclick={() => openRemoveConfirm(member)}
																title="그룹에서 제거"
															>
																<Icon data={faTrash} />
															</button>
														</td>
													</tr>
												{/each}
											</tbody>
										</table>
									</div>
								{:else}
									<div class="text-center py-8">
										<Icon data={faUsers} class="w-12 h-12 text-base-content/30 mb-3" />
										<p class="text-base-content/70">아직 멤버가 없습니다</p>
										<p class="text-sm text-base-content/50 mb-4">
											멤버를 추가하여 그룹을 구성하세요
										</p>
										<button class="btn btn-primary btn-sm" onclick={openMemberSelector}>
											<Icon data={faPlus} />
											첫 번째 멤버 추가
										</button>
									</div>
								{/if}
							</div>
						</div>
					</div>
				{:else}
					<!-- 에러 상태 -->
					<div class="text-center py-12">
						<p class="text-error">그룹 정보를 불러올 수 없습니다</p>
						<button class="btn btn-ghost btn-sm mt-2" onclick={loadGroup}> 다시 시도 </button>
					</div>
				{/if}

				<!-- 모달 액션 -->
				<div class="modal-action flex-shrink-0 mt-6">
					<button class="btn btn-ghost" onclick={handleClose}>닫기</button>
				</div>
			</div>

			<!-- 모달 배경 -->
			<div
				class="modal-backdrop"
				onclick={handleClose}
				role="button"
				tabindex="-1"
				onkeydown={(e) => {
					if (e.key === 'Enter' || e.key === ' ') {
						e.preventDefault();
						handleClose();
					}
				}}
			></div>
		</div>
	{/if}
{/if}

{#if showMemberSelector}
	<!-- 멤버 선택 모달 -->
	<MemberSelector
		show={showMemberSelector}
		excludeUserIds={members.map((m) => m.user.id)}
		excludeGroupId={groupId}
		modalMode={true}
		onclose={closeMemberSelector}
		onselected={handleMemberAdded}
		onselectionchange={undefined}
	/>
{/if}

<!-- 멤버 제거 확인 모달 -->
{#if showRemoveConfirm && memberToRemove}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">멤버 제거 확인</h3>
			<p class="mb-4">
				<strong>"{memberToRemove.user.name}"</strong> 님을 이 그룹에서 제거하시겠습니까?
			</p>
			<p class="text-sm text-warning mb-6">
				이 작업은 되돌릴 수 있으며, 해당 직원은 언제든지 다시 그룹에 추가할 수 있습니다.
			</p>
			<div class="modal-action">
				<button class="btn btn-ghost" onclick={closeRemoveConfirm}>취소</button>
				<button class="btn btn-warning" onclick={handleRemoveMember}>
					<Icon data={faTrash} />
					제거
				</button>
			</div>
		</div>
		<div
			class="modal-backdrop"
			onclick={closeRemoveConfirm}
			role="button"
			tabindex="-1"
			onkeydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					closeRemoveConfirm();
				}
			}}
		></div>
	</div>
{/if}
