<script lang="ts">
	import { onMount } from 'svelte';
	import type { User } from '$lib/User';
	import type { NotificationTemplate } from '$lib/types/notification';
	import { getAllTemplates, incrementTemplateUsage } from '$stores/notification/templateStore';
	import PriorityBadge from '$components/Notification/Common/PriorityBadge.svelte';

	import Icon from 'svelte-awesome';
	import { faFileText } from '@fortawesome/free-solid-svg-icons/faFileText';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';

	interface Props {
		user: User;
		onselect?: (template: NotificationTemplate) => void;
	}

	let { user, onselect }: Props = $props();

	// 상태 관리
	let templates = $state<NotificationTemplate[]>([]);
	let filteredTemplates = $state<NotificationTemplate[]>([]);
	let isLoading = $state(false);
	let searchQuery = $state('');
	let isExpanded = $state(false);

	// 템플릿 목록 로드
	async function loadTemplateList() {
		isLoading = true;
		try {
			const templateList = await getAllTemplates(user);
			templates = templateList.items;
			filteredTemplates = templateList.items;
		} catch (error) {
			console.error('템플릿 목록 로드 실패:', error);
			templates = [];
			filteredTemplates = [];
		} finally {
			isLoading = false;
		}
	}

	// 검색 필터링
	function filterTemplates() {
		if (!searchQuery.trim()) {
			filteredTemplates = templates;
		} else {
			const query = searchQuery.toLowerCase();
			filteredTemplates = templates.filter(
				(template) =>
					template.name.toLowerCase().includes(query) ||
					template.title.toLowerCase().includes(query) ||
					template.content.toLowerCase().includes(query)
			);
		}
	}

	// 템플릿 선택 처리
	async function handleTemplateSelect(template: NotificationTemplate) {
		try {
			// 사용 횟수 증가 (백그라운드)
			await incrementTemplateUsage(user, template.id);

			// 템플릿 선택 이벤트 발생
			onselect?.(template);

			// 선택 후 접기
			isExpanded = false;
			searchQuery = '';
		} catch (error) {
			console.error('템플릿 선택 처리 실패:', error);
		}
	}

	// 검색어 초기화
	function clearSearch() {
		searchQuery = '';
		filterTemplates();
	}

	// 템플릿 섹션 토글
	function toggleExpanded() {
		isExpanded = !isExpanded;
		if (isExpanded && templates.length === 0 && !hasLoaded) {
			hasLoaded = true;
			loadTemplateList();
		}
	}

	// 검색어 변경 시 필터링
	$effect(() => {
		filterTemplates();
	});

	// 컴포넌트 마운트 시 템플릿 목록 로드 (한 번만 실행)
	let hasLoaded = $state(false);
	onMount(() => {
		if (!hasLoaded) {
			hasLoaded = true;
			loadTemplateList();
		}
	});
</script>

<div class="template-selector">
	<!-- 템플릿 선택 헤더 -->
	<div class="flex items-center justify-between mb-4">
		<div class="flex items-center gap-2">
			<Icon data={faFileText} class="w-5 h-5 text-primary" />
			<span class="font-medium">템플릿 선택</span>
			{#if templates.length > 0}
				<span class="badge badge-outline badge-sm">{templates.length}개</span>
			{/if}
		</div>
		<button
			type="button"
			class="btn btn-ghost btn-sm"
			onclick={toggleExpanded}
			disabled={isLoading}
		>
			{isExpanded ? '접기' : '펼치기'}
		</button>
	</div>

	{#if isExpanded}
		<div class="template-content space-y-4">
			<!-- 검색 영역 -->
			{#if templates.length > 0}
				<div class="form-control">
					<div class="input-group">
						<div class="relative flex-1">
							<input
								type="text"
								placeholder="템플릿 검색..."
								class="input input-bordered w-full pr-10"
								bind:value={searchQuery}
							/>
							{#if searchQuery}
								<button
									type="button"
									class="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-xs"
									onclick={() => {
										clearSearch();
									}}
								>
									<Icon data={faTimes} class="w-3 h-3" />
								</button>
							{:else}
								<Icon
									data={faSearch}
									class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-base-content/50"
								/>
							{/if}
						</div>
					</div>
				</div>
			{/if}

			<!-- 템플릿 목록 -->
			<div class="template-list">
				{#if isLoading}
					<div class="flex items-center justify-center py-8">
						<span class="loading loading-spinner loading-md"></span>
						<span class="ml-2">템플릿 로딩 중...</span>
					</div>
				{:else if filteredTemplates.length === 0}
					<div class="text-center py-8 text-base-content/60">
						{#if searchQuery}
							<p>검색 결과가 없습니다.</p>
							<button type="button" class="btn btn-ghost btn-sm mt-2" onclick={() => {
								clearSearch();
							}}>
								검색 초기화
							</button>
						{:else if templates.length === 0}
							<p>저장된 템플릿이 없습니다.</p>
							<p class="text-sm mt-1">
								템플릿 관리에서 자주 사용하는 알림을 템플릿으로 저장하세요.
							</p>
						{:else}
							<p>템플릿이 없습니다.</p>
						{/if}
					</div>
				{:else}
					<div class="template-grid-container">
						<div class="template-grid">
							{#each filteredTemplates as template (template.id)}
								<div
									class="template-card"
									onclick={() => handleTemplateSelect(template)}
									role="button"
									tabindex="0"
									onkeydown={(e) => {
										if (e.key === 'Enter' || e.key === ' ') {
											e.preventDefault();
											handleTemplateSelect(template);
										}
									}}
								>
									<div class="template-card-body">
										<!-- 템플릿 헤더 -->
										<div class="template-header">
											<div class="template-info">
												<h4 class="template-name" title={template.name}>
													{template.name}
												</h4>
												<p class="template-title" title={template.title}>
													{template.title}
												</p>
											</div>
											<div class="template-badges">
												<PriorityBadge priority={template.priority} size="sm" />
												{#if template.usage_count > 0}
													<span class="usage-badge">
														{template.usage_count}
													</span>
												{/if}
											</div>
										</div>

										<!-- 템플릿 내용 미리보기 -->
										<div class="template-content">
											{template.content}
										</div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.template-selector {
		border: 1px solid hsl(var(--bc) / 0.2);
		border-radius: 0.5rem;
		padding: 1rem;
		background-color: hsl(var(--b2));
	}

	/* 템플릿 그리드 컨테이너 */
	.template-selector .template-grid-container {
		overflow-x: hidden !important;
		overflow-y: auto !important;
		max-height: 160px !important;
		padding: 4px !important;
		border: 1px solid rgba(0, 0, 0, 0.1) !important;
		border-radius: 8px !important;
		background: rgba(255, 255, 255, 0.02) !important;
	}

	.template-selector .template-grid {
		display: grid !important;
		grid-template-columns: repeat(3, 1fr) !important;
		gap: 16px !important;
		padding: 8px !important;
	}

	/* 템플릿 카드 스타일 */
	.template-selector .template-card {
		background: hsl(var(--b1)) !important;
		border: 2px solid #e5e7eb !important;
		border-radius: 12px !important;
		cursor: pointer !important;
		transition: all 0.2s ease !important;
		height: 140px !important;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
		position: relative !important;
	}

	.template-selector .template-card:hover {
		border-color: hsl(var(--p)) !important;
		box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
		transform: translateY(-2px) !important;
		background: hsl(var(--b1)) !important;
	}

	.template-selector .template-card:active {
		transform: translateY(0) !important;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
	}

	.template-selector .template-card:focus {
		outline: 2px solid hsl(var(--p)) !important;
		outline-offset: 2px !important;
		border-color: hsl(var(--p)) !important;
	}

	.template-selector .template-card-body {
		padding: 14px !important;
		height: 100% !important;
		display: flex !important;
		flex-direction: column !important;
	}

	/* 템플릿 헤더 */
	.template-selector .template-header {
		display: flex !important;
		justify-content: space-between !important;
		align-items: flex-start !important;
		margin-bottom: 10px !important;
		gap: 8px !important;
	}

	.template-selector .template-info {
		flex: 1 !important;
		min-width: 0 !important;
	}

	.template-selector .template-name {
		font-size: 13px !important;
		font-weight: 600 !important;
		color: hsl(var(--bc)) !important;
		margin: 0 !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		line-height: 1.3 !important;
	}

	.template-selector .template-title {
		font-size: 11px !important;
		color: hsl(var(--bc) / 0.7) !important;
		margin: 4px 0 0 0 !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		line-height: 1.3 !important;
	}

	.template-selector .template-badges {
		display: flex !important;
		align-items: center !important;
		gap: 4px !important;
		flex-shrink: 0 !important;
	}

	.template-selector .usage-badge {
		font-size: 9px !important;
		background: rgba(0, 0, 0, 0.1) !important;
		color: hsl(var(--bc) / 0.8) !important;
		padding: 2px 5px !important;
		border-radius: 6px !important;
		line-height: 1 !important;
		border: 1px solid rgba(0, 0, 0, 0.1) !important;
	}

	/* 템플릿 내용 */
	.template-selector .template-content {
		flex: 1 !important;
		font-size: 10px !important;
		color: hsl(var(--bc) / 0.65) !important;
		line-height: 1.4 !important;
		overflow: hidden !important;
		display: -webkit-box !important;
		-webkit-line-clamp: 3 !important;
		-webkit-box-orient: vertical !important;
	}

	/* 스크롤바 스타일링 - 더 강력한 선택자 */
	.template-selector .template-grid-container::-webkit-scrollbar {
		width: 8px !important;
		background: transparent !important;
	}

	.template-selector .template-grid-container::-webkit-scrollbar-track {
		background: #f1f5f9 !important;
		border-radius: 4px !important;
		margin: 4px 0 !important;
	}

	.template-selector .template-grid-container::-webkit-scrollbar-thumb {
		background: #cbd5e1 !important;
		border-radius: 4px !important;
		border: 1px solid #e2e8f0 !important;
	}

	.template-selector .template-grid-container::-webkit-scrollbar-thumb:hover {
		background: #94a3b8 !important;
	}

	.template-selector .template-grid-container::-webkit-scrollbar-corner {
		background: transparent !important;
	}

	/* 반응형 디자인 */
	@media (max-width: 768px) {
		.template-selector {
			padding: 0.75rem !important;
		}

		.template-selector .template-grid {
			grid-template-columns: repeat(2, 1fr) !important;
			gap: 12px !important;
		}

		.template-selector .template-card {
			height: 130px !important;
		}

		.template-selector .template-card-body {
			padding: 12px !important;
		}

		.template-selector .template-grid-container {
			max-height: 280px !important;
		}
	}

	@media (max-width: 480px) {
		.template-selector .template-grid {
			grid-template-columns: 1fr !important;
			gap: 10px !important;
		}

		.template-selector .template-card {
			height: 110px !important;
		}

		.template-selector .template-card-body {
			padding: 10px !important;
		}

		.template-selector .template-content {
			-webkit-line-clamp: 2 !important;
		}

		.template-selector .template-grid-container {
			max-height: 240px !important;
		}
	}
</style>
