<script lang="ts">
	import type { User } from '$lib/User';
	import type { Member } from '$lib/types/types';
	import type { NotificationGroup, NotificationTargetType } from '$lib/types/notification';
	import { MemberService, MemberUtils } from '$lib/services/notification/MemberService';
	import { getAllGroups } from '$lib/stores/notification/groupStore';
	import { executeMessage } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';
	import { faUserFriends } from '@fortawesome/free-solid-svg-icons/faUserFriends';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons/faExclamationCircle';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';

	interface Props {
		user: User;
		targetType: NotificationTargetType;
		selectedTargetId?: number;
		onselect?: (event: { targetId: number; targetName: string; recipientCount: number; memberNames?: string[] }) => void;
		ontargettypechange?: (targetType: NotificationTargetType) => void;
	}

	let { user, targetType, selectedTargetId, onselect, ontargettypechange }: Props = $props();

	// 상태 관리
	let groups = $state<NotificationGroup[]>([]);
	let members = $state<Member[]>([]);
	let filteredGroups = $state<NotificationGroup[]>([]);
	let filteredMembers = $state<Member[]>([]);
	let searchQuery = $state('');
	let isLoading = $state(false);
	let error = $state<string | null>(null);

	// 검색 디바운싱을 위한 타이머
	let searchTimeout: NodeJS.Timeout;

	// 대상 타입 옵션
	const targetTypeOptions = [
		{
			value: 'all' as NotificationTargetType,
			text: '전체 직원',
			icon: faUsers,
			description: '모든 활성 직원에게 전송',
			color: 'btn-success'
		},
		{
			value: 'group' as NotificationTargetType,
			text: '특정 그룹',
			icon: faUserFriends,
			description: '선택한 그룹의 멤버들에게 전송',
			color: 'btn-warning'
		},
		{
			value: 'individual' as NotificationTargetType,
			text: '개별 직원',
			icon: faUser,
			description: '선택한 개별 직원에게 전송',
			color: 'btn-info'
		}
	];

	// 대상 타입 변경 시 데이터 로드 (초기 로드 포함)
	$effect(() => {
		if (targetType) {
			loadData();
		}
	});

	// 검색어 변경 시 필터링
	$effect(() => {
		if (searchQuery !== undefined) {
			clearTimeout(searchTimeout);
			searchTimeout = setTimeout(() => {
				filterData();
			}, 300);
		}
	});

	/**
	 * 데이터 로드
	 */
	async function loadData() {
		if (targetType === 'all') {
			// 전체 직원의 경우 별도 로딩 불필요
			return;
		}

		isLoading = true;
		error = null;

		try {
			if (targetType === 'group') {
				groups = await getAllGroups(user);
				filteredGroups = groups;
			} else if (targetType === 'individual') {
				members = await MemberService.getActiveMembers(user);
				filteredMembers = MemberUtils.sortMembers(members, 'name');

				if (members.length === 0) {
					console.warn('직원 데이터가 비어있습니다.');
				}
			}
		} catch (err) {
			console.error('데이터 로드 실패:', err);
			const errorMessage = err instanceof Error ? err.message : String(err);

			error = `데이터를 불러오는 중 오류가 발생했습니다: ${errorMessage}`;
			await executeMessage(error, 'error');
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 검색 필터링
	 */
	function filterData() {
		if (targetType === 'group') {
			if (!searchQuery.trim()) {
				filteredGroups = groups;
			} else {
				const query = searchQuery.toLowerCase().trim();
				filteredGroups = groups.filter(
					(group) =>
						group.name.toLowerCase().includes(query) ||
						group.description?.toLowerCase().includes(query)
				);
			}
		} else if (targetType === 'individual') {
			if (!searchQuery.trim()) {
				filteredMembers = members;
			} else {
				filteredMembers = MemberUtils.filterMembers(members, searchQuery);
			}
		}
	}

	/**
	 * 그룹 선택 핸들러
	 */
	function handleGroupSelect(group: NotificationGroup) {
		// 그룹 멤버 이름들을 수집
		const memberNames = group.members ? group.members.map(member => member.user?.name || '알 수 없음').filter(name => name !== '알 수 없음') : [];

		// 그룹 멤버 ID들을 수집 (실제 알림 전송 대상)
		const memberIds = group.members ? group.members.map(member => member.user?.id).filter(id => id !== undefined) : [];

		onselect?.({
			targetId: group.id,
			targetName: group.name,
			recipientCount: group.members_count,
			memberNames: memberNames, // 멤버 이름 목록 추가
			memberIds: memberIds // 멤버 ID 목록 추가 (실제 전송 대상)
		});
	}

	/**
	 * 개별 직원 선택 핸들러
	 */
	function handleMemberSelect(member: Member) {
		onselect?.({
			targetId: member.id,
			targetName: MemberUtils.getDisplayName(member),
			recipientCount: 1
		});
	}

	/**
	 * 검색어 초기화
	 */
	function clearSearch() {
		searchQuery = '';
	}

	/**
	 * 대상 타입 변경 핸들러
	 */
	function handleTargetTypeChange(newTargetType: NotificationTargetType) {
		ontargettypechange?.(newTargetType);
	}

	// 전체 직원 수 계산 (파생 상태)
	const totalActiveMembers = $derived.by(() => {
		if (targetType === 'individual') {
			return members.length;
		}
		return 0;
	});

	// 검색 결과 수 계산 (파생 상태)
	const searchResultCount = $derived.by(() => {
		if (targetType === 'group') {
			return filteredGroups.length;
		} else if (targetType === 'individual') {
			return filteredMembers.length;
		}
		return 0;
	});
</script>

<div class="space-y-6">
	<!-- 대상 타입 선택 버튼 -->
	<div class="form-control">
		<div class="label">
			<span class="label-text font-medium">전송 대상 선택</span>
		</div>
		<div class="grid grid-cols-1 md:grid-cols-3 gap-3">
			{#each targetTypeOptions as option}
				<button
					type="button"
					class="btn {option.color} {targetType === option.value
						? ''
						: 'btn-outline'} h-auto py-4 px-4 flex-col gap-2"
					onclick={() => handleTargetTypeChange(option.value)}
				>
					<Icon data={option.icon} class="w-6 h-6" />
					<div class="text-center">
						<div class="font-medium">{option.text}</div>
						<div class="text-xs opacity-80">{option.description}</div>
					</div>
				</button>
			{/each}
		</div>
	</div>

	{#if targetType === 'all'}
		<!-- 전체 직원 선택 시 정보 표시 -->
		<div class="alert alert-success">
			<Icon data={faUsers} class="w-5 h-5" />
			<div>
				<div class="font-medium">전체 직원에게 전송</div>
				<div class="text-sm">모든 활성 직원에게 알림이 전송됩니다.</div>
			</div>
		</div>
	{:else}
		<!-- 그룹 또는 개별 직원 선택 -->
		<div class="space-y-4">
			<!-- 검색 입력 -->
			<div class="form-control">
				<div class="input-group">
					<input
						type="text"
						placeholder={targetType === 'group'
							? '그룹명 또는 설명으로 검색...'
							: '이름, 사용자명, 이메일로 검색...'}
						class="input input-bordered flex-1"
						bind:value={searchQuery}
						disabled={isLoading}
					/>
					<button
						type="button"
						class="btn btn-square btn-outline"
						onclick={clearSearch}
						disabled={isLoading || !searchQuery}
						title="검색어 지우기"
					>
						{#if searchQuery}
							<Icon data={faTimes} class="w-4 h-4" />
						{:else}
							<Icon data={faSearch} class="w-4 h-4" />
						{/if}
					</button>
				</div>
				{#if searchQuery && searchResultCount() > 0}
					<div class="label">
						<span class="label-text-alt">검색 결과: {searchResultCount()}개</span>
					</div>
				{/if}
			</div>

			<!-- 로딩 상태 -->
			{#if isLoading}
				<div class="flex items-center justify-center py-8">
					<Icon data={faSpinner} class="w-6 h-6 animate-spin text-primary" />
					<span class="ml-2">데이터를 불러오는 중...</span>
				</div>
			{:else if error}
				<!-- 에러 상태 -->
				<div class="alert alert-error">
					<Icon data={faExclamationCircle} class="w-5 h-5" />
					<span>{error}</span>
				</div>
			{:else if targetType === 'group'}
				<!-- 그룹 선택 -->
				{#if filteredGroups.length === 0}
					<div class="alert alert-warning">
						<Icon data={faExclamationCircle} class="w-5 h-5" />
						<span>
							{searchQuery ? '검색 결과가 없습니다.' : '사용 가능한 그룹이 없습니다.'}
						</span>
					</div>
				{:else}
					<div class="group-grid-container">
						<div class="group-grid">
							{#each filteredGroups as group (group.id)}
								<label
									for="group_{group.id}"
									class="group-card"
									class:selected={selectedTargetId === group.id}
								>
									<input
										id="group_{group.id}"
										type="radio"
										name="target_group"
										value={group.id}
										class="radio radio-primary group-radio"
										checked={selectedTargetId === group.id}
										onchange={() => handleGroupSelect(group)}
									/>
									<div class="group-content">
										<div class="group-header">
											<Icon data={faUsers} class="w-4 h-4 text-primary" />
											<span class="group-name" title={group.name}>{group.name}</span>
										</div>
										<div class="group-member-count">
											<span class="badge badge-outline badge-sm">
												{group.members_count}명
											</span>
										</div>
										{#if group.description}
											<p class="group-description" title={group.description}>
												{group.description}
											</p>
										{/if}
									</div>
								</label>
							{/each}
						</div>
					</div>
				{/if}
			{:else if targetType === 'individual'}
				<!-- 개별 직원 선택 -->
				{#if filteredMembers.length === 0}
					<div class="alert alert-warning">
						<Icon data={faExclamationCircle} class="w-5 h-5" />
						<span>
							{searchQuery ? '검색 결과가 없습니다.' : '활성 직원이 없습니다.'}
						</span>
					</div>
				{:else}
					<div class="mb-2">
						<span class="text-sm text-base-content/70">
							총 {totalActiveMembers}명의 활성 직원
							{#if searchQuery}
								중 {searchResultCount}명 표시
							{/if}
						</span>
					</div>
					<div class="space-y-2 max-h-64 overflow-y-auto">
						{#each filteredMembers as member (member.id)}
							<label
								for="member_{member.id}"
								class="label cursor-pointer flex items-center gap-3 p-3 rounded-lg border border-base-300 hover:border-primary transition-colors"
								class:border-primary={selectedTargetId === member.id}
								class:bg-primary-100={selectedTargetId === member.id}
							>
								<input
									id="member_{member.id}"
									type="radio"
									name="target_member"
									value={member.id}
									class="radio radio-primary"
									checked={selectedTargetId === member.id}
									onchange={() => handleMemberSelect(member)}
								/>
								<div class="flex-1">
									<div class="flex items-center gap-2 mb-1">
										<Icon data={faUser} class="w-4 h-4 text-primary" />
										<span class="font-medium">{MemberUtils.getDisplayName(member)}</span>
										{#if MemberUtils.isRecentlyActive(member)}
											<span class="badge badge-success badge-xs">최근 활동</span>
										{/if}
									</div>
									<div class="text-sm text-base-content/70 space-x-2">
										{#if member.part}
											<span>{member.part}</span>
										{/if}
										{#if member.position}
											<span>• {member.position}</span>
										{/if}
										{#if member.email}
											<span>• {member.email}</span>
										{/if}
									</div>
								</div>
							</label>
						{/each}
					</div>
				{/if}
			{/if}
		</div>
	{/if}
</div>

<style>
	/* 그룹 그리드 컨테이너 */
	.group-grid-container {
		max-height: 320px !important;
		overflow-y: auto !important;
		overflow-x: hidden !important;
		padding: 4px !important;
		border: 1px solid rgba(0, 0, 0, 0.1) !important;
		border-radius: 8px !important;
		background: rgba(255, 255, 255, 0.02) !important;
	}

	.group-grid {
		display: grid !important;
		grid-template-columns: repeat(3, 1fr) !important;
		gap: 16px !important;
		padding: 8px !important;
	}

	/* 그룹 카드 스타일 */
	.group-card {
		background: hsl(var(--b1)) !important;
		border: 2px solid #e5e7eb !important;
		border-radius: 12px !important;
		cursor: pointer !important;
		transition: all 0.2s ease !important;
		height: 140px !important;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
		position: relative !important;
		padding: 14px !important;
		display: flex !important;
		flex-direction: column !important;
	}

	.group-card:hover {
		border-color: hsl(var(--p)) !important;
		box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
		transform: translateY(-2px) !important;
	}

	.group-card.selected {
		border-color: hsl(var(--p)) !important;
		background: hsl(var(--p) / 0.1) !important;
		box-shadow: 0 4px 12px hsl(var(--p) / 0.2) !important;
	}

	.group-radio {
		position: absolute !important;
		top: 10px !important;
		right: 10px !important;
		z-index: 10 !important;
	}

	.group-content {
		flex: 1 !important;
		display: flex !important;
		flex-direction: column !important;
		gap: 8px !important;
		margin-top: 8px !important;
	}

	.group-header {
		display: flex !important;
		align-items: center !important;
		gap: 8px !important;
	}

	.group-name {
		font-weight: 600 !important;
		color: hsl(var(--bc)) !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		flex: 1 !important;
		font-size: 14px !important;
	}

	.group-member-count {
		display: flex !important;
		justify-content: flex-start !important;
	}

	.group-description {
		font-size: 11px !important;
		color: hsl(var(--bc) / 0.65) !important;
		line-height: 1.4 !important;
		overflow: hidden !important;
		display: -webkit-box !important;
		-webkit-line-clamp: 3 !important;
		-webkit-box-orient: vertical !important;
		margin: 0 !important;
		flex: 1 !important;
	}

	/* 그룹 그리드 스크롤바 스타일링 */
	.group-grid-container::-webkit-scrollbar {
		width: 8px !important;
	}

	.group-grid-container::-webkit-scrollbar-track {
		background: #f1f5f9 !important;
		border-radius: 4px !important;
		margin: 4px 0 !important;
	}

	.group-grid-container::-webkit-scrollbar-thumb {
		background: #cbd5e1 !important;
		border-radius: 4px !important;
		border: 1px solid #e2e8f0 !important;
	}

	.group-grid-container::-webkit-scrollbar-thumb:hover {
		background: #94a3b8 !important;
	}

	/* 스크롤바 스타일링 */
	.overflow-y-auto::-webkit-scrollbar {
		width: 6px;
	}

	.overflow-y-auto::-webkit-scrollbar-track {
		background: hsl(var(--b2));
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb {
		background: hsl(var(--bc) / 0.3);
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--bc) / 0.5);
	}

	/* 라디오 버튼 그룹 호버 효과 */
	.label.cursor-pointer:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 선택된 항목 애니메이션 */
	.label.cursor-pointer {
		transition: all 0.2s ease;
	}

	/* 검색 입력 그룹 스타일링 */
	.input-group {
		display: flex;
		width: 100%;
	}

	.input-group .input {
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-right: none;
	}

	.input-group .btn {
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}

	/* 반응형 디자인 */
	@media (max-width: 768px) {
		.group-grid {
			grid-template-columns: repeat(2, 1fr) !important;
			gap: 12px !important;
		}

		.group-card {
			height: 130px !important;
			padding: 12px !important;
		}

		.group-grid-container {
			max-height: 280px !important;
		}
	}

	@media (max-width: 480px) {
		.group-grid {
			grid-template-columns: 1fr !important;
			gap: 10px !important;
		}

		.group-card {
			height: 110px !important;
			padding: 10px !important;
		}

		.group-description {
			-webkit-line-clamp: 2 !important;
		}

		.group-grid-container {
			max-height: 240px !important;
		}
	}
</style>
