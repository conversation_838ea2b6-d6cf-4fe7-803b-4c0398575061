import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
	send,
	validateFormData,
	estimateDeliveryTime,
	createNotificationError,
	isNotificationError
} from '../../../../services/notification/NotificationService';

// Mock dependencies
const NotificationService = { send, validateFormData, estimateDeliveryTime };
vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: {
		post: vi.fn()
	}
}));

vi.mock('$lib/Functions', () => ({
	handleCatch: vi.fn(),
	executeMessage: vi.fn()
}));

describe('NotificationService - 알림 전송 처리', () => {
	const mockUser = {
		id: 1,
		name: '테스트 사용자',
		role: 'Admin'
	};

	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('폼 유효성 검사', () => {
		it('유효한 데이터는 검증을 통과한다', () => {
			const validFormData = {
				title: '유효한 제목',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const,
				action_url: 'https://example.com'
			};

			const result = NotificationService.validateFormData(validFormData);

			expect(result.isValid).toBe(true);
			expect(Object.keys(result.errors)).toHaveLength(0);
		});

		it('제목이 없으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: '',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.title).toBe('제목을 입력해주세요.');
		});

		it('제목이 너무 짧으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: 'a',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.title).toBe('제목은 최소 2자 이상 입력해주세요.');
		});

		it('제목이 너무 길면 검증에 실패한다', () => {
			const invalidFormData = {
				title: 'a'.repeat(201),
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.title).toBe('제목은 200자 이내로 입력해주세요.');
		});

		it('내용이 없으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: '',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.content).toBe('내용을 입력해주세요.');
		});

		it('내용이 너무 짧으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: 'abc',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.content).toBe('내용은 최소 5자 이상 입력해주세요.');
		});

		it('잘못된 URL 형식은 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const,
				action_url: 'invalid-url'
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.action_url).toBe(
				'올바른 URL 형식을 입력해주세요. (예: https://example.com)'
			);
		});

		it('HTTP가 아닌 프로토콜은 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const,
				action_url: 'ftp://example.com'
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.action_url).toBe('HTTP 또는 HTTPS URL만 허용됩니다.');
		});

		it('그룹 대상 선택 시 target_id가 없으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'group' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.target_id).toBe('그룹을 선택해주세요.');
		});

		it('개별 직원 선택 시 target_id가 없으면 검증에 실패한다', () => {
			const invalidFormData = {
				title: '유효한 제목',
				content: '유효한 내용입니다.',
				priority: 'normal' as const,
				target_type: 'individual' as const
			};

			const result = NotificationService.validateFormData(invalidFormData);

			expect(result.isValid).toBe(false);
			expect(result.errors.target_id).toBe('직원을 선택해주세요.');
		});
	});

	describe('전송 예상 시간 계산', () => {
		it('10명 이하는 즉시 전송', () => {
			expect(NotificationService.estimateDeliveryTime(5)).toBe('즉시');
			expect(NotificationService.estimateDeliveryTime(10)).toBe('즉시');
		});

		it('50명 이하는 1-2초', () => {
			expect(NotificationService.estimateDeliveryTime(25)).toBe('1-2초');
			expect(NotificationService.estimateDeliveryTime(50)).toBe('1-2초');
		});

		it('100명 이하는 2-3초', () => {
			expect(NotificationService.estimateDeliveryTime(75)).toBe('2-3초');
			expect(NotificationService.estimateDeliveryTime(100)).toBe('2-3초');
		});

		it('100명 초과는 3초 이상', () => {
			expect(NotificationService.estimateDeliveryTime(150)).toBe('3초 이상');
			expect(NotificationService.estimateDeliveryTime(1000)).toBe('3초 이상');
		});
	});

	describe('알림 전송 API', () => {
		it('성공적인 전송은 처리 시간을 포함한다', async () => {
			const { authClient } = await import('../../../../services/AxiosBackend');

			vi.mocked(authClient.post).mockResolvedValue({
				status: 200,
				data: {
					success: true,
					message: '알림이 성공적으로 전송되었습니다.'
				}
			});

			const formData = {
				title: '테스트 알림',
				content: '테스트 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			const result = await NotificationService.send(mockUser, formData);

			expect(result.success).toBe(true);
			expect(result.processing_time).toBeDefined();
			expect(typeof result.processing_time).toBe('number');
		});

		it('3초 타임아웃이 적용된다', async () => {
			const { authClient } = await import('../../../../services/AxiosBackend');

			// 3초 이상 걸리는 응답 모킹
			vi.mocked(authClient.post).mockImplementation(
				() => new Promise((resolve) => setTimeout(resolve, 4000))
			);

			const formData = {
				title: '테스트 알림',
				content: '테스트 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			await expect(NotificationService.send(mockUser, formData)).rejects.toThrow(
				'알림 전송 요청이 시간 초과되었습니다. (3초)'
			);
		});

		it('서버 에러 시 적절한 에러를 발생시킨다', async () => {
			const { authClient } = await import('../../../../services/AxiosBackend');

			vi.mocked(authClient.post).mockResolvedValue({
				status: 400,
				data: {
					success: false,
					message: '서버 오류가 발생했습니다.'
				}
			});

			const formData = {
				title: '테스트 알림',
				content: '테스트 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			await expect(NotificationService.send(mockUser, formData)).rejects.toThrow(
				'서버 오류가 발생했습니다.'
			);
		});

		it('유효하지 않은 데이터는 전송 전에 검증된다', async () => {
			const formData = {
				title: '', // 빈 제목
				content: '테스트 내용입니다.',
				priority: 'normal' as const,
				target_type: 'all' as const
			};

			await expect(NotificationService.send(mockUser, formData)).rejects.toThrow(
				'제목을 입력해주세요.'
			);
		});
	});

	describe('NotificationError', () => {
		it('에러 코드와 상세 정보를 포함한다', () => {
			const error = createNotificationError('테스트 에러', 'TEST_ERROR', { additional: 'info' });

			expect(isNotificationError(error)).toBe(true);
			expect(error.message).toBe('테스트 에러');
			expect(error.code).toBe('TEST_ERROR');
			expect(error.details).toEqual({ additional: 'info' });
			expect(error.name).toBe('NotificationError');
		});
	});
});
