<script lang="ts">
	import type { User } from '$lib/User';
	import type {
		NotificationFormData,
		NotificationPriority,
		NotificationTargetType,
		NotificationTemplate
	} from '$lib/types/notification';
	import {
		send,
		validateFormData,
		estimateDeliveryTime,
		isNotificationError,
		createNotificationError
	} from '$lib/services/notification/NotificationService';
	import { executeMessage } from '$lib/Functions';
	import {
		DEFAULT_NOTIFICATION_FORM,
		NOTIFICATION_PRIORITY_LOW,
		NOTIFICATION_PRIORITY_NORMAL,
		NOTIFICATION_PRIORITY_HIGH,
		NOTIFICATION_PRIORITY_URGENT,
		NOTIFICATION_TARGET_ALL,
		NOTIFICATION_TARGET_GROUP,
		NOTIFICATION_TARGET_INDIVIDUAL,
		SUCCESS_MESSAGES
	} from '$lib/constants/notification';

	import PriorityBadge from '$lib/components/Notification/Common/PriorityBadge.svelte';
	import TargetSelector from '$lib/components/Notification/Send/TargetSelector.svelte';
	import TemplateSelector from '$lib/components/Notification/Send/TemplateSelector.svelte';

	import Icon from 'svelte-awesome';
	import { faPaperPlane } from '@fortawesome/free-solid-svg-icons/faPaperPlane';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';

	interface Props {
		user: User;
		onsent?: () => void;
		oncancel?: () => void;
	}

	let { user, onsent, oncancel }: Props = $props();

	// 폼 데이터 상태
	let formData = $state<NotificationFormData>({ ...DEFAULT_NOTIFICATION_FORM });
	let isSubmitting = $state(false);
	let submitProgress = $state<string>(''); // 전송 진행 상태 메시지
	let errors = $state<Record<string, string>>({});

	// 선택된 대상 정보
	let selectedTargetInfo = $state<{ name: string; count: number, members: string[] } | null>(null);

	// 우선순위 옵션
	const priorityOptions = [
		{ value: NOTIFICATION_PRIORITY_LOW, text: '낮음', color: 'badge-info' },
		{ value: NOTIFICATION_PRIORITY_NORMAL, text: '보통', color: 'badge-success' },
		{ value: NOTIFICATION_PRIORITY_HIGH, text: '높음', color: 'badge-warning' },
		{ value: NOTIFICATION_PRIORITY_URGENT, text: '긴급', color: 'badge-error' }
	];

	// 폼 유효성 검사
	function validateForm(): boolean {
		// 함수형 유틸리티 호출
		const validation = validateFormData(formData);

		// 추가 검증: 수신자 수 확인
		if (
			formData.target_type !== NOTIFICATION_TARGET_ALL &&
			selectedTargetInfo &&
			selectedTargetInfo.count === 0
		) {
			validation.errors.target_ids = '선택된 대상에 수신 가능한 직원이 없습니다.';
			validation.isValid = false;
		}

		errors = validation.errors;
		return validation.isValid;
	}

	// 알림 전송 처리
	async function handleSubmit() {
		if (!validateForm()) {
			// 첫 번째 에러 필드로 포커스 이동
			const firstErrorField = Object.keys(errors)[0];
			if (firstErrorField) {
				const element = document.getElementById(firstErrorField);
				element?.focus();
			}
			return;
		}

		isSubmitting = true;
		submitProgress = '알림 전송 중...';
		const startTime = Date.now();

		try {
			// API 호출 시작 시간 기록
			submitProgress = '서버에 전송 중...';
			const response = await send(user, formData);

			submitProgress = '전송 결과 처리 중...';

			if (response.success) {
				// 전송 완료 시간 계산
				const elapsedTime = Date.now() - startTime;
				console.log(`알림 전송 완료: ${elapsedTime}ms`);

				// 3초 이내 피드백 보장
				if (elapsedTime < 3000) {
					await executeMessage(SUCCESS_MESSAGES.NOTIFICATION_SENT);
				} else {
					// 3초 초과 시 경고와 함께 성공 메시지
					await executeMessage(
						`${SUCCESS_MESSAGES.NOTIFICATION_SENT}\n(처리 시간: ${Math.round(elapsedTime / 1000)}초)`,
						'warning'
					);
				}

				// 폼 초기화 후 성공 콜백 호출
				resetForm();
				onsent?.();
			} else {
				throw createNotificationError(
					response.message || '알림 전송에 실패했습니다.',
					'SEND_FAILED',
					response
				);
			}
		} catch (error) {
			console.error('알림 전송 오류:', error);

			// 에러 타입별 상세 처리
			if (isNotificationError(error)) {
				// 네트워크 에러인 경우 재시도 안내
				if (error.code === 'NETWORK_ERROR') {
					await executeMessage(
						`${error.message}\n\n네트워크 연결을 확인하고 다시 시도해주세요.`,
						'error'
					);
				} else if (error.code === 'VALIDATION_ERROR') {
					// 서버 측 유효성 검사 에러
					await executeMessage(`입력 데이터 오류: ${error.message}`, 'error');
				} else if (error.code === 'PERMISSION_DENIED') {
					// 권한 에러
					await executeMessage(`권한 오류: ${error.message}\n\n관리자에게 문의하세요.`, 'error');
				} else {
					// 기타 알림 관련 에러
					await executeMessage(error.message, 'error');
				}
			} else {
				// 예상치 못한 에러
				await executeMessage(
					'알림 전송 중 예상치 못한 오류가 발생했습니다.\n\n잠시 후 다시 시도하거나 관리자에게 문의하세요.',
					'error'
				);
			}
		} finally {
			isSubmitting = false;
			submitProgress = '';
		}
	}

	// 취소 처리
	function handleCancel() {
		oncancel?.();
	}

	// 폼 초기화
	function resetForm() {
		formData = { ...DEFAULT_NOTIFICATION_FORM };
		selectedTargetInfo = null;
		errors = {};
	}

	// 대상 타입 변경 시 target_ids 초기화
	function handleTargetTypeChange() {
		formData.target_ids = [];
		selectedTargetInfo = null;
		if (errors.target_ids) {
			delete errors.target_ids;
		}
	}

	// 대상 선택 핸들러
	function handleTargetSelect(event: {
		targetId: number;
		targetName: string;
		recipientCount: number;
		memberNames?: string[];
		memberIds?: number[];
	}) {
		// 그룹 선택인 경우 멤버 ID들을 사용, 개별 선택인 경우 해당 직원 ID 사용
		if (formData.target_type === 'group' && event.memberIds && event.memberIds.length > 0) {
			formData.target_ids = [event.targetId];
		} else {
			formData.target_ids = [event.targetId];
		}

		selectedTargetInfo = {
			name: event.targetName,
			count: event.recipientCount,
			members: event.memberNames || []
		};

		// 에러 상태 초기화
		if (errors.target_ids) {
			delete errors.target_ids;
		}
	}

	// 실시간 유효성 검사
	function handleFieldChange(field: keyof NotificationFormData) {
		// 기존 에러 제거
		if (errors[field]) {
			delete errors[field];
		}

		// 실시간 검증 (디바운싱 없이 즉시 검증)
		const newErrors: Record<string, string> = {};

		if (field === 'title' && formData.title) {
			if (formData.title.trim().length < 2) {
				newErrors.title = '제목은 최소 2자 이상 입력해주세요.';
			} else if (formData.title.trim().length > 200) {
				newErrors.title = '제목은 200자 이내로 입력해주세요.';
			}
		}

		if (field === 'content' && formData.content) {
			if (formData.content.trim().length < 5) {
				newErrors.content = '내용은 최소 5자 이상 입력해주세요.';
			} else if (formData.content.trim().length > 1000) {
				newErrors.content = '내용은 1000자 이내로 입력해주세요.';
			}
		}

		if (field === 'action_url' && formData.action_url && formData.action_url.trim()) {
			try {
				const url = new URL(formData.action_url);
				if (!['http:', 'https:'].includes(url.protocol)) {
					newErrors.action_url = 'HTTP 또는 HTTPS URL만 허용됩니다.';
				}
			} catch {
				newErrors.action_url = '올바른 URL 형식을 입력해주세요. (예: https://example.com)';
			}
		}

		// 새로운 에러가 있으면 추가
		if (newErrors[field]) {
			errors[field] = newErrors[field];
		}
	}

	// 템플릿 선택 핸들러
	function handleTemplateSelect(template: NotificationTemplate) {
		// 템플릿 데이터로 폼 자동 입력
		formData.title = template.title;
		formData.content = template.content;
		formData.priority = template.priority;

		// 에러 상태 초기화
		if (errors.title) delete errors.title;
		if (errors.content) delete errors.content;
	}

	// 문자 수 계산
	const titleLength = $derived(formData.title?.length || 0);
	const contentLength = $derived(formData.content?.length || 0);

	// 전송 대상 미리보기 텍스트
	let targetPreview = $derived.by(() => {
		if (formData.target_type === NOTIFICATION_TARGET_ALL) {
			return '모든 활성 직원';
		} else if (selectedTargetInfo) {
			return `${selectedTargetInfo.name} (${selectedTargetInfo.count}명)`;
		} else if (formData.target_type === NOTIFICATION_TARGET_GROUP) {
			return '그룹을 선택해주세요';
		} else if (formData.target_type === NOTIFICATION_TARGET_INDIVIDUAL) {
			return '직원을 선택해주세요';
		} else {
			return '';
		}
	});

	// 예상 전송 시간
	let estimatedDeliveryTime = $derived.by(() => {
		if (selectedTargetInfo && selectedTargetInfo.count > 0) {
			return estimateDeliveryTime(selectedTargetInfo.count);
		} else if (formData.target_type === NOTIFICATION_TARGET_ALL) {
			// 전체 직원 수를 알 수 없으므로 일반적인 예상 시간
			return '1-3초';
		}
		return '';
	});
</script>

<div class="card bg-base-100 shadow-xl">
	<div class="card-body">
		<div class="card-title flex items-center gap-2 mb-6">
			<Icon data={faPaperPlane} class="w-6 h-6 text-primary" />
			<span>알림 작성</span>
		</div>

		<form
			onsubmit={(e) => {
				e.preventDefault();
				handleSubmit();
			}}
			class="space-y-6"
		>
			<!-- 템플릿 선택 -->
			<div class="form-control">
				<TemplateSelector {user} onselect={handleTemplateSelect} />
			</div>
			
			<!-- 제목 입력 -->
			<div class="form-control">
				<label class="label" for="title">
					<span class="label-text font-medium">제목 <span class="text-error">*</span></span>
					<span class="label-text-alt text-sm">
						{titleLength}/200
					</span>
				</label>
				<input
					id="title"
					type="text"
					placeholder="알림 제목을 입력하세요"
					class="input input-bordered w-full"
					class:input-error={errors.title}
					bind:value={formData.title}
					oninput={() => handleFieldChange('title')}
					maxlength="200"
					required
				/>
				{#if errors.title}
					<div class="label">
						<span class="label-text-alt text-error">{errors.title}</span>
					</div>
				{/if}
			</div>

			<!-- 내용 입력 -->
			<div class="form-control">
				<label class="label" for="content">
					<span class="label-text font-medium">내용 <span class="text-error">*</span></span>
					<span class="label-text-alt text-sm">
						{contentLength}/1000
					</span>
				</label>
				<textarea
					id="content"
					placeholder="알림 내용을 입력하세요"
					class="textarea textarea-bordered h-32 w-full resize-none"
					class:textarea-error={errors.content}
					bind:value={formData.content}
					oninput={() => handleFieldChange('content')}
					maxlength="1000"
					required
				></textarea>
				{#if errors.content}
					<div class="label">
						<span class="label-text-alt text-error">{errors.content}</span>
					</div>
				{/if}
			</div>

			<!-- 우선순위 선택 -->
			<div class="form-control">
				<label class="label" for="priority">
					<span class="label-text font-medium">우선순위</span>
				</label>
				<div class="flex flex-wrap gap-3">
					{#each priorityOptions as option}
						<label
							class="label cursor-pointer flex items-center gap-2 p-3 rounded-lg border border-base-300 hover:border-primary transition-colors"
							class:border-primary={formData.priority === option.value}
							class:bg-primary-100={formData.priority === option.value}
						>
							<input
								type="radio"
								name="priority"
								value={option.value}
								class="radio radio-primary radio-sm"
								bind:group={formData.priority}
							/>
							<PriorityBadge priority={option.value as NotificationPriority} size="sm" />
						</label>
					{/each}
				</div>
			</div>

			<!-- 대상 선택 컴포넌트 -->
			<div class="form-control">
				<div class="label">
					<span class="label-text font-medium">전송 대상 <span class="text-error">*</span></span>
				</div>
				<TargetSelector
					{user}
					targetType={formData.target_type}
					selectedTargetId={formData.target_ids?.[0]}
					onselect={handleTargetSelect}
					ontargettypechange={(newTargetType) => {
						formData.target_type = newTargetType;
						handleTargetTypeChange();
					}}
				/>
				{#if errors.target_ids}
					<div class="label">
						<span class="label-text-alt text-error">{errors.target_ids}</span>
					</div>
				{/if}
			</div>

			<!-- 액션 URL 입력 (선택사항) - 임시 주석 처리 -->
			<!-- 
			<div class="form-control">
				<label class="label" for="action_url">
					<span class="label-text font-medium">액션 URL (선택사항)</span>
					<span class="label-text-alt text-sm">
						{actionUrlLength > 0 ? `${actionUrlLength}/500` : ''}
					</span>
				</label>
				<input
					id="action_url"
					type="url"
					placeholder="https://example.com (클릭 시 이동할 URL)"
					class="input input-bordered w-full"
					class:input-error={errors.action_url}
					bind:value={formData.action_url}
					oninput={() => handleFieldChange('action_url')}
					maxlength="500"
				/>
				{#if errors.action_url}
					<label class="label">
						<span class="label-text-alt text-error">{errors.action_url}</span>
					</label>
				{/if}
				<label class="label">
					<span class="label-text-alt text-sm">
						알림을 클릭했을 때 이동할 페이지 URL을 입력하세요. (선택사항)
					</span>
				</label>
			</div>
			-->

			<!-- 전송 미리보기 -->
			<div class="card bg-base-200">
				<div class="card-body p-4">
					<h3 class="card-title text-lg mb-3">전송 미리보기</h3>
					<div class="space-y-2">
						<div class="flex items-center gap-2">
							<span class="font-medium">대상:</span>
							<span class="text-sm">{targetPreview}</span>
						</div>
						{#if selectedTargetInfo && formData.target_type === NOTIFICATION_TARGET_GROUP && selectedTargetInfo.members}
							<div class="member-list-section">
								<Icon data={faUsers} />
								{#each selectedTargetInfo.members as name (name)}
									<span class="member-tag">{name}</span>
								{/each}
							</div>
						{:else if selectedTargetInfo && formData.target_type === NOTIFICATION_TARGET_INDIVIDUAL}
							<div class="text-xs text-base-content/70">
								선택된 직원: {selectedTargetInfo.name}
							</div>
						{/if}
						<div class="flex items-center gap-2">
							<span class="font-medium">우선순위:</span>
							<PriorityBadge priority={formData.priority} size="sm" />
						</div>
						{#if estimatedDeliveryTime}
							<div class="flex items-center gap-2">
								<span class="font-medium">예상 전송 시간:</span>
								<span class="text-sm badge badge-outline">{estimatedDeliveryTime}</span>
							</div>
						{/if}
						{#if formData.title}
							<div>
								<span class="font-medium">제목:</span>
								<p class="text-sm mt-1 p-2 bg-base-100 rounded">{formData.title}</p>
							</div>
						{/if}
						{#if formData.content}
							<div>
								<span class="font-medium">내용:</span>
								<p class="text-sm mt-1 p-2 bg-base-100 rounded whitespace-pre-wrap">
									{formData.content}
								</p>
							</div>
						{/if}
						<!-- 액션 URL 미리보기 임시 주석 처리 -->
						<!-- 
						{#if formData.action_url}
							<div>
								<span class="font-medium">액션 URL:</span>
								<p class="text-sm mt-1 p-2 bg-base-100 rounded break-all">{formData.action_url}</p>
							</div>
						{/if}
						-->
					</div>
				</div>
			</div>

			<!-- 전송 진행 상태 표시 -->
			{#if isSubmitting && submitProgress}
				<div class="alert alert-info">
					<div class="flex items-center gap-3">
						<span class="loading loading-spinner loading-sm"></span>
						<span>{submitProgress}</span>
					</div>
				</div>
			{/if}

			<!-- 버튼 영역 -->
			<div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-base-300">
				<button
					type="submit"
					class="btn btn-primary btn-lg flex-1"
					class:loading={isSubmitting}
					disabled={isSubmitting}
				>
					{#if !isSubmitting}
						<Icon data={faPaperPlane} class="w-5 h-5" />
					{/if}
					{isSubmitting ? submitProgress || '전송 중...' : '알림 전송'}
				</button>

				<div class="flex gap-3 sm:flex-none">
					<button
						type="button"
						class="btn btn-outline btn-lg flex-1 sm:w-24"
						onclick={handleCancel}
						disabled={isSubmitting}
					>
						<Icon data={faTimes} class="w-4 h-4" />
						취소
					</button>

					<button
						type="button"
						class="btn btn-ghost btn-lg flex-1 sm:w-24"
						onclick={resetForm}
						disabled={isSubmitting}
					>
						초기화
					</button>
				</div>
			</div>
		</form>
	</div>
</div>

<style>
	.form-control {
		margin-bottom: 0;
	}

	.card-body {
		padding: 2rem;
	}

	@media (max-width: 640px) {
		.card-body {
			padding: 1.5rem;
		}
	}

	/* 라디오 버튼 그룹 스타일링 */
	.label.cursor-pointer:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	/* 미리보기 영역 스타일링 */
	.card.bg-base-200 {
		border: 1px solid hsl(var(--bc) / 0.1);
	}

	/* 텍스트 영역 자동 크기 조정 방지 */
	.textarea {
		resize: vertical;
		min-height: 8rem;
	}

	/* 입력 필드 포커스 스타일 */
	.input:focus,
	.textarea:focus {
		outline: 2px solid hsl(var(--p));
		outline-offset: 2px;
	}

	/* 멤버 리스트 섹션 스타일 */
	.member-list-section {
		background: hsl(var(--b1));
		border: 1px solid hsl(var(--bc) / 0.15);
		border-radius: 8px;
		padding-left: 20px;
		margin-top: 8px;
	}

	.member-tag {
		background: hsl(var(--p) / 0.1);
		color: hsl(var(--p));
		border: 1px solid hsl(var(--p) / 0.3);
		border-radius: 6px;
		padding: 4px 4px;
		font-size: 12px;
		font-weight: 500;
		line-height: 1.2;
		transition: all 0.2s ease;
		white-space: nowrap;
	}

	.member-tag:hover {
		background: hsl(var(--p) / 0.15);
		transform: translateY(-1px);
		box-shadow: 0 2px 4px hsl(var(--p) / 0.2);
	}

	/* 반응형 디자인 */
	@media (max-width: 768px) {
		.member-list-section {
			padding: 6px;
		}

		.member-tag {
			font-size: 11px;
			padding: 3px 6px;
		}
	}

	@media (max-width: 480px) {
		.member-tag {
			font-size: 10px;
			padding: 2px 5px;
		}
	}
</style>
