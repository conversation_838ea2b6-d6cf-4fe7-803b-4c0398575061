<script lang="ts">
	import type { Notification, NotificationRecipient } from '$lib/types/notification';
	import type { User } from '$lib/User';
	import { authClient } from '$lib/services/AxiosBackend';
	import { handleCatch } from '$lib/Functions';
	import { API_ENDPOINTS } from '$lib/constants/notification';
	import {
		getPriorityText,
		getPriorityColor,
		getTargetTypeText,
		getStatusText,
		getStatusColor
	} from '$lib/types/notification';
	import StatusIndicator from './StatusIndicator.svelte';

	interface Props {
		notification: Notification;
		user: User;
		show: boolean;
		onclose?: () => void;
	}

	let { notification, user, show, onclose } = $props();

	// 수신자 목록 상태
	let recipients = $state<NotificationRecipient[]>([]);
	let recipientsLoading = $state(false);
	let recipientsError = $state<string | null>(null);
	let recipientsLoaded = $state(false); // 로딩 완료 여부 추적

	// 통계 데이터
	let stats = $derived.by(() => {
		// 새로운 API 응답 구조에서 recipient_stats 사용
		const recipientStats = notification.recipient_stats;
		const totalRecipients = recipientStats?.total_count || notification.recipients_count || 0;
		const readCount = parseInt(recipientStats?.read_count || '0') || notification.read_count || 0;
		const deliveredCount = parseInt(recipientStats?.delivered_count || '0') || 0;
		const unreadCount = parseInt(recipientStats?.unread_count || '0') || (totalRecipients - readCount);
		const readRate = totalRecipients > 0 ? Math.round((readCount / totalRecipients) * 100) : 0;

		return {
			total: totalRecipients,
			read: readCount,
			delivered: deliveredCount,
			unread: unreadCount,
			readRate
		};
	});

	// 수신자 목록 로드
	async function loadRecipients() {
		if (!notification.id || recipientsLoading || recipientsLoaded) return;

		try {
			recipientsLoading = true;
			recipientsError = null;

			const { status, data } = await authClient.get(
				`${API_ENDPOINTS.HISTORY}/${notification.id}/recipients`,
				{
					params: { userId: user.id }
				}
			);

			if (status === 200 && data?.success) {
				recipients = data.data || [];
				recipientsLoaded = true;
			} else {
				recipientsError = data?.message || '수신자 목록을 불러올 수 없습니다.';
			}
		} catch (error) {
			recipientsError = (await handleCatch(error, true)) as string;
		} finally {
			recipientsLoading = false;
		}
	}

	// 모달이 열릴 때 수신자 목록 로드 (중복 실행 방지)
	$effect(() => {
		if (show && notification.id && !recipientsLoading && !recipientsLoaded) {
			loadRecipients();
		}
	});

	// 모달 닫기
	function handleClose() {
		// 다음 번 열 때를 위해 상태 초기화
		recipients = [];
		recipientsLoaded = false;
		recipientsError = null;
		onclose?.();
	}

	// ESC 키로 모달 닫기
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}

	// 읽음 상태 텍스트
	function getReadStatusText(recipient: NotificationRecipient): string {
		if (recipient.read_at) {
			return `읽음 (${new Date(recipient.read_at).toLocaleString('ko-KR')})`;
		} else if (recipient.delivered_at) {
			return '전달됨 (읽지 않음)';
		} else {
			return '전달 대기';
		}
	}

	// 읽음 상태 색상
	function getReadStatusColor(recipient: NotificationRecipient): string {
		if (recipient.read_at) {
			return 'badge-success';
		} else if (recipient.delivered_at) {
			return 'badge-warning';
		} else {
			return 'badge-ghost';
		}
	}

	// 시간 포맷팅 - timestamp와 문자열 모두 처리
	function formatDateTime(dateInput?: string | number): string {
		if (!dateInput) return '-';

		let date: Date;
		if (typeof dateInput === 'number') {
			// timestamp인 경우 (초 단위라면 1000을 곱하여 밀리초로 변환)
			date = new Date(dateInput > 10000000000 ? dateInput : dateInput * 1000);
		} else {
			// 문자열인 경우
			date = new Date(dateInput);
		}

		return date.toLocaleString('ko-KR');
	}
</script>

{#if show}
	<div class="modal modal-open" role="dialog" aria-labelledby="modal-title">
		<div class="modal-box max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
			<!-- 모달 헤더 -->
			<div class="flex items-center justify-between mb-6">
				<h3 id="modal-title" class="font-bold text-xl">알림 상세 정보</h3>
				<button
					class="btn btn-sm btn-circle btn-ghost"
					onclick={handleClose}
					aria-label="모달 닫기"
				>
					<i class="fas fa-times"></i>
				</button>
			</div>

			<!-- 모달 내용 (스크롤 가능) -->
			<div class="flex-1 overflow-y-auto">
				<!-- 기본 정보 섹션 -->
				<div class="card bg-base-200 mb-6">
					<div class="card-body">
						<h4 class="card-title text-lg mb-4">
							<i class="fas fa-info-circle text-info"></i>
							기본 정보
						</h4>

						<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<!-- 왼쪽 컬럼 -->
							<div class="space-y-4">
								<div>
									<div class="label">
										<span class="label-text font-semibold">제목</span>
									</div>
									<div class="text-base-content text-lg">{notification.title}</div>
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">우선순위</span>
									</div>
									<div class="badge {getPriorityColor(notification.priority)} badge-lg">
										{getPriorityText(notification.priority)}
									</div>
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">상태</span>
									</div>
									<StatusIndicator {notification} />
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">발송자</span>
									</div>
									<div class="text-base-content">
										{notification.sender?.name || '알 수 없음'}
									</div>
								</div>
							</div>

							<!-- 오른쪽 컬럼 -->
							<div class="space-y-4">
								<div>
									<div class="label">
										<span class="label-text font-semibold">전송 대상</span>
									</div>
									<div class="text-base-content">
										{getTargetTypeText(notification.target_type)}
										{#if notification.target_group}
											- {notification.target_group.name}
										{/if}
										{#if notification.target_user}
											- {notification.target_user.name}
										{/if}
									</div>
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">생성 시간</span>
									</div>
									<div class="text-base-content">
										{formatDateTime(notification.created_at)}
									</div>
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">전송 시간</span>
									</div>
									<div class="text-base-content">
										{formatDateTime(notification.sent_at)}
									</div>
								</div>

								<div>
									<div class="label">
										<span class="label-text font-semibold">수신자 수</span>
									</div>
									<div class="text-base-content font-semibold">
										{stats.total}명
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 내용 섹션 -->
				<div class="card bg-base-200 mb-6">
					<div class="card-body">
						<h4 class="card-title text-lg mb-4">
							<i class="fas fa-file-alt text-primary"></i>
							알림 내용
						</h4>
						<div class="bg-base-100 p-4 rounded-lg border">
							<div class="whitespace-pre-wrap text-base-content">
								{notification.content}
							</div>
						</div>
					</div>
				</div>

				<!-- 액션 URL 섹션 -->
				{#if notification.action_url}
					<div class="card bg-base-200 mb-6">
						<div class="card-body">
							<h4 class="card-title text-lg mb-4">
								<i class="fas fa-link text-accent"></i>
								액션 URL
							</h4>
							<div class="bg-base-100 p-4 rounded-lg border">
								<a
									href={notification.action_url}
									target="_blank"
									rel="noopener noreferrer"
									class="link link-primary break-all"
								>
									{notification.action_url}
									<i class="fas fa-external-link-alt ml-1"></i>
								</a>
							</div>
						</div>
					</div>
				{/if}

				<!-- 읽음 통계 섹션 -->
				<div class="card bg-base-200 mb-6">
					<div class="card-body">
						<h4 class="card-title text-lg mb-4">
							<i class="fas fa-chart-pie text-success"></i>
							읽음 통계
						</h4>

						<div class="grid grid-cols-2 lg:grid-cols-5 gap-4">
							<div class="stat bg-base-100 rounded-lg">
								<div class="stat-title">전체 수신자</div>
								<div class="stat-value text-primary">{stats.total}</div>
								<div class="stat-desc">명</div>
							</div>

							<div class="stat bg-base-100 rounded-lg">
								<div class="stat-title">전달됨</div>
								<div class="stat-value text-info">{stats.delivered}</div>
								<div class="stat-desc">명</div>
							</div>

							<div class="stat bg-base-100 rounded-lg">
								<div class="stat-title">읽음</div>
								<div class="stat-value text-success">{stats.read}</div>
								<div class="stat-desc">명</div>
							</div>

							<div class="stat bg-base-100 rounded-lg">
								<div class="stat-title">읽지 않음</div>
								<div class="stat-value text-warning">{stats.unread}</div>
								<div class="stat-desc">명</div>
							</div>

							<div class="stat bg-base-100 rounded-lg">
								<div class="stat-title">읽음률</div>
								<div class="stat-value text-info">{stats.readRate}%</div>
								<div class="stat-desc">
									{stats.read}/{stats.total}
								</div>
							</div>
						</div>

						<!-- 읽음률 프로그레스 바 -->
						<div class="mt-4">
							<div class="flex justify-between text-sm mb-1">
								<span>읽음률</span>
								<span>{stats.readRate}%</span>
							</div>
							<progress class="progress progress-success w-full" value={stats.readRate} max="100"
							></progress>
						</div>
					</div>
				</div>

				<!-- 수신자 목록 섹션 -->
				<div class="card bg-base-200">
					<div class="card-body">
						<h4 class="card-title text-lg mb-4">
							<i class="fas fa-users text-secondary"></i>
							수신자 목록 및 읽음 상태
						</h4>

						{#if recipientsLoading}
							<div class="flex justify-center items-center py-8">
								<span class="loading loading-spinner loading-lg"></span>
								<span class="ml-2">수신자 목록을 불러오는 중...</span>
							</div>
						{:else if recipientsError}
							<div class="alert alert-error">
								<i class="fas fa-exclamation-triangle"></i>
								<span>{recipientsError}</span>
								<div>
									<button class="btn btn-sm btn-ghost" onclick={loadRecipients}> 다시 시도 </button>
								</div>
							</div>
						{:else if !Array.isArray(recipients) || recipients.length === 0}
							<div class="text-center py-8 text-base-content/70">
								<i class="fas fa-inbox text-4xl mb-2"></i>
								<p>수신자가 없습니다.</p>
							</div>
						{:else}
							<div class="overflow-x-auto">
								<table class="table table-zebra">
									<thead>
										<tr>
											<th>수신자</th>
											<th>부서</th>
											<th>직급</th>
											<th>전달 시간</th>
											<th>읽음 시간</th>
											<th>상태</th>
										</tr>
									</thead>
									<tbody>
										{#each recipients as recipient}
											<tr>
												<td>
													<div class="flex items-center gap-2">
														<div class="avatar placeholder">
															<div class="bg-neutral text-neutral-content rounded-full w-8">
																<span class="text-xs">
																	{recipient.user?.name?.charAt(0) || '?'}
																</span>
															</div>
														</div>
														<span class="font-medium">
															{recipient.user?.name || '알 수 없음'}
														</span>
													</div>
												</td>
												<td>{recipient.user?.department || '-'}</td>
												<td>{recipient.user?.position || '-'}</td>
												<td class="text-sm">
													{formatDateTime(recipient.delivered_at)}
												</td>
												<td class="text-sm">
													{formatDateTime(recipient.read_at)}
												</td>
												<td>
													<div class="badge {getReadStatusColor(recipient)} badge-sm">
														{getReadStatusText(recipient)}
													</div>
												</td>
											</tr>
										{/each}
									</tbody>
								</table>
							</div>

							<!-- 수신자 요약 -->
							<div class="mt-4 text-sm text-base-content/70">
								총 {Array.isArray(recipients) ? recipients.length : 0}명의 수신자 중 {Array.isArray(recipients) ? recipients.filter((r) => r.read_at).length : 0}명이 읽음
							</div>
						{/if}
					</div>
				</div>
			</div>

			<!-- 모달 푸터 -->
			<div class="modal-action mt-6">
				<button class="btn btn-primary" onclick={handleClose}>
					<i class="fas fa-check"></i>
					확인
				</button>
			</div>
		</div>

		<!-- 모달 배경 -->
		<div
			class="modal-backdrop"
			onclick={handleClose}
			role="button"
			tabindex="0"
			onkeydown={handleKeydown}
		></div>
	</div>
{/if}
