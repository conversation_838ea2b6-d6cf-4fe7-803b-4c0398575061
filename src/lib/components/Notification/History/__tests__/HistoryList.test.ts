import { describe, it, expect } from 'vitest';
import type { Notification } from '$lib/types/notification';

// 취소 가능 여부 확인 로직 (HistoryList.svelte에서 추출)
function canCancel(notification: Notification): boolean {
	// 전송된 알림만 취소 가능하고, 이미 취소된 알림은 취소 불가
	if (notification.status !== 'sent') {
		return false;
	}

	// 전송되지 않은 알림은 취소 불가 (임시저장 상태)
	if (!notification.sent_at) {
		return false;
	}

	// 모든 수신자가 읽은 경우 취소 불가
	if (notification.recipients_count && notification.read_count) {
		return notification.read_count < notification.recipients_count;
	}

	// 수신자가 있는 경우에만 취소 가능
	return (notification.recipients_count || 0) > 0;
}

// 테스트용 알림 데이터
const mockNotifications: Notification[] = [
	{
		id: '1',
		title: '취소 가능한 알림',
		content: '테스트 내용 1',
		priority: 'normal',
		sender_id: 1,
		target_type: 'all',
		status: 'sent',
		sent_at: '2025-01-27T10:00:00Z',
		created_at: '2025-01-27T09:00:00Z',
		updated_at: '2025-01-27T09:00:00Z',
		recipients_count: 10,
		read_count: 5 // 5명이 읽음, 5명이 읽지 않음
	},
	{
		id: '2',
		title: '취소 불가능한 알림 (모두 읽음)',
		content: '테스트 내용 2',
		priority: 'urgent',
		sender_id: 1,
		target_type: 'all',
		status: 'sent',
		sent_at: '2025-01-27T11:00:00Z',
		created_at: '2025-01-27T10:00:00Z',
		updated_at: '2025-01-27T10:00:00Z',
		recipients_count: 5,
		read_count: 5 // 모든 사용자가 읽음
	},
	{
		id: '3',
		title: '취소 불가능한 알림 (취소됨)',
		content: '테스트 내용 3',
		priority: 'high',
		sender_id: 1,
		target_type: 'all',
		status: 'cancelled',
		sent_at: '2025-01-27T12:00:00Z',
		created_at: '2025-01-27T11:00:00Z',
		updated_at: '2025-01-27T11:00:00Z',
		recipients_count: 8,
		read_count: 3
	},
	{
		id: '4',
		title: '취소 불가능한 알림 (임시저장)',
		content: '테스트 내용 4',
		priority: 'low',
		sender_id: 1,
		target_type: 'all',
		status: 'draft',
		created_at: '2025-01-27T12:00:00Z',
		updated_at: '2025-01-27T12:00:00Z',
		recipients_count: 0,
		read_count: 0
	}
];

describe('알림 취소 기능 로직 테스트', () => {
	it('읽지 않은 사용자가 있는 전송된 알림은 취소 가능해야 함', () => {
		const notification = mockNotifications[0];
		expect(canCancel(notification)).toBe(true);
	});

	it('모든 사용자가 읽은 알림은 취소 불가능해야 함', () => {
		const notification = mockNotifications[1];
		expect(canCancel(notification)).toBe(false);
	});

	it('이미 취소된 알림은 취소 불가능해야 함', () => {
		const notification = mockNotifications[2];
		expect(canCancel(notification)).toBe(false);
	});

	it('임시저장 상태의 알림은 취소 불가능해야 함', () => {
		const notification = mockNotifications[3];
		expect(canCancel(notification)).toBe(false);
	});

	it('전송되지 않은 알림은 취소 불가능해야 함', () => {
		const notification: Notification = {
			...mockNotifications[0],
			sent_at: undefined
		};
		expect(canCancel(notification)).toBe(false);
	});

	it('수신자가 없는 알림은 취소 불가능해야 함', () => {
		const notification: Notification = {
			...mockNotifications[0],
			recipients_count: 0,
			read_count: 0
		};
		expect(canCancel(notification)).toBe(false);
	});
});
