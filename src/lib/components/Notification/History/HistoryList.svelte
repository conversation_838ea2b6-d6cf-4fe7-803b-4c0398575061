<script lang="ts">
	import type { Notification } from '$lib/types/notification';
	import {
		getPriorityText,
		getPriorityColor,
		getStatusText,
		getStatusColor,
		getTargetTypeText
	} from '$lib/types/notification';
	import StatusIndicator from './StatusIndicator.svelte';
	import { throttle, createLazyLoader } from '$lib/utils/performance';
	import { onMount } from 'svelte';

	interface Props {
		notifications: Notification[];
		loading?: boolean;
		sortBy?: string;
		sortOrder?: 'asc' | 'desc';
		onnotificationclick?: (event: { notification: Notification }) => void;
		oncancelclick?: (event: { notification: Notification }) => void;
		onsort?: (event: { field: string; order: 'asc' | 'desc' }) => void;
	}

	let {
		notifications,
		loading = false,
		sortBy = 'created_at',
		sortOrder = 'desc',
		onnotificationclick,
		oncancelclick,
		onsort
	} = $props();

	// 정렬 핸들러
	function handleSort(field: string) {
		const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
		onsort?.({ field, order: newOrder });
	}

	// 스로틀된 알림 클릭 핸들러 (성능 최적화)
	const throttledNotificationClick = throttle((notification: Notification) => {
		onnotificationclick?.({ notification });
	}, 200); // 200ms 스로틀

	function handleNotificationClick(notification: Notification) {
		throttledNotificationClick(notification);
	}

	// 스로틀된 취소 버튼 클릭 핸들러 (성능 최적화)
	const throttledCancelClick = throttle((notification: Notification) => {
		oncancelclick?.({ notification });
	}, 300); // 300ms 스로틀

	function handleCancelClick(event: Event, notification: Notification) {
		event.stopPropagation();
		throttledCancelClick(notification);
	}

	// 지연 로딩을 위한 Intersection Observer 설정
	let lazyLoader: ReturnType<typeof createLazyLoader> | null = $state(null);
	let visibleNotifications = $state<Set<string>>(new Set());

	onMount(() => {
		// 대용량 리스트의 경우 지연 로딩 활성화
		if (notifications.length > 50) {
			lazyLoader = createLazyLoader((entry) => {
				const notificationId = entry.target.getAttribute('data-notification-id');
				if (notificationId) {
					visibleNotifications.add(notificationId);
					visibleNotifications = new Set(visibleNotifications); // 반응성 트리거
				}
			});
		}
	});

	// 취소 가능 여부 확인
	function canCancel(notification: Notification): boolean {
		// 전송된 알림만 취소 가능하고, 이미 취소된 알림은 취소 불가
		if (notification.status !== 'sent') {
			return false;
		}

		// 전송되지 않은 알림은 취소 불가 (임시저장 상태)
		if (!notification.sent_at) {
			return false;
		}

		// 모든 수신자가 읽은 경우 취소 불가
		if (notification.recipients_count && notification.read_count) {
			return notification.read_count < notification.recipients_count;
		}

		// 수신자가 있는 경우에만 취소 가능
		return (notification.recipients_count || 0) > 0;
	}

	// 읽음률 계산
	function calculateReadRate(notification: Notification): number {
		if (!notification.recipients_count || notification.recipients_count === 0) {
			return 0;
		}
		const readCount = notification.read_count || 0;
		return Math.round((readCount / notification.recipients_count) * 100);
	}

	// 내용 미리보기 생성
	function getContentPreview(content: string, maxLength: number = 50): string {
		if (!content) return '';
		const cleanContent = content.replace(/<[^>]*>/g, '').trim();
		if (cleanContent.length <= maxLength) {
			return cleanContent;
		}
		return cleanContent.substring(0, maxLength) + '...';
	}

	// 날짜 포맷팅 - timestamp와 문자열 모두 처리
	function formatDate(dateInput: string | number): Date {
		if (typeof dateInput === 'number') {
			// timestamp인 경우 (초 단위라면 1000을 곱하여 밀리초로 변환)
			return new Date(dateInput > 10000000000 ? dateInput : dateInput * 1000);
		} else {
			// 문자열인 경우
			return new Date(dateInput);
		}
	}

	// 대상 정보 표시
	function getTargetInfo(notification: Notification): string {
		let info = getTargetTypeText(notification.target_type);

		if (notification.target_group) {
			info += ` - ${notification.target_group.name}`;
		} else if (notification.target_user) {
			info += ` - ${notification.target_user.name}`;
		}

		return info;
	}

	// 정렬 아이콘 표시
	function getSortIcon(field: string): string {
		if (sortBy !== field) return 'fas fa-sort text-base-content/30';
		return sortOrder === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
	}

	// 지연 로딩을 위한 Svelte 액션
	function lazyObserver(node: HTMLElement, enabled: boolean) {
		if (!enabled || !lazyLoader) return {};

		lazyLoader.observe(node);

		return {
			destroy() {
				lazyLoader?.observer.unobserve(node);
			}
		};
	}

	// 알림이 보이는지 확인하는 함수 (성능 최적화)
	function isNotificationVisible(notificationId: string): boolean {
		if (notifications.length <= 50) return true; // 작은 리스트는 모두 렌더링
		return visibleNotifications.has(notificationId);
	}
</script>

<div class="overflow-x-auto">
	<table class="table table-zebra w-full">
		<thead>
			<tr>
				<!-- 제목 -->
				<th class="cursor-pointer hover:bg-base-200" onclick={() => handleSort('title')}>
					<div class="flex items-center gap-2">
						<span>제목</span>
						<i class={getSortIcon('title')}></i>
					</div>
				</th>

				<!-- 우선순위 -->
				<th class="cursor-pointer hover:bg-base-200" onclick={() => handleSort('priority')}>
					<div class="flex items-center gap-2">
						<span>우선순위</span>
						<i class={getSortIcon('priority')}></i>
					</div>
				</th>

				<!-- 대상 -->
				<th class="cursor-pointer hover:bg-base-200" onclick={() => handleSort('target_type')}>
					<div class="flex items-center gap-2">
						<span>대상</span>
						<i class={getSortIcon('target_type')}></i>
					</div>
				</th>

				<!-- 전송시간 -->
				<th class="cursor-pointer hover:bg-base-200" onclick={() => handleSort('sent_at')}>
					<div class="flex items-center gap-2">
						<span>전송시간</span>
						<i class={getSortIcon('sent_at')}></i>
					</div>
				</th>

				<!-- 상태 -->
				<th class="cursor-pointer hover:bg-base-200" onclick={() => handleSort('status')}>
					<div class="flex items-center gap-2">
						<span>상태</span>
						<i class={getSortIcon('status')}></i>
					</div>
				</th>

				<!-- 읽음률 -->
				<th class="text-center">읽음률</th>

				<!-- 액션 -->
				<th class="text-center">액션</th>
			</tr>
		</thead>

		<tbody>
			{#if loading}
				<!-- 로딩 상태 -->
				{#each Array(5) as _}
					<tr>
						<td colspan="7">
							<div class="flex items-center gap-3">
								<div class="skeleton h-4 w-full"></div>
							</div>
						</td>
					</tr>
				{/each}
			{:else if notifications.length === 0}
				<!-- 데이터 없음 -->
				<tr>
					<td colspan="7" class="text-center py-8">
						<div class="flex flex-col items-center gap-2 text-base-content/50">
							<i class="fas fa-inbox text-4xl"></i>
							<p>알림 히스토리가 없습니다.</p>
						</div>
					</td>
				</tr>
			{:else}
				<!-- 알림 목록 (성능 최적화된) -->
				{#each notifications as notification}
					<tr
						class="hover:bg-base-200 cursor-pointer transition-colors"
						data-notification-id={notification.id}
						onclick={() => handleNotificationClick(notification)}
						use:lazyObserver={lazyLoader && notifications.length > 50}
					>
						<!-- 제목 및 내용 미리보기 (지연 로딩 최적화) -->
						<td class="max-w-xs">
							{#if isNotificationVisible(notification.id)}
								<div class="space-y-1">
									<div class="font-medium text-base-content truncate">
										{notification.title}
									</div>
									<div class="text-sm text-base-content/60 truncate">
										{getContentPreview(notification.content)}
									</div>
								</div>
							{:else}
								<!-- 플레이스홀더 (성능 최적화) -->
								<div class="space-y-1">
									<div class="skeleton h-4 w-3/4"></div>
									<div class="skeleton h-3 w-1/2"></div>
								</div>
							{/if}
						</td>

						<!-- 우선순위 -->
						<td>
							<div class="badge {getPriorityColor(notification.priority)} badge-sm">
								{getPriorityText(notification.priority)}
							</div>
						</td>

						<!-- 대상 -->
						<td class="max-w-xs">
							<div class="text-sm truncate">
								{getTargetInfo(notification)}
							</div>
							{#if notification.recipients_count}
								<div class="text-xs text-base-content/50">
									{notification.recipients_count}명
								</div>
							{/if}
						</td>

						<!-- 전송시간 -->
						<td>
							{#if notification.sent_at}
								<div class="text-sm">
									{formatDate(notification.sent_at).toLocaleDateString('ko-KR')}
								</div>
								<div class="text-xs text-base-content/50">
									{formatDate(notification.sent_at).toLocaleTimeString('ko-KR', {
										hour: '2-digit',
										minute: '2-digit'
									})}
								</div>
							{:else}
								<div class="text-sm text-base-content/50">전송되지 않음</div>
							{/if}
						</td>

						<!-- 상태 -->
						<td>
							<StatusIndicator {notification} />
						</td>

						<!-- 읽음률 -->
						<td class="text-center">
							{#if notification.recipients_count && notification.recipients_count > 0}
								<div class="text-sm font-medium">
									{calculateReadRate(notification)}%
								</div>
								<div class="text-xs text-base-content/50">
									{notification.read_count || 0}/{notification.recipients_count}
								</div>
							{:else}
								<div class="text-sm text-base-content/50">-</div>
							{/if}
						</td>

						<!-- 액션 -->
						<td class="text-center">
							<div class="flex justify-center gap-1">
								<!-- 상세보기 버튼 -->
								<button
									class="btn btn-ghost btn-xs"
									onclick={(e) => {
										e.stopPropagation();
										handleNotificationClick(notification);
									}}
									title="상세보기"
									aria-label="알림 상세보기"
								>
									<i class="fas fa-eye"></i>
								</button>

								<!-- 취소 버튼 (조건부 표시) -->
								{#if canCancel(notification)}
									<button
										class="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
										onclick={(e) => handleCancelClick(e, notification)}
										title="알림 취소 - 읽지 않은 사용자의 알림을 제거합니다"
										aria-label="알림 취소"
									>
										<i class="fas fa-ban"></i>
									</button>
								{:else if notification.status === 'sent' && notification.recipients_count === notification.read_count}
									<button
										class="btn btn-ghost btn-xs text-base-content/30 cursor-not-allowed"
										disabled
										title="모든 사용자가 읽어서 취소할 수 없습니다"
										aria-label="취소 불가능"
									>
										<i class="fas fa-check-circle"></i>
									</button>
								{/if}
							</div>
						</td>
					</tr>
				{/each}
			{/if}
		</tbody>
	</table>
</div>

<style>
	.table th {
		position: sticky;
		top: 0;
		z-index: 10;
		background-color: hsl(var(--b1));
	}

	.table tbody tr:hover {
		background-color: hsl(var(--b2));
	}

	.truncate {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
