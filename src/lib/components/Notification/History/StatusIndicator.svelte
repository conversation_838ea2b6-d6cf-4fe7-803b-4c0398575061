<script lang="ts">
	import type { Notification } from '$lib/types/notification';
	import { getStatusText, getStatusColor } from '$lib/types/notification';

	interface Props {
		notification: Notification;
		showIcon?: boolean;
		size?: 'sm' | 'md' | 'lg';
	}

	let { notification, showIcon = true, size = 'sm' } = $props();

	// 상태별 아이콘 매핑
	const statusIcons = {
		draft: 'fas fa-edit',
		sent: 'fas fa-check-circle',
		cancelled: 'fas fa-ban'
	};

	// 상태별 추가 정보
	const statusInfo = $derived.by(() => {
		switch (notification.status) {
			case 'sent':
				if (notification.sent_at) {
					return '전송완료';
				} else {
					return '전송 대기중';
				}
			case 'cancelled':
				return '취소됨';
			case 'draft':
				return '임시저장';
			default:
				return '알 수 없음';
		}
	});

	// 배지 크기 클래스
	const sizeClass = $derived.by(() => {
		switch (size) {
			case 'sm':
				return 'badge-sm';
			case 'lg':
				return 'badge-lg';
			default:
				return '';
		}
	});

	// 상태별 펄스 효과 (전송 대기중일 때)
	const shouldPulse = $derived(notification.status === 'sent' && !notification.sent_at);
</script>

<div class="flex items-center gap-2">
	<div
		class="badge {getStatusColor(notification.status)} {sizeClass}"
		class:animate-pulse={shouldPulse}
	>
		{#if showIcon}
			<i class="{statusIcons[notification.status]} mr-1"></i>
		{/if}
		{statusInfo}
	</div>

	<!-- 추가 상태 정보 (전송 시간 등) -->
	{#if notification.status === 'sent' && notification.sent_at}
		<div
			class="tooltip tooltip-top"
			data-tip="전송 시간: {new Date(notification.sent_at).toLocaleString('ko-KR')}"
		>
			<i class="fas fa-info-circle text-xs text-base-content/50 cursor-help"></i>
		</div>
	{/if}
</div>
