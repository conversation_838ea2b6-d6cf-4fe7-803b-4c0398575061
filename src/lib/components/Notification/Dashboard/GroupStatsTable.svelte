<script lang="ts">
	import type { NotificationGroupStats } from '$lib/types/notification';

	interface Props {
		data: NotificationGroupStats[];
		loading?: boolean;
		sortBy?: string;
		sortOrder?: 'asc' | 'desc';
		onsort?: (event: { field: string; order: 'asc' | 'desc' }) => void;
		ongroupclick?: (event: { groupId: number }) => void;
	}

	let {
		data = [],
		loading = false,
		sortBy = 'name',
		sortOrder = 'asc',
		onsort,
		ongroupclick
	}: Props = $props();

	// 정렬 핸들러
	function handleSort(field: string) {
		const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
		onsort?.({ field, order: newOrder });
	}

	// 그룹 클릭 핸들러
	function handleGroupClick(groupId: number) {
		ongroupclick?.({ groupId });
	}

	// 읽음률 색상 계산
	function getReadRateColor(rate: number): string {
		if (rate >= 80) return 'text-success';
		if (rate >= 60) return 'text-warning';
		return 'text-error';
	}

	// 읽음률 배지 스타일 계산
	function getReadRateBadge(rate: number): string {
		if (rate >= 80) return 'badge-success';
		if (rate >= 60) return 'badge-warning';
		return 'badge-error';
	}

	// 정렬 아이콘 표시
	function getSortIcon(field: string): string {
		if (sortBy !== field) return '↕️';
		return sortOrder === 'asc' ? '↑' : '↓';
	}

	// 최근 활동 시간 포맷팅
	function formatLastActivity(dateString: string | null | undefined): string {
		if (!dateString) return '활동 없음';

		const date = new Date(dateString);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffHours / 24);

		if (diffHours < 1) return '방금 전';
		if (diffHours < 24) return `${diffHours}시간 전`;
		if (diffDays < 7) return `${diffDays}일 전`;

		return date.toLocaleDateString('ko-KR', {
			month: 'short',
			day: 'numeric'
		});
	}
</script>

<div class="card bg-base-100 shadow-sm border border-base-300">
	<div class="card-body">
		<div class="flex justify-between items-center mb-4">
			<h3 class="card-title text-lg">그룹별 알림 수신 현황</h3>
			<div class="text-sm text-base-content/70">
				{#if data.length > 0}
					총 {data.length}개 그룹 표시
				{:else}
					그룹이 없습니다
				{/if}
			</div>
		</div>

		<div class="overflow-x-auto">
			<table class="table table-zebra">
				<thead>
					<tr>
						<th>
							<button
								class="btn btn-ghost btn-sm p-0 h-auto min-h-0 font-semibold"
								onclick={() => handleSort('name')}
							>
								그룹명 {getSortIcon('name')}
							</button>
						</th>
						<th>
							<button
								class="btn btn-ghost btn-sm p-0 h-auto min-h-0 font-semibold"
								onclick={() => handleSort('members_count')}
							>
								멤버 수 {getSortIcon('members_count')}
							</button>
						</th>
						<th>
							<button
								class="btn btn-ghost btn-sm p-0 h-auto min-h-0 font-semibold"
								onclick={() => handleSort('sent_count')}
							>
								전송된 알림 {getSortIcon('sent_count')}
							</button>
						</th>
						<th>
							<button
								class="btn btn-ghost btn-sm p-0 h-auto min-h-0 font-semibold"
								onclick={() => handleSort('read_rate')}
							>
								읽음률 {getSortIcon('read_rate')}
							</button>
						</th>
						<th>
							<button
								class="btn btn-ghost btn-sm p-0 h-auto min-h-0 font-semibold"
								onclick={() => handleSort('last_activity')}
							>
								최근 활동 {getSortIcon('last_activity')}
							</button>
						</th>
						<th>액션</th>
					</tr>
				</thead>
				<tbody>
					{#if loading}
						<tr>
							<td colspan="6" class="text-center py-8">
								<div class="flex justify-center items-center gap-2">
									<span class="loading loading-spinner loading-sm"></span>
									<span class="text-base-content/50">데이터를 불러오는 중...</span>
								</div>
							</td>
						</tr>
					{:else if data.length === 0}
						<tr>
							<td colspan="6" class="text-center text-base-content/50 py-8">
								등록된 그룹이 없습니다.
							</td>
						</tr>
					{:else}
						{#each data as group (group.id)}
							<tr class="hover:bg-base-200/50 cursor-pointer">
								<td>
									<div class="flex flex-col">
										<button
											class="font-medium text-left hover:text-primary transition-colors"
											onclick={() => handleGroupClick(group.id)}
										>
											{group.name}
										</button>
										{#if group.description}
											<div class="text-xs text-base-content/60 mt-1">
												{group.description}
											</div>
										{/if}
									</div>
								</td>
								<td>
									<div class="flex items-center gap-2">
										<span class="font-medium">{group.members_count}</span>
										<span class="text-xs text-base-content/60">명</span>
									</div>
								</td>
								<td>
									<div class="flex items-center gap-2">
										<span class="font-medium">{group.sent_count}</span>
										<span class="text-xs text-base-content/60">건</span>
									</div>
								</td>
								<td>
									<div class="flex items-center gap-2">
										<div class="badge {getReadRateBadge(group.read_rate)} badge-sm">
											{group.read_rate}%
										</div>
										<div class="w-16 bg-base-300 rounded-full h-2">
											<div
												class="h-2 rounded-full transition-all duration-300 {group.read_rate >= 80
													? 'bg-success'
													: group.read_rate >= 60
														? 'bg-warning'
														: 'bg-error'}"
												style="width: {group.read_rate}%"
											></div>
										</div>
									</div>
								</td>
								<td>
									<div
										class="text-sm {group.last_activity
											? 'text-base-content'
											: 'text-base-content/50'}"
									>
										{formatLastActivity(group.last_activity)}
									</div>
								</td>
								<td>
									<div class="flex gap-1">
										<button
											class="btn btn-ghost btn-xs"
											onclick={() => handleGroupClick(group.id)}
											title="그룹 상세 보기"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="h-3 w-3 fill-none stroke-current"
												viewBox="0 0 24 24"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
												/>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
												/>
											</svg>
											그룹 상세 보기
										</button>
									</div>
								</td>
							</tr>
						{/each}
					{/if}
				</tbody>
			</table>
		</div>

		{#if data.length > 0 && !loading}
			<div class="flex justify-between items-center mt-4 text-sm text-base-content/70">
				<div>
					총 {data.reduce((sum, group) => sum + group.members_count, 0)}명의 직원이 {data.length}개
					그룹에 소속
				</div>
				<div>
					평균 읽음률: {Math.round(
						data.reduce((sum, group) => sum + group.read_rate, 0) / data.length
					)}%
				</div>
			</div>
		{/if}
	</div>
</div>
