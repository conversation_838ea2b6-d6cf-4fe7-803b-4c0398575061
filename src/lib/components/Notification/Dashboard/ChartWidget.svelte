<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		Chart,
		CategoryScale,
		LinearScale,
		BarElement,
		LineElement,
		PointElement,
		BarController,
		LineController,
		Title,
		Tooltip,
		Legend,
		Filler
	} from 'chart.js';
	import Icon from 'svelte-awesome';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faChartLine } from '@fortawesome/free-solid-svg-icons/faChartLine';
	import { faChartBar } from '@fortawesome/free-solid-svg-icons/faChartBar';

	// Chart.js 컴포넌트 등록
	Chart.register(
		CategoryScale,
		LinearScale,
		BarElement,
		LineElement,
		PointElement,
		BarController,
		LineController,
		Title,
		Tooltip,
		Legend,
		Filler
	);

	// Props using Svelte 5 $props rune
	let {
		type = 'trend',
		title,
		data = [],
		loading = false,
		height = 300,
		color = 'primary'
	}: {
		type?: 'trend' | 'priority';
		title: string;
		data: any[];
		loading?: boolean;
		height?: number;
		color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info';
	} = $props();

	// 차트 인스턴스와 캔버스 참조
	let chartCanvas: HTMLCanvasElement;
	let chartInstance: Chart | null = null;

	// 색상 매핑
	const colorMap = {
		primary: 'rgb(99, 102, 241)',
		secondary: 'rgb(244, 114, 182)',
		accent: 'rgb(34, 197, 94)',
		success: 'rgb(34, 197, 94)',
		warning: 'rgb(251, 191, 36)',
		error: 'rgb(239, 68, 68)',
		info: 'rgb(59, 130, 246)'
	};

	// 투명도 적용 함수
	function transparentize(color: string, opacity: number): string {
		const rgb = color.match(/\d+/g);
		if (!rgb) return color;
		return `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${opacity})`;
	}

	// 최근 7일간 알림 전송 추이 차트 설정
	function createTrendChart() {
		if (!chartCanvas || !data.length) return;

		const labels = data.map((item) => item.date || item.label);
		const values = data.map((item) => item.count || item.value);

		const config = {
			type: 'line' as const,
			data: {
				labels,
				datasets: [
					{
						label: '알림 전송 수',
						data: values,
						borderColor: colorMap[color],
						backgroundColor: transparentize(colorMap[color], 0.1),
						borderWidth: 2,
						fill: true,
						tension: 0.4,
						pointBackgroundColor: colorMap[color],
						pointBorderColor: '#ffffff',
						pointBorderWidth: 2,
						pointRadius: 4,
						pointHoverRadius: 6
					}
				]
			},
			options: {
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					title: {
						display: false
					},
					legend: {
						display: false
					},
					tooltip: {
						backgroundColor: 'rgba(0, 0, 0, 0.8)',
						titleColor: '#ffffff',
						bodyColor: '#ffffff',
						borderColor: colorMap[color],
						borderWidth: 1,
						cornerRadius: 8,
						displayColors: false,
						callbacks: {
							title: function (context: any) {
								return context[0].label;
							},
							label: function (context: any) {
								return `알림 전송: ${context.parsed.y}건`;
							}
						}
					}
				},
				scales: {
					x: {
						display: true,
						grid: {
							display: false
						},
						ticks: {
							color: 'rgb(156, 163, 175)',
							font: {
								size: 12
							}
						}
					},
					y: {
						display: true,
						beginAtZero: true,
						grid: {
							color: 'rgba(156, 163, 175, 0.1)',
							drawBorder: false
						},
						ticks: {
							color: 'rgb(156, 163, 175)',
							font: {
								size: 12
							},
							callback: function (value: any) {
								return value + '건';
							}
						}
					}
				},
				interaction: {
					intersect: false,
					mode: 'index' as const
				},
				elements: {
					point: {
						hoverBackgroundColor: colorMap[color]
					}
				}
			}
		};

		chartInstance = new Chart(chartCanvas, config);
	}

	// 우선순위별 읽음률 차트 설정
	function createPriorityChart() {
		if (!chartCanvas || !data.length) return;

		const labels = data.map((item) => {
			const priorityMap: Record<string, string> = {
				urgent: '긴급',
				high: '높음',
				normal: '보통',
				low: '낮음'
			};
			return priorityMap[item.priority] || item.priority;
		});
		const readRates = data.map((item) => item.read_rate || item.value);

		// 우선순위별 색상
		const priorityColors = data.map((item) => {
			const colorMap: Record<string, string> = {
				urgent: 'rgb(239, 68, 68)', // 빨강
				high: 'rgb(251, 191, 36)', // 주황
				normal: 'rgb(59, 130, 246)', // 파랑
				low: 'rgb(156, 163, 175)' // 회색
			};
			return colorMap[item.priority] || 'rgb(99, 102, 241)';
		});

		const config = {
			type: 'bar' as const,
			data: {
				labels,
				datasets: [
					{
						label: '읽음률',
						data: readRates,
						backgroundColor: priorityColors.map((color) => transparentize(color, 0.8)),
						borderColor: priorityColors,
						borderWidth: 2,
						borderRadius: 4,
						borderSkipped: false
					}
				]
			},
			options: {
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					title: {
						display: false
					},
					legend: {
						display: false
					},
					tooltip: {
						backgroundColor: 'rgba(0, 0, 0, 0.8)',
						titleColor: '#ffffff',
						bodyColor: '#ffffff',
						borderColor: 'rgba(255, 255, 255, 0.1)',
						borderWidth: 1,
						cornerRadius: 8,
						displayColors: true,
						callbacks: {
							title: function (context: any) {
								return `${context[0].label} 우선순위`;
							},
							label: function (context: any) {
								return `읽음률: ${context.parsed.y}%`;
							}
						}
					}
				},
				scales: {
					x: {
						display: true,
						grid: {
							display: false
						},
						ticks: {
							color: 'rgb(156, 163, 175)',
							font: {
								size: 12
							}
						}
					},
					y: {
						display: true,
						beginAtZero: true,
						max: 100,
						grid: {
							color: 'rgba(156, 163, 175, 0.1)',
							drawBorder: false
						},
						ticks: {
							color: 'rgb(156, 163, 175)',
							font: {
								size: 12
							},
							callback: function (value: any) {
								return value + '%';
							}
						}
					}
				},
				interaction: {
					intersect: false,
					mode: 'index' as const
				}
			}
		};

		chartInstance = new Chart(chartCanvas, config);
	}

	// 차트 생성 함수
	function createChart() {
		if (chartInstance) {
			chartInstance.destroy();
			chartInstance = null;
		}

		if (loading || !data.length) return;

		if (type === 'trend') {
			createTrendChart();
		} else if (type === 'priority') {
			createPriorityChart();
		}
	}

	// 차트 업데이트 함수
	function updateChart() {
		if (!chartInstance || loading) return;

		if (type === 'trend') {
			const labels = data.map((item) => item.date || item.label);
			const values = data.map((item) => item.count || item.value);

			chartInstance.data.labels = labels;
			chartInstance.data.datasets[0].data = values;
		} else if (type === 'priority') {
			const labels = data.map((item) => {
				const priorityMap: Record<string, string> = {
					urgent: '긴급',
					high: '높음',
					normal: '보통',
					low: '낮음'
				};
				return priorityMap[item.priority] || item.priority;
			});
			const readRates = data.map((item) => item.read_rate || item.value);

			chartInstance.data.labels = labels;
			chartInstance.data.datasets[0].data = readRates;
		}

		chartInstance.update('none');
	}

	// 컴포넌트 마운트 시 차트 생성
	onMount(() => {
		createChart();
	});

	// 컴포넌트 언마운트 시 차트 정리
	onDestroy(() => {
		if (chartInstance) {
			chartInstance.destroy();
			chartInstance = null;
		}
	});

	// 데이터 변경 시 차트 업데이트 - Svelte 5 $effect rune 사용
	$effect(() => {
		if (chartCanvas) {
			if (loading) {
				if (chartInstance) {
					chartInstance.destroy();
					chartInstance = null;
				}
			} else {
				if (chartInstance) {
					updateChart();
				} else {
					createChart();
				}
			}
		}
	});
</script>

<div class="card bg-base-100 shadow-md">
	<div class="card-body p-6">
		<!-- 헤더 -->
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center gap-3">
				<div
					class="w-10 h-10 rounded-lg flex items-center justify-center"
					style="background-color: {transparentize(colorMap[color], 0.1)};"
				>
					{#if loading}
						<Icon data={faSpinner} class="text-lg animate-spin" style="color: {colorMap[color]};" />
					{:else if type === 'trend'}
						<Icon data={faChartLine} class="text-lg" style="color: {colorMap[color]};" />
					{:else}
						<Icon data={faChartBar} class="text-lg" style="color: {colorMap[color]};" />
					{/if}
				</div>
				<h3 class="text-lg font-semibold text-base-content">
					{title}
				</h3>
			</div>
		</div>

		<!-- 차트 영역 -->
		<div class="relative" style="height: {height}px;">
			{#if loading}
				<div class="absolute inset-0 flex items-center justify-center bg-base-100 bg-opacity-50">
					<div class="flex flex-col items-center gap-3">
						<Icon data={faSpinner} class="text-2xl text-base-content animate-spin" />
						<span class="text-sm text-base-content/70">차트 로딩 중...</span>
					</div>
				</div>
			{:else if !data.length}
				<div class="absolute inset-0 flex items-center justify-center">
					<div class="text-center">
						<div class="text-4xl text-base-content/30 mb-2">📊</div>
						<p class="text-sm text-base-content/50">표시할 데이터가 없습니다</p>
					</div>
				</div>
			{/if}

			<canvas bind:this={chartCanvas} class="w-full h-full" class:opacity-30={loading}></canvas>
		</div>
	</div>
</div>

<style>
	/* 차트 캔버스 스타일 */
	canvas {
		max-height: 100%;
	}

	/* 로딩 상태 애니메이션 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
