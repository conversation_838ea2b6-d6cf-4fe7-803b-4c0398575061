<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faArrowUp } from '@fortawesome/free-solid-svg-icons/faArrowUp';
	import { faArrowDown } from '@fortawesome/free-solid-svg-icons/faArrowDown';
	import { faMinus } from '@fortawesome/free-solid-svg-icons/faMinus';

	// Props using Svelte 5 $props rune
	let {
		title,
		value,
		icon,
		color = 'primary',
		loading = false,
		subtitle = '',
		trend = undefined,
		onClick = undefined
	}: {
		title: string;
		value: number | string;
		icon: any;
		color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info';
		loading?: boolean;
		subtitle?: string;
		trend?: number | undefined;
		onClick?: (() => void) | undefined;
	} = $props();

	// 색상 매핑
	const colorClasses: Record<typeof color, string> = {
		primary: 'bg-primary text-primary-content',
		secondary: 'bg-secondary text-secondary-content',
		accent: 'bg-accent text-accent-content',
		success: 'bg-success text-success-content',
		warning: 'bg-warning text-warning-content',
		error: 'bg-error text-error-content',
		info: 'bg-info text-info-content'
	};

	// 트렌드 색상 결정 - Svelte 5 $derived rune 사용
	const trendColor = $derived(
		trend !== undefined
			? trend > 0
				? 'text-success'
				: trend < 0
					? 'text-error'
					: 'text-base-content'
			: ''
	);

	// 클릭 가능한 카드인지 확인 - Svelte 5 $derived rune 사용
	const isClickable = $derived(onClick !== undefined);

	// 카드 클릭 핸들러
	function handleClick() {
		if (onClick) {
			onClick();
		}
	}

	// 키보드 접근성
	function handleKeydown(event: KeyboardEvent) {
		if (isClickable && (event.key === 'Enter' || event.key === ' ')) {
			event.preventDefault();
			handleClick();
		}
	}
</script>

<div
	class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow duration-200 {isClickable
		? 'cursor-pointer hover:bg-base-200'
		: ''}"
	class:animate-pulse={loading}
	onclick={handleClick}
	onkeydown={handleKeydown}
	role={isClickable ? 'button' : undefined}
	tabindex={isClickable ? 0 : undefined}
>
	<div class="card-body p-6">
		<div class="flex items-center justify-between">
			<!-- 아이콘 영역 -->
			<div class="flex-shrink-0">
				<div class="w-12 h-12 rounded-lg {colorClasses[color]} flex items-center justify-center">
					{#if loading}
						<Icon data={faSpinner} class="text-xl animate-spin" />
					{:else}
						<Icon data={icon} class="text-xl" />
					{/if}
				</div>
			</div>

			<!-- 내용 영역 -->
			<div class="flex-1 ml-4 text-right">
				<h3 class="text-sm font-medium text-base-content/70 mb-1">
					{title}
				</h3>

				<div class="flex items-center justify-end gap-2">
					{#if loading}
						<div class="skeleton h-8 w-16"></div>
					{:else}
						<span class="text-2xl font-bold text-base-content">
							{typeof value === 'number' ? value.toLocaleString() : value}
						</span>
					{/if}

					{#if trend !== undefined && !loading}
						<div class="flex items-center {trendColor}">
							{#if trend > 0}
								<Icon data={faArrowUp} class="text-xs mr-1" />
								<span class="text-sm font-medium">+{trend}%</span>
							{:else if trend < 0}
								<Icon data={faArrowDown} class="text-xs mr-1" />
								<span class="text-sm font-medium">{trend}%</span>
							{:else}
								<Icon data={faMinus} class="text-xs mr-1" />
								<span class="text-sm font-medium">0%</span>
							{/if}
						</div>
					{/if}
				</div>

				{#if subtitle && !loading}
					<p class="text-xs text-base-content/50 mt-1">
						{subtitle}
					</p>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.card:focus {
		outline: 2px solid hsl(var(--p));
		outline-offset: 2px;
	}
</style>
