/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import ChartWidget from './ChartWidget.svelte';

// Chart.js 모킹
vi.mock('chart.js', () => ({
	Chart: vi.fn().mockImplementation(() => ({
		destroy: vi.fn(),
		update: vi.fn(),
		data: {
			labels: [],
			datasets: [{ data: [] }]
		}
	})),
	CategoryScale: vi.fn(),
	LinearScale: vi.fn(),
	BarElement: vi.fn(),
	LineElement: vi.fn(),
	PointElement: vi.fn(),
	BarController: vi.fn(),
	LineController: vi.fn(),
	Title: vi.fn(),
	Tooltip: vi.fn(),
	Legend: vi.fn(),
	Filler: vi.fn()
}));

// Font Awesome 아이콘 모킹
vi.mock('svelte-awesome', () => ({
	default: vi.fn()
}));

vi.mock('@fortawesome/free-solid-svg-icons/faSpinner', () => ({
	faSpinner: {}
}));

vi.mock('@fortawesome/free-solid-svg-icons/faChartLine', () => ({
	faChartLine: {}
}));

vi.mock('@fortawesome/free-solid-svg-icons/faChartBar', () => ({
	faChartBar: {}
}));

describe('ChartWidget', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('차트 위젯이 올바르게 렌더링된다', () => {
		const props = {
			type: 'trend' as const,
			title: '테스트 차트',
			data: [
				{ date: '2024-01-01', count: 10 },
				{ date: '2024-01-02', count: 15 }
			],
			loading: false
		};

		render(ChartWidget, { props });

		expect(screen.getByText('테스트 차트')).toBeInTheDocument();
	});

	it('로딩 상태일 때 로딩 메시지를 표시한다', () => {
		const props = {
			type: 'trend' as const,
			title: '테스트 차트',
			data: [],
			loading: true
		};

		render(ChartWidget, { props });

		expect(screen.getByText('차트 로딩 중...')).toBeInTheDocument();
	});

	it('데이터가 없을 때 빈 상태 메시지를 표시한다', () => {
		const props = {
			type: 'trend' as const,
			title: '테스트 차트',
			data: [],
			loading: false
		};

		render(ChartWidget, { props });

		expect(screen.getByText('표시할 데이터가 없습니다')).toBeInTheDocument();
	});

	it('우선순위 차트 타입이 올바르게 설정된다', () => {
		const props = {
			type: 'priority' as const,
			title: '우선순위별 읽음률',
			data: [
				{ priority: 'urgent', read_rate: 85 },
				{ priority: 'high', read_rate: 72 }
			],
			loading: false
		};

		render(ChartWidget, { props });

		expect(screen.getByText('우선순위별 읽음률')).toBeInTheDocument();
	});

	it('추이 차트 타입이 올바르게 설정된다', () => {
		const props = {
			type: 'trend' as const,
			title: '최근 7일 알림 전송 추이',
			data: [
				{ date: '2024-01-01', count: 10 },
				{ date: '2024-01-02', count: 15 },
				{ date: '2024-01-03', count: 8 }
			],
			loading: false
		};

		render(ChartWidget, { props });

		expect(screen.getByText('최근 7일 알림 전송 추이')).toBeInTheDocument();
	});

	it('커스텀 높이가 올바르게 적용된다', () => {
		const props = {
			type: 'trend' as const,
			title: '테스트 차트',
			data: [{ date: '2024-01-01', count: 10 }],
			loading: false,
			height: 400
		};

		const { container } = render(ChartWidget, { props });
		const chartContainer = container.querySelector('[style*="height: 400px"]');

		expect(chartContainer).toBeInTheDocument();
	});

	it('색상 prop이 올바르게 적용된다', () => {
		const props = {
			type: 'trend' as const,
			title: '테스트 차트',
			data: [{ date: '2024-01-01', count: 10 }],
			loading: false,
			color: 'success' as const
		};

		render(ChartWidget, { props });

		// 색상이 적용된 요소가 존재하는지 확인
		expect(screen.getByText('테스트 차트')).toBeInTheDocument();
	});
});
