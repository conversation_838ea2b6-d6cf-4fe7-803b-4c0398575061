import { render, screen, fireEvent } from '@testing-library/svelte';
import { describe, it, expect, vi } from 'vitest';
import StatCard from '../StatCard.svelte';
import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';

describe('StatCard 컴포넌트', () => {
	it('기본 props로 렌더링된다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell
			}
		});

		expect(screen.getByText('테스트 제목')).toBeInTheDocument();
		expect(screen.getByText('42')).toBeInTheDocument();
	});

	it('로딩 상태를 표시한다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				loading: true
			}
		});

		expect(document.querySelector('.skeleton')).toBeInTheDocument();
		expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
	});

	it('문자열 값을 표시한다', () => {
		render(StatCard, {
			props: {
				title: '읽음률',
				value: '85%',
				icon: faBell
			}
		});

		expect(screen.getByText('85%')).toBeInTheDocument();
	});

	it('부제목을 표시한다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				subtitle: '테스트 부제목'
			}
		});

		expect(screen.getByText('테스트 부제목')).toBeInTheDocument();
	});

	it('트렌드를 표시한다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				trend: 15
			}
		});

		expect(screen.getByText('+15%')).toBeInTheDocument();
	});

	it('음수 트렌드를 표시한다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				trend: -10
			}
		});

		expect(screen.getByText('-10%')).toBeInTheDocument();
	});

	it('클릭 이벤트를 처리한다', async () => {
		const onClick = vi.fn();

		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				onClick
			}
		});

		const card = document.querySelector('.card');
		expect(card).toHaveClass('cursor-pointer');

		await fireEvent.click(card!);
		expect(onClick).toHaveBeenCalledTimes(1);
	});

	it('키보드 접근성을 지원한다', async () => {
		const onClick = vi.fn();

		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				onClick
			}
		});

		const card = document.querySelector('.card');
		expect(card).toHaveAttribute('role', 'button');
		expect(card).toHaveAttribute('tabindex', '0');

		await fireEvent.keyDown(card!, { key: 'Enter' });
		expect(onClick).toHaveBeenCalledTimes(1);

		await fireEvent.keyDown(card!, { key: ' ' });
		expect(onClick).toHaveBeenCalledTimes(2);
	});

	it('다양한 색상을 지원한다', () => {
		const { rerender } = render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 42,
				icon: faBell,
				color: 'success'
			}
		});

		expect(document.querySelector('.bg-success')).toBeInTheDocument();

		rerender({
			title: '테스트 제목',
			value: 42,
			icon: faBell,
			color: 'error'
		});

		expect(document.querySelector('.bg-error')).toBeInTheDocument();
	});

	it('숫자 값을 천 단위로 구분한다', () => {
		render(StatCard, {
			props: {
				title: '테스트 제목',
				value: 1234567,
				icon: faBell
			}
		});

		expect(screen.getByText('1,234,567')).toBeInTheDocument();
	});
});
