<script lang="ts">
	import type { User } from '$lib/User';
	import type {
		NotificationTemplate,
		TemplateFormData,
		NotificationPriority
	} from '$lib/types/notification';
	import { createTemplate, updateTemplate } from '$stores/notification/templateStore';
	import {
		NOTIFICATION_PRIORITY_LOW,
		NOTIFICATION_PRIORITY_NORMAL,
		NOTIFICATION_PRIORITY_HIGH,
		NOTIFICATION_PRIORITY_URGENT
	} from '$lib/constants/notification';

	import PriorityBadge from '$components/Notification/Common/PriorityBadge.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-solid-svg-icons/faSave';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';
	import { faFileAlt } from '@fortawesome/free-solid-svg-icons/faFileAlt';

	interface Props {
		show: boolean;
		user: User;
		template?: NotificationTemplate | null;
		onclose?: () => void;
		onsaved?: () => void;
	}

	let { show, user, template = null, onclose, onsaved }: Props = $props();

	// 폼 데이터 상태
	let formData = $state<TemplateFormData>({
		name: '',
		title: '',
		content: '',
		priority: NOTIFICATION_PRIORITY_NORMAL
	});

	let isSubmitting = $state(false);
	let errors = $state<Record<string, string>>({});
	let showPreview = $state(false);

	// 편집 모드 여부
	const isEditMode = $derived(template !== null);
	const modalTitle = $derived(isEditMode ? '템플릿 수정' : '새 템플릿 생성');

	// 우선순위 옵션
	const priorityOptions = [
		{ value: NOTIFICATION_PRIORITY_LOW, text: '낮음', color: 'badge-ghost' },
		{ value: NOTIFICATION_PRIORITY_NORMAL, text: '보통', color: 'badge-info' },
		{ value: NOTIFICATION_PRIORITY_HIGH, text: '높음', color: 'badge-warning' },
		{ value: NOTIFICATION_PRIORITY_URGENT, text: '긴급', color: 'badge-error' }
	];

	// 템플릿 데이터로 폼 초기화
	$effect(() => {
		if (show) {
			if (template) {
				// 수정 모드: 기존 템플릿 데이터로 초기화
				formData = {
					name: template.name,
					title: template.title,
					content: template.content,
					priority: template.priority
				};
			} else {
				// 생성 모드: 빈 폼으로 초기화
				formData = {
					name: '',
					title: '',
					content: '',
					priority: NOTIFICATION_PRIORITY_NORMAL
				};
			}
			errors = {};
			showPreview = false;
		}
	});

	// 폼 유효성 검사
	function validateForm(): boolean {
		const newErrors: Record<string, string> = {};

		// 템플릿명 검증
		if (!formData.name.trim()) {
			newErrors.name = '템플릿명을 입력해주세요.';
		} else if (formData.name.trim().length < 2) {
			newErrors.name = '템플릿명은 최소 2자 이상 입력해주세요.';
		} else if (formData.name.trim().length > 100) {
			newErrors.name = '템플릿명은 100자 이내로 입력해주세요.';
		}

		// 제목 검증
		if (!formData.title.trim()) {
			newErrors.title = '제목을 입력해주세요.';
		} else if (formData.title.trim().length < 2) {
			newErrors.title = '제목은 최소 2자 이상 입력해주세요.';
		} else if (formData.title.trim().length > 200) {
			newErrors.title = '제목은 200자 이내로 입력해주세요.';
		}

		// 내용 검증
		if (!formData.content.trim()) {
			newErrors.content = '내용을 입력해주세요.';
		} else if (formData.content.trim().length < 5) {
			newErrors.content = '내용은 최소 5자 이상 입력해주세요.';
		} else if (formData.content.trim().length > 1000) {
			newErrors.content = '내용은 1000자 이내로 입력해주세요.';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	}

	// 템플릿 저장 처리
	async function handleSubmit() {
		if (!validateForm()) {
			// 첫 번째 에러 필드로 포커스 이동
			const firstErrorField = Object.keys(errors)[0];
			if (firstErrorField) {
				const element = document.getElementById(firstErrorField);
				element?.focus();
			}
			return;
		}

		isSubmitting = true;

		try {
			let success = false;

			if (isEditMode && template) {
				// 수정 모드
				success = await updateTemplate(user, template.id, formData);
			} else {
				// 생성 모드
				success = await createTemplate(user, formData);
			}

			if (success) {
				onsaved?.();
			}
		} finally {
			isSubmitting = false;
		}
	}

	// 취소 처리
	function handleCancel() {
		onclose?.();
	}

	// 실시간 유효성 검사
	function handleFieldChange(field: keyof TemplateFormData) {
		// 기존 에러 제거
		if (errors[field]) {
			delete errors[field];
		}

		// 실시간 검증
		const newErrors: Record<string, string> = {};

		if (field === 'name' && formData.name) {
			if (formData.name.trim().length < 2) {
				newErrors.name = '템플릿명은 최소 2자 이상 입력해주세요.';
			} else if (formData.name.trim().length > 100) {
				newErrors.name = '템플릿명은 100자 이내로 입력해주세요.';
			}
		}

		if (field === 'title' && formData.title) {
			if (formData.title.trim().length < 2) {
				newErrors.title = '제목은 최소 2자 이상 입력해주세요.';
			} else if (formData.title.trim().length > 200) {
				newErrors.title = '제목은 200자 이내로 입력해주세요.';
			}
		}

		if (field === 'content' && formData.content) {
			if (formData.content.trim().length < 5) {
				newErrors.content = '내용은 최소 5자 이상 입력해주세요.';
			} else if (formData.content.trim().length > 1000) {
				newErrors.content = '내용은 1000자 이내로 입력해주세요.';
			}
		}

		// 새로운 에러가 있으면 추가
		if (newErrors[field]) {
			errors[field] = newErrors[field];
		}
	}

	// 미리보기 토글
	function togglePreview() {
		showPreview = !showPreview;
	}

	// 문자 수 계산
	const nameLength = $derived(formData.name?.length || 0);
	const titleLength = $derived(formData.title?.length || 0);
	const contentLength = $derived(formData.content?.length || 0);

	// 키보드 이벤트 처리
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleCancel();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if show}
	<div class="modal modal-open">
		<div class="modal-box max-w-4xl w-full h-[90vh] flex flex-col">
			<!-- 모달 헤더 -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center gap-3">
					<Icon data={faFileAlt} class="w-6 h-6 text-primary" />
					<h3 class="font-bold text-xl">{modalTitle}</h3>
				</div>
				<div class="flex items-center gap-2">
					<button
						class="btn btn-ghost btn-sm"
						onclick={togglePreview}
						class:btn-active={showPreview}
					>
						<Icon data={faEye} />
						미리보기
					</button>
					<button class="btn btn-ghost btn-sm btn-circle" onclick={handleCancel}>
						<Icon data={faTimes} />
					</button>
				</div>
			</div>

			<!-- 메인 콘텐츠 -->
			<div class="flex-1 overflow-hidden">
				<div class="grid grid-cols-1 {showPreview ? 'lg:grid-cols-2' : ''} gap-6 h-full">
					<!-- 폼 영역 -->
					<div class="flex flex-col h-full">
						<form
							onsubmit={(e) => {
								e.preventDefault();
								handleSubmit();
							}}
							class="flex flex-col h-full space-y-4"
						>
							<!-- 템플릿명 입력 -->
							<div class="form-control">
								<label class="label" for="template-name">
									<span class="label-text font-medium">
										템플릿명 <span class="text-error">*</span>
									</span>
									<span class="label-text-alt text-sm">
										{nameLength}/100
									</span>
								</label>
								<input
									id="template-name"
									type="text"
									placeholder="템플릿명을 입력하세요"
									class="input input-bordered w-full"
									class:input-error={errors.name}
									bind:value={formData.name}
									oninput={() => handleFieldChange('name')}
									maxlength="100"
									required
								/>
								{#if errors.name}
									<div class="label">
										<span class="label-text-alt text-error">{errors.name}</span>
									</div>
								{/if}
							</div>

							<!-- 제목 입력 -->
							<div class="form-control">
								<label class="label" for="template-title">
									<span class="label-text font-medium">
										제목 <span class="text-error">*</span>
									</span>
									<span class="label-text-alt text-sm">
										{titleLength}/200
									</span>
								</label>
								<input
									id="template-title"
									type="text"
									placeholder="알림 제목을 입력하세요"
									class="input input-bordered w-full"
									class:input-error={errors.title}
									bind:value={formData.title}
									oninput={() => handleFieldChange('title')}
									maxlength="200"
									required
								/>
								{#if errors.title}
									<div class="label">
										<span class="label-text-alt text-error">{errors.title}</span>
									</div>
								{/if}
							</div>

							<!-- 내용 입력 -->
							<div class="form-control flex-1">
								<label class="label" for="template-content">
									<span class="label-text font-medium">
										내용 <span class="text-error">*</span>
									</span>
									<span class="label-text-alt text-sm">
										{contentLength}/1000
									</span>
								</label>
								<textarea
									id="template-content"
									placeholder="알림 내용을 입력하세요"
									class="textarea textarea-bordered flex-1 w-full resize-none"
									class:textarea-error={errors.content}
									bind:value={formData.content}
									oninput={() => handleFieldChange('content')}
									maxlength="1000"
									required
								></textarea>
								{#if errors.content}
									<div class="label">
										<span class="label-text-alt text-error">{errors.content}</span>
									</div>
								{/if}
							</div>

							<!-- 우선순위 선택 -->
							<div class="form-control">
								<label class="label">
									<span class="label-text font-medium">우선순위</span>
								</label>
								<div class="flex flex-wrap gap-3">
									{#each priorityOptions as option}
										<label
											class="label cursor-pointer flex items-center gap-2 p-3 rounded-lg border border-base-300 hover:border-primary transition-colors"
											class:border-primary={formData.priority === option.value}
											class:bg-primary-100={formData.priority === option.value}
										>
											<input
												type="radio"
												name="priority"
												value={option.value}
												class="radio radio-primary radio-sm"
												bind:group={formData.priority}
											/>
											<PriorityBadge priority={option.value as NotificationPriority} size="sm" />
										</label>
									{/each}
								</div>
							</div>
						</form>
					</div>

					<!-- 미리보기 영역 -->
					{#if showPreview}
						<div class="flex flex-col h-full">
							<div class="label">
								<span class="label-text font-medium">실시간 미리보기</span>
							</div>
							<div class="card bg-base-200 flex-1 overflow-hidden">
								<div class="card-body p-4 overflow-y-auto">
									<div class="space-y-4">
										{#if formData.name}
											<div>
												<div class="text-xs text-base-content/70 mb-1">템플릿명</div>
												<div class="font-medium text-base-content">{formData.name}</div>
											</div>
										{/if}

										{#if formData.title}
											<div>
												<div class="text-xs text-base-content/70 mb-1">제목</div>
												<div class="font-medium text-lg text-base-content">{formData.title}</div>
											</div>
										{/if}

										{#if formData.content}
											<div>
												<div class="text-xs text-base-content/70 mb-1">내용</div>
												<div
													class="bg-base-100 p-3 rounded-lg text-base-content whitespace-pre-wrap"
												>
													{formData.content}
												</div>
											</div>
										{/if}

										<div>
											<div class="text-xs text-base-content/70 mb-1">우선순위</div>
											<PriorityBadge priority={formData.priority} />
										</div>

										{#if !formData.name && !formData.title && !formData.content}
											<div class="text-center py-8 text-base-content/50">
												<Icon data={faEye} class="w-12 h-12 mb-2" />
												<p>내용을 입력하면 미리보기가 표시됩니다</p>
											</div>
										{/if}
									</div>
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>

			<!-- 모달 액션 -->
			<div class="flex justify-end gap-3 pt-6 border-t border-base-300 mt-6">
				<button type="button" class="btn btn-ghost" onclick={handleCancel} disabled={isSubmitting}>
					<Icon data={faTimes} />
					취소
				</button>
				<button
					type="button"
					class="btn btn-primary"
					class:loading={isSubmitting}
					onclick={handleSubmit}
					disabled={isSubmitting}
				>
					{#if !isSubmitting}
						<Icon data={faSave} />
					{/if}
					{isSubmitting ? '저장 중...' : isEditMode ? '수정' : '생성'}
				</button>
			</div>
		</div>

		<!-- 모달 백드롭 -->
		<div
			class="modal-backdrop"
			onclick={handleCancel}
			role="button"
			tabindex="-1"
			onkeydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					handleCancel();
				}
			}}
		></div>
	</div>
{/if}

<style>
	.form-control {
		margin-bottom: 0;
	}

	/* 미리보기 영역 스타일링 */
	.card.bg-base-200 {
		border: 1px solid hsl(var(--bc) / 0.1);
	}

	/* 텍스트 영역 자동 크기 조정 방지 */
	.textarea {
		resize: none;
		min-height: 200px;
	}

	/* 입력 필드 포커스 스타일 */
	.input:focus,
	.textarea:focus {
		outline: 2px solid hsl(var(--p));
		outline-offset: 2px;
	}

	/* 라디오 버튼 그룹 스타일링 */
	.label.cursor-pointer:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	/* 모달 크기 조정 */
	.modal-box {
		max-height: 90vh;
		overflow: hidden;
	}

	/* 그리드 레이아웃 반응형 */
	@media (max-width: 1024px) {
		.modal-box {
			max-width: 95vw;
		}
	}

	/* 스크롤바 스타일링 */
	.overflow-y-auto::-webkit-scrollbar {
		width: 6px;
	}

	.overflow-y-auto::-webkit-scrollbar-track {
		background: hsl(var(--b2));
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb {
		background: hsl(var(--bc) / 0.3);
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--bc) / 0.5);
	}
</style>
