<script lang="ts">
	import type { NotificationTargetType, NotificationGroup } from '$lib/types/notification';
	import type { Member } from '$lib/types/types';
	import { getTargetTypeName } from '$lib/constants/notification';
	import TargetTypeIcon from './TargetTypeIcon.svelte';
	import Icon from 'svelte-awesome';
	import { faChevronDown } from '@fortawesome/free-solid-svg-icons/faChevronDown';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';

	interface Props {
		selectedTargetType: NotificationTargetType;
		selectedTargetId?: number;
		groups?: NotificationGroup[];
		members?: Member[];
		onTargetChange: (targetType: NotificationTargetType, targetId?: number) => void;
		disabled?: boolean;
	}

	let {
		selectedTargetType,
		selectedTargetId,
		groups = [],
		members = [],
		onTargetChange,
		disabled = false
	}: Props = $props();

	let searchTerm = $state('');
	let isGroupDropdownOpen = $state(false);
	let isMemberDropdownOpen = $state(false);

	// 필터링된 그룹 목록
	const filteredGroups = $derived(
		groups.filter(
			(group) =>
				group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
		)
	);

	// 필터링된 멤버 목록
	const filteredMembers = $derived(
		members.filter(
			(member) =>
				member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(member.email && member.email.toLowerCase().includes(searchTerm.toLowerCase()))
		)
	);

	// 선택된 대상 정보
	const selectedTargetInfo = $derived.by(() => {
		if (selectedTargetType === 'all') {
			return { name: '전체 직원', count: members.length };
		} else if (selectedTargetType === 'group' && selectedTargetId) {
			const group = groups.find((g) => g.id === selectedTargetId);
			return { name: group?.name || '그룹 선택', count: group?.members_count || 0 };
		} else if (selectedTargetType === 'individual' && selectedTargetId) {
			const member = members.find((m) => m.id === selectedTargetId);
			return { name: member?.name || '직원 선택', count: 1 };
		}
		return { name: '대상 선택', count: 0 };
	});

	function handleTargetTypeChange(targetType: NotificationTargetType) {
		onTargetChange(targetType, undefined);
		isGroupDropdownOpen = false;
		isMemberDropdownOpen = false;
		searchTerm = '';
	}

	function handleGroupSelect(groupId: number) {
		onTargetChange('group', groupId);
		isGroupDropdownOpen = false;
		searchTerm = '';
	}

	function handleMemberSelect(memberId: number) {
		onTargetChange('individual', memberId);
		isMemberDropdownOpen = false;
		searchTerm = '';
	}

	function toggleGroupDropdown() {
		if (disabled) return;
		isGroupDropdownOpen = !isGroupDropdownOpen;
		isMemberDropdownOpen = false;
		searchTerm = '';
	}

	function toggleMemberDropdown() {
		if (disabled) return;
		isMemberDropdownOpen = !isMemberDropdownOpen;
		isGroupDropdownOpen = false;
		searchTerm = '';
	}
</script>

<div class="target-selector">
	<!-- 대상 타입 선택 -->
	<div class="form-control">
		<label class="label">
			<span class="label-text font-medium">전송 대상</span>
		</label>
		<div class="flex flex-wrap gap-2">
			<label class="label cursor-pointer">
				<input
					type="radio"
					name="targetType"
					value="all"
					bind:group={selectedTargetType}
					onchange={() => handleTargetTypeChange('all')}
					{disabled}
					class="radio radio-primary radio-sm"
				/>
				<span class="label-text ml-2 flex items-center gap-2">
					<TargetTypeIcon targetType="all" size="sm" />
					전체 직원
				</span>
			</label>
			<label class="label cursor-pointer">
				<input
					type="radio"
					name="targetType"
					value="group"
					bind:group={selectedTargetType}
					onchange={() => handleTargetTypeChange('group')}
					{disabled}
					class="radio radio-primary radio-sm"
				/>
				<span class="label-text ml-2 flex items-center gap-2">
					<TargetTypeIcon targetType="group" size="sm" />
					특정 그룹
				</span>
			</label>
			<label class="label cursor-pointer">
				<input
					type="radio"
					name="targetType"
					value="individual"
					bind:group={selectedTargetType}
					onchange={() => handleTargetTypeChange('individual')}
					{disabled}
					class="radio radio-primary radio-sm"
				/>
				<span class="label-text ml-2 flex items-center gap-2">
					<TargetTypeIcon targetType="individual" size="sm" />
					개별 직원
				</span>
			</label>
		</div>
	</div>

	<!-- 대상 상세 선택 -->
	{#if selectedTargetType !== 'all'}
		<div class="form-control mt-4">
			<label class="label">
				<span class="label-text font-medium">
					{selectedTargetType === 'group' ? '그룹 선택' : '직원 선택'}
				</span>
			</label>

			{#if selectedTargetType === 'group'}
				<!-- 그룹 선택 드롭다운 -->
				<div class="dropdown dropdown-bottom w-full" class:dropdown-open={isGroupDropdownOpen}>
					<div
						tabindex="0"
						role="button"
						class="btn btn-outline w-full justify-between"
						class:btn-disabled={disabled}
						onclick={toggleGroupDropdown}
					>
						<div class="flex items-center gap-2">
							<TargetTypeIcon targetType="group" size="sm" />
							<span>{selectedTargetInfo().name}</span>
							{#if selectedTargetInfo().count > 0}
								<span class="badge badge-sm badge-ghost">
									{selectedTargetInfo().count}명
								</span>
							{/if}
						</div>
						<Icon data={faChevronDown} class="w-4 h-4" />
					</div>
					<div
						class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full max-h-60 overflow-auto"
					>
						<!-- 검색 입력 -->
						<div class="form-control mb-2">
							<div class="input-group input-group-sm">
								<span class="bg-base-200">
									<Icon data={faSearch} class="w-4 h-4" />
								</span>
								<input
									type="text"
									placeholder="그룹 검색..."
									class="input input-bordered input-sm flex-1"
									bind:value={searchTerm}
								/>
							</div>
						</div>
						<!-- 그룹 목록 -->
						{#each filteredGroups as group (group.id)}
							<li>
								<button
									class="flex items-center justify-between"
									class:active={selectedTargetId === group.id}
									onclick={() => handleGroupSelect(group.id)}
								>
									<div class="flex items-center gap-2">
										<TargetTypeIcon targetType="group" size="sm" />
										<div>
											<div class="font-medium">{group.name}</div>
											{#if group.description}
												<div class="text-xs text-base-content/70">{group.description}</div>
											{/if}
										</div>
									</div>
									<span class="badge badge-sm badge-ghost">
										{group.members_count}명
									</span>
								</button>
							</li>
						{:else}
							<li><span class="text-base-content/50">검색 결과가 없습니다</span></li>
						{/each}
					</div>
				</div>
			{:else if selectedTargetType === 'individual'}
				<!-- 개별 직원 선택 드롭다운 -->
				<div class="dropdown dropdown-bottom w-full" class:dropdown-open={isMemberDropdownOpen}>
					<div
						tabindex="0"
						role="button"
						class="btn btn-outline w-full justify-between"
						class:btn-disabled={disabled}
						onclick={toggleMemberDropdown}
					>
						<div class="flex items-center gap-2">
							<TargetTypeIcon targetType="individual" size="sm" />
							<span>{selectedTargetInfo().name}</span>
						</div>
						<Icon data={faChevronDown} class="w-4 h-4" />
					</div>
					<div
						class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full max-h-60 overflow-auto"
					>
						<!-- 검색 입력 -->
						<div class="form-control mb-2">
							<div class="input-group input-group-sm">
								<span class="bg-base-200">
									<Icon data={faSearch} class="w-4 h-4" />
								</span>
								<input
									type="text"
									placeholder="직원 검색..."
									class="input input-bordered input-sm flex-1"
									bind:value={searchTerm}
								/>
							</div>
						</div>
						<!-- 직원 목록 -->
						{#each filteredMembers as member (member.id)}
							<li>
								<button
									class="flex items-center justify-between"
									class:active={selectedTargetId === member.id}
									onclick={() => handleMemberSelect(member.id)}
								>
									<div class="flex items-center gap-2">
										<TargetTypeIcon targetType="individual" size="sm" />
										<div>
											<div class="font-medium">{member.name}</div>
											{#if member.email}
												<div class="text-xs text-base-content/70">{member.email}</div>
											{/if}
										</div>
									</div>
									{#if member.status === 1}
										<span class="badge badge-sm badge-success">활성</span>
									{:else}
										<span class="badge badge-sm badge-ghost">비활성</span>
									{/if}
								</button>
							</li>
						{:else}
							<li><span class="text-base-content/50">검색 결과가 없습니다</span></li>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}

	<!-- 선택된 대상 미리보기 -->
	<div class="mt-4 p-3 bg-base-200 rounded-lg">
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-2">
				<TargetTypeIcon targetType={selectedTargetType} size="sm" />
				<span class="font-medium">선택된 대상:</span>
				<span>{selectedTargetInfo().name}</span>
			</div>
			<div class="flex items-center gap-2">
				<span class="text-sm text-base-content/70">예상 수신자:</span>
				<span class="badge badge-primary badge-sm">
					{selectedTargetInfo().count}명
				</span>
			</div>
		</div>
	</div>
</div>

<style>
	.target-selector {
		@apply w-full;
	}

	/* 드롭다운 애니메이션 */
	.dropdown-content {
		transition: all 0.2s ease-in-out;
	}

	/* 라디오 버튼 스타일은 DaisyUI 기본 스타일 사용 */

	/* 활성 메뉴 아이템 스타일은 DaisyUI 기본 스타일 사용 */

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.target-selector .flex-wrap {
			@apply flex-col;
		}

		.target-selector .label {
			@apply justify-start;
		}
	}
</style>
