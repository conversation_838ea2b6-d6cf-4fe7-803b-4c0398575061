<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faClock } from '@fortawesome/free-solid-svg-icons/faClock';

	interface Props {
		totalRecipients: number;
		deliveredCount: number;
		readCount: number;
		failedCount?: number;
		isDelivering?: boolean;
		showDetails?: boolean;
		size?: 'sm' | 'md' | 'lg';
	}

	let {
		totalRecipients,
		deliveredCount,
		readCount,
		failedCount = 0,
		isDelivering = false,
		showDetails = true,
		size = 'md'
	}: Props = $props();

	// 전송 상태 계산
	const deliveryStatus = $derived.by(() => {
		if (isDelivering) {
			return {
				status: 'delivering',
				text: '전송 중',
				color: 'text-info',
				bgColor: 'bg-info/10',
				icon: faSpinner,
				animated: true
			};
		}

		if (failedCount > 0) {
			return {
				status: 'partial',
				text: '일부 실패',
				color: 'text-warning',
				bgColor: 'bg-warning/10',
				icon: faExclamationTriangle,
				animated: false
			};
		}

		if (deliveredCount === totalRecipients) {
			return {
				status: 'completed',
				text: '전송 완료',
				color: 'text-success',
				bgColor: 'bg-success/10',
				icon: faCheckCircle,
				animated: false
			};
		}

		return {
			status: 'pending',
			text: '대기 중',
			color: 'text-base-content/50',
			bgColor: 'bg-base-200',
			icon: faClock,
			animated: false
		};
	});

	// 전송률 계산
	const deliveryRate = $derived(
		totalRecipients > 0 ? Math.round((deliveredCount / totalRecipients) * 100) : 0
	);

	// 읽음률 계산
	const readRate = $derived(
		deliveredCount > 0 ? Math.round((readCount / deliveredCount) * 100) : 0
	);

	// 크기별 클래스
	const sizeClasses = $derived({
		container: size === 'sm' ? 'p-2' : size === 'lg' ? 'p-4' : 'p-3',
		icon: size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5',
		text: size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base',
		badge: size === 'sm' ? 'badge-sm' : size === 'lg' ? 'badge-lg' : ''
	});
</script>

<div class="delivery-status-indicator">
	<!-- 메인 상태 표시 -->
	<div class="card bg-base-100 border border-base-300 shadow-sm">
		<div class="card-body {sizeClasses.container}">
			<!-- 상태 헤더 -->
			<div class="flex items-center justify-between mb-3">
				<div class="flex items-center gap-2 {deliveryStatus().color}">
					<Icon
						data={deliveryStatus().icon}
						class="{sizeClasses.icon} {deliveryStatus().animated ? 'animate-spin' : ''}"
					/>
					<span class="font-semibold {sizeClasses.text}">
						{deliveryStatus().text}
					</span>
				</div>
				<div class="text-right">
					<div class="text-sm text-base-content/70">전송률</div>
					<div class="font-bold {sizeClasses.text}">{deliveryRate}%</div>
				</div>
			</div>

			<!-- 진행률 바 -->
			<div class="w-full bg-base-200 rounded-full h-2 mb-3">
				<div
					class="bg-primary h-2 rounded-full transition-all duration-300"
					style="width: {deliveryRate}%"
				></div>
			</div>

			<!-- 상세 정보 -->
			{#if showDetails}
				<div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-center">
					<!-- 총 수신자 -->
					<div class="stat-item">
						<div class="text-xs text-base-content/70">총 수신자</div>
						<div class="font-bold {sizeClasses.text}">{totalRecipients}</div>
					</div>

					<!-- 전송 완료 -->
					<div class="stat-item">
						<div class="text-xs text-base-content/70">전송 완료</div>
						<div class="font-bold text-success {sizeClasses.text}">{deliveredCount}</div>
					</div>

					<!-- 읽음 -->
					<div class="stat-item">
						<div class="text-xs text-base-content/70">읽음</div>
						<div class="font-bold text-info {sizeClasses.text}">
							{readCount}
							<span class="text-xs">({readRate}%)</span>
						</div>
					</div>

					<!-- 실패 -->
					{#if failedCount > 0}
						<div class="stat-item">
							<div class="text-xs text-base-content/70">실패</div>
							<div class="font-bold text-error {sizeClasses.text}">{failedCount}</div>
						</div>
					{:else}
						<div class="stat-item">
							<div class="text-xs text-base-content/70">대기</div>
							<div class="font-bold text-base-content/50 {sizeClasses.text}">
								{totalRecipients - deliveredCount}
							</div>
						</div>
					{/if}
				</div>
			{:else}
				<!-- 간단한 요약 -->
				<div class="flex items-center justify-between text-sm">
					<span class="text-base-content/70">
						{deliveredCount}/{totalRecipients} 전송
					</span>
					<span class="text-base-content/70">
						{readCount}명 읽음 ({readRate}%)
					</span>
				</div>
			{/if}

			<!-- 상태별 배지 -->
			<div class="flex justify-center mt-3">
				<div class="badge {deliveryStatus().color.replace('text-', 'badge-')} {sizeClasses.badge}">
					{deliveryStatus().text}
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	.delivery-status-indicator {
		@apply w-full;
	}

	.stat-item {
		@apply p-2 bg-base-200/50 rounded-lg;
	}

	/* 애니메이션 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 진행률 바 애니메이션 */
	.bg-primary {
		transition: width 0.3s ease-in-out;
	}

	/* 배지 색상 매핑 */
	.badge-info {
		@apply bg-info/20 text-info border-info/30;
	}

	.badge-success {
		@apply bg-success/20 text-success border-success/30;
	}

	.badge-warning {
		@apply bg-warning/20 text-warning border-warning/30;
	}

	.badge-error {
		@apply bg-error/20 text-error border-error/30;
	}

	/* 반응형 디자인 */
	@media (max-width: 768px) {
		.grid-cols-4 {
			@apply grid-cols-2;
		}
	}

	@media (max-width: 640px) {
		.delivery-status-indicator .card-body {
			@apply p-2;
		}

		.delivery-status-indicator .stat-item {
			@apply p-1;
		}

		.delivery-status-indicator .text-sm {
			@apply text-xs;
		}
	}
</style>
