<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faInbox } from '@fortawesome/free-solid-svg-icons/faInbox';

	interface Props {
		icon?: any;
		title?: string;
		description?: string;
		actionText?: string;
		actionHandler?: () => void;
	}

	let {
		icon = faInbox,
		title = '데이터가 없습니다',
		description = '',
		actionText = '',
		actionHandler
	}: Props = $props();
</script>

<div class="flex flex-col items-center justify-center py-12 px-4">
	<!-- 아이콘 -->
	<div class="w-16 h-16 bg-base-200 rounded-full flex items-center justify-center mb-4">
		<Icon data={icon} class="w-8 h-8 text-base-content/40" />
	</div>

	<!-- 제목 -->
	<h3 class="text-lg font-semibold text-base-content mb-2">
		{title}
	</h3>

	<!-- 설명 -->
	{#if description}
		<p class="text-base-content/70 text-center max-w-md mb-6">
			{description}
		</p>
	{/if}

	<!-- 액션 버튼 -->
	{#if actionText && actionHandler}
		<button class="btn btn-primary btn-sm" onclick={actionHandler}>
			{actionText}
		</button>
	{/if}
</div>

<style>
	/* Tailwind CSS 클래스 사용으로 별도 스타일 불필요 */
</style>
