<script lang="ts">
	import type { NotificationPriority } from '$lib/types/notification';
	import { getPriorityName, getPriorityColor } from '$lib/constants/notification';
	import Icon from 'svelte-awesome';
	import { faArrowDown } from '@fortawesome/free-solid-svg-icons/faArrowDown';
	import { faMinus } from '@fortawesome/free-solid-svg-icons/faMinus';
	import { faArrowUp } from '@fortawesome/free-solid-svg-icons/faArrowUp';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';

	interface Props {
		priority: NotificationPriority;
		size?: 'sm' | 'md' | 'lg';
		showIcon?: boolean;
	}

	let { priority, size = 'md', showIcon = true }: Props = $props();

	// 우선순위별 아이콘 매핑
	const priorityIcons = {
		low: faArrowDown,
		normal: faMinus,
		high: faArrowUp,
		urgent: faExclamationTriangle
	};

	const priorityName = $derived(getPriorityName(priority));
	const priorityColor = $derived(getPriorityColor(priority));
	const priorityIcon = $derived(priorityIcons[priority]);
	const sizeClass = $derived(size === 'sm' ? 'badge-sm' : size === 'lg' ? 'badge-lg' : '');
</script>

<div class="badge {priorityColor} {sizeClass} gap-1">
	{#if showIcon}
		<Icon data={priorityIcon} class="w-3 h-3" />
	{/if}
	<span>{priorityName}</span>
</div>

<style>
	.badge {
		font-weight: 500;
	}

	.badge-sm {
		font-size: 0.75rem;
		padding: 0.25rem 0.5rem;
	}

	.badge-lg {
		font-size: 0.875rem;
		padding: 0.5rem 0.75rem;
	}
</style>
