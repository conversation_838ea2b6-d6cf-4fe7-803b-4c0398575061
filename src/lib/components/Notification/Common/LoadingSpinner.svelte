<script lang="ts">
	interface Props {
		size?: 'sm' | 'md' | 'lg';
		text?: string;
		showText?: boolean;
		color?: string;
	}

	let {
		size = 'md',
		text = '로딩 중...',
		showText = true,
		color = 'text-primary'
	}: Props = $props();

	const sizeClass = $derived(size === 'sm' ? 'loading-sm' : size === 'lg' ? 'loading-lg' : '');
</script>

<div class="flex flex-col items-center justify-center gap-3 py-8">
	<span class="loading loading-spinner {sizeClass} {color}"></span>
	{#if showText}
		<p class="text-base-content/70 text-sm">{text}</p>
	{/if}
</div>

<style>
	/* DaisyUI loading 클래스 사용으로 별도 스타일 불필요 */
</style>
