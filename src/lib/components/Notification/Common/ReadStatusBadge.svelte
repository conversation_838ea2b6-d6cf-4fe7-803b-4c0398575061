<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';
	import { faEyeSlash } from '@fortawesome/free-solid-svg-icons/faEyeSlash';

	interface Props {
		isRead: boolean;
		size?: 'sm' | 'md' | 'lg';
		showIcon?: boolean;
		showText?: boolean;
	}

	let { isRead, size = 'md', showIcon = true, showText = true }: Props = $props();

	const readStatus = $derived({
		text: isRead ? '읽음' : '읽지않음',
		color: isRead ? 'badge-success' : 'badge-warning',
		icon: isRead ? faEye : faEyeSlash
	});

	const sizeClass = $derived(size === 'sm' ? 'badge-sm' : size === 'lg' ? 'badge-lg' : '');
</script>

<div class="badge {readStatus.color} {sizeClass} gap-1">
	{#if showIcon}
		<Icon data={readStatus.icon} class="w-3 h-3" />
	{/if}
	{#if showText}
		<span>{readStatus.text}</span>
	{/if}
</div>

<style>
	.badge {
		font-weight: 500;
	}

	.badge-sm {
		font-size: 0.75rem;
		padding: 0.25rem 0.5rem;
	}

	.badge-lg {
		font-size: 0.875rem;
		padding: 0.5rem 0.75rem;
	}
</style>
