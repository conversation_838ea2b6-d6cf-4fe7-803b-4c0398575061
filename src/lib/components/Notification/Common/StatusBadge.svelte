<script lang="ts">
	import type { NotificationStatus } from '$lib/types/notification';
	import { getStatusName, getStatusColor } from '$lib/constants/notification';
	import Icon from 'svelte-awesome';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faTimesCircle } from '@fortawesome/free-solid-svg-icons/faTimesCircle';

	interface Props {
		status: NotificationStatus;
		size?: 'sm' | 'md' | 'lg';
		showIcon?: boolean;
	}

	let { status, size = 'md', showIcon = true }: Props = $props();

	// 상태별 아이콘 매핑
	const statusIcons = {
		draft: faEdit,
		sent: faCheckCircle,
		cancelled: faTimesCircle
	};

	const statusName = $derived(getStatusName(status));
	const statusColor = $derived(getStatusColor(status));
	const statusIcon = $derived(statusIcons[status]);
	const sizeClass = $derived(size === 'sm' ? 'badge-sm' : size === 'lg' ? 'badge-lg' : '');
</script>

<div class="badge {statusColor} {sizeClass} gap-1">
	{#if showIcon}
		<Icon data={statusIcon} class="w-3 h-3" />
	{/if}
	<span>{statusName}</span>
</div>

<style>
	.badge {
		font-weight: 500;
	}

	.badge-sm {
		font-size: 0.75rem;
		padding: 0.25rem 0.5rem;
	}

	.badge-lg {
		font-size: 0.875rem;
		padding: 0.5rem 0.75rem;
	}
</style>
