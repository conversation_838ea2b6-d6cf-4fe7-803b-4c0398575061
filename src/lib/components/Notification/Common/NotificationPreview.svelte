<script lang="ts">
	import type { NotificationPriority, NotificationTargetType } from '$lib/types/notification';
	import PriorityBadge from './PriorityBadge.svelte';
	import TargetTypeIcon from './TargetTypeIcon.svelte';
	import Icon from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons/faExternalLinkAlt';
	import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';
	import { faClock } from '@fortawesome/free-solid-svg-icons/faClock';

	interface Props {
		title: string;
		content: string;
		priority: NotificationPriority;
		targetType: NotificationTargetType;
		targetName?: string;
		targetCount?: number;
		actionUrl?: string;
		senderName?: string;
		showMobilePreview?: boolean;
	}

	let {
		title,
		content,
		priority,
		targetType,
		targetName,
		targetCount = 0,
		actionUrl,
		senderName,
		showMobilePreview = false
	}: Props = $props();

	// 현재 시간 (미리보기용)
	const previewTime = $derived(
		new Date().toLocaleString('ko-KR', {
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		})
	);

	// 대상 정보 텍스트
	const targetInfo = $derived.by(() => {
		if (targetType === 'all') {
			return `전체 직원 (${targetCount}명)`;
		} else if (targetType === 'group') {
			return `${targetName || '그룹'} (${targetCount}명)`;
		} else if (targetType === 'individual') {
			return targetName || '개별 직원';
		}
		return '대상 미선택';
	});
</script>

<div class="notification-preview">
	<div class="flex flex-col lg:flex-row gap-4">
		<!-- 데스크톱 미리보기 -->
		<div class="flex-1">
			<h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
				<Icon data={faBell} class="w-5 h-5 text-primary" />
				데스크톱 미리보기
			</h3>
			<div class="mockup-window border border-base-300 bg-base-100">
				<div class="bg-base-200 px-4 py-2">
					<!-- 알림 헤더 -->
					<div class="flex items-center justify-between mb-3">
						<div class="flex items-center gap-2">
							<Icon data={faBell} class="w-4 h-4 text-primary" />
							<span class="font-medium text-sm">새 알림</span>
							<PriorityBadge {priority} size="sm" />
						</div>
						<div class="flex items-center gap-2 text-xs text-base-content/70">
							<Icon data={faClock} class="w-3 h-3" />
							<span>{previewTime}</span>
						</div>
					</div>

					<!-- 알림 내용 -->
					<div class="bg-base-100 rounded-lg p-4 shadow-sm">
						<div class="flex items-start gap-3">
							<div class="avatar placeholder">
								<div class="bg-primary text-primary-content rounded-full w-8 h-8">
									<Icon data={faUser} class="w-4 h-4" />
								</div>
							</div>
							<div class="flex-1">
								<div class="flex items-center gap-2 mb-1">
									{#if senderName}
										<span class="font-medium text-sm">{senderName}</span>
									{:else}
										<span class="font-medium text-sm">관리자</span>
									{/if}
									<span class="text-xs text-base-content/50">•</span>
									<div class="flex items-center gap-1">
										<TargetTypeIcon {targetType} size="sm" />
										<span class="text-xs text-base-content/70">{targetInfo()}</span>
									</div>
								</div>
								<h4 class="font-semibold text-base mb-2">{title || '제목을 입력하세요'}</h4>
								<p class="text-sm text-base-content/80 whitespace-pre-wrap">
									{content || '내용을 입력하세요'}
								</p>
								{#if actionUrl}
									<div class="mt-3">
										<button class="btn btn-primary btn-sm gap-2">
											<Icon data={faExternalLinkAlt} class="w-3 h-3" />
											바로가기
										</button>
									</div>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 모바일 미리보기 (선택적) -->
		{#if showMobilePreview}
			<div class="lg:w-80">
				<h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
					<Icon data={faBell} class="w-5 h-5 text-primary" />
					모바일 미리보기
				</h3>
				<div class="mockup-phone border-primary">
					<div class="camera"></div>
					<div class="display">
						<div class="artboard artboard-demo phone-1 bg-base-200">
							<!-- 모바일 알림 -->
							<div class="p-4">
								<div class="bg-base-100 rounded-lg p-3 shadow-lg">
									<div class="flex items-center gap-2 mb-2">
										<Icon data={faBell} class="w-4 h-4 text-primary" />
										<span class="font-medium text-sm">새 알림</span>
										<PriorityBadge {priority} size="sm" />
									</div>
									<h4 class="font-semibold text-sm mb-1">
										{title || '제목을 입력하세요'}
									</h4>
									<p class="text-xs text-base-content/80 line-clamp-2">
										{content || '내용을 입력하세요'}
									</p>
									<div class="flex items-center justify-between mt-2 text-xs text-base-content/50">
										<span>{previewTime}</span>
										{#if actionUrl}
											<button class="btn btn-primary btn-xs">
												<Icon data={faExternalLinkAlt} class="w-2 h-2" />
											</button>
										{/if}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- 미리보기 정보 -->
	<div class="mt-4 p-3 bg-base-200 rounded-lg">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
			<div class="flex items-center gap-2">
				<span class="font-medium">우선순위:</span>
				<PriorityBadge {priority} size="sm" />
			</div>
			<div class="flex items-center gap-2">
				<span class="font-medium">전송 대상:</span>
				<div class="flex items-center gap-1">
					<TargetTypeIcon {targetType} size="sm" />
					<span>{targetInfo()}</span>
				</div>
			</div>
			{#if actionUrl}
				<div class="flex items-center gap-2">
					<span class="font-medium">액션 URL:</span>
					<span class="text-primary truncate">{actionUrl}</span>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.notification-preview {
		@apply w-full;
	}

	/* 텍스트 줄 제한 */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* 모바일 미리보기 스타일 */
	.mockup-phone .camera {
		@apply bg-base-content;
	}

	.mockup-phone .display {
		@apply bg-base-100;
	}

	/* 반응형 디자인 */
	@media (max-width: 1024px) {
		.notification-preview .lg\\:flex-row {
			@apply flex-col;
		}

		.notification-preview .lg\\:w-80 {
			@apply w-full;
		}
	}

	@media (max-width: 640px) {
		.notification-preview .mockup-window {
			@apply text-xs;
		}

		.notification-preview .grid-cols-3 {
			@apply grid-cols-1;
		}
	}
</style>
