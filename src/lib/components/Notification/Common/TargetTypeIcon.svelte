<script lang="ts">
	import type { NotificationTargetType } from '$lib/types/notification';
	import { getTargetTypeName } from '$lib/constants/notification';
	import Icon from 'svelte-awesome';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import { faUserFriends } from '@fortawesome/free-solid-svg-icons/faUserFriends';
	import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';

	interface Props {
		targetType: NotificationTargetType;
		size?: 'sm' | 'md' | 'lg';
		showText?: boolean;
		color?: string;
	}

	let {
		targetType,
		size = 'md',
		showText = false,
		color = 'text-base-content/70'
	}: Props = $props();

	// 대상 타입별 아이콘 매핑
	const targetTypeIcons = {
		all: faUsers,
		group: faUserFriends,
		individual: faUser
	};

	const targetTypeName = $derived(getTargetTypeName(targetType));
	const targetTypeIcon = $derived(targetTypeIcons[targetType]);
	const sizeClass = $derived(size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4');
	const textSizeClass = $derived(
		size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'
	);
</script>

<div class="flex items-center gap-2 {color}">
	<Icon data={targetTypeIcon} class={sizeClass} />
	{#if showText}
		<span class={textSizeClass}>{targetTypeName}</span>
	{/if}
</div>
