<script lang="ts">
	import type { User } from '$lib/User';

	interface ProfileInfo {
		name: string;
		email: string;
		cellphone: string;
	}

	let { user, profileInfo, onSubmit } = $props<{
		user: User | null | undefined;
		profileInfo: ProfileInfo | null | undefined;
		onSubmit: (data: ProfileInfo) => void;
	}>();

	// 기본값 설정
	let formData = $state({
		name: profileInfo?.name || user?.name || '',
		email: profileInfo?.email || user?.email || '',
		cellphone: profileInfo?.cellphone || user?.cellphone || ''
	});

	// 폼 제출 처리
	function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		onSubmit(formData);
	}
</script>

<form
	class="w-full bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
	id="profile_info_form"
	onsubmit={handleSubmit}
>
	<!-- 헤더 섹션 -->
	<div class="bg-gradient-to-r from-green-600 to-blue-600 px-8 py-6">
		<div class="flex items-center space-x-3">
			<div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
				<svg class="w-6 h-6 text-white fill-none stroke-current" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
					></path>
				</svg>
			</div>
			<div>
				<h2 class="text-xl font-bold text-white">내 정보 수정</h2>
				<p class="text-green-100 text-sm">개인 정보를 안전하게 관리하세요</p>
			</div>
		</div>
	</div>

	<!-- 폼 내용 -->
	<div class="px-8 py-6">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<!-- 아이디 (읽기 전용) -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="username"> 아이디(ID) </label>
				<div
					class="w-full px-4 py-3 bg-gray-100 border border-gray-200 rounded-xl text-gray-700 font-medium"
				>
					{user?.username || '로딩 중...'}
				</div>
			</div>

			<!-- 이름 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="name"> 이름 </label>
				<input
					bind:value={formData.name}
					class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
					id="name"
					name="name"
					placeholder="이름을 입력하세요"
					required
					type="text"
				/>
			</div>

			<!-- 이메일 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="email"> 이메일(E-mail) </label>
				<input
					bind:value={formData.email}
					class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
					id="email"
					name="email"
					placeholder="이메일을 입력하세요"
					type="email"
				/>
			</div>

			<!-- 연락처 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="cellphone">
					연락처(휴대폰)
				</label>
				<input
					bind:value={formData.cellphone}
					class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
					id="cellphone"
					name="cellphone"
					placeholder="휴대폰 번호를 입력하세요"
					required
					type="text"
				/>
			</div>
		</div>
	</div>

	<!-- 제출 버튼 -->
	<div class="px-8 py-6 bg-gray-50 border-t border-gray-100">
		<button
			class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-lg hover:shadow-xl"
			type="submit"
		>
			<div class="flex items-center justify-center space-x-2">
				<svg class="w-5 h-5 fill-none stroke-current" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
					></path>
				</svg>
				<span>정보 수정</span>
			</div>
		</button>
	</div>
</form>
