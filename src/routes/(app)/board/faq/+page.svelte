<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types/types';

	import { getNumberFormat } from '$lib/Functions';
	import { faqStore, loadFaq } from '$stores/faqStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import SearchButton from '$components/Button/Search.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faLayerGroup } from '@fortawesome/free-solid-svg-icons/faLayerGroup';
	import { faFontAwesome } from '@fortawesome/free-solid-svg-icons/faFontAwesome';

	let user: User = getUser();

	const apiUrl = '/wms/faqs';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let keyword = $state(page.url.searchParams.get('keyword') ?? ''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');

	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadFaq(`${apiUrl}?${apiSearchParams}`, user);

		if ($faqStore) {
			startNo = $faqStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;

		makeData();
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: 'FAQ', url: '/board/faq' },
		{ title: 'FAQ 리스트', url: '/board/faq' }
	];
</script>

<svelte:head>
	<title>FAQ</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<SearchField>
						<SearchFieldTitle title="검색" />
						<SearchFieldContent>
							<label
								class="input input-sm input-bordered flex items-center justify-center gap-2 mx-2"
							>
								<input
									bind:value={keyword}
									class="grow bg-base-100"
									onkeydown={(e) => {
										if (e.key === 'Enter') {
											makeData();
										}
									}}
									type="text"
								/>

								<span
									onclick={() => {
										keyword = '';
									}}
									role="presentation"
								>
									<Icon class="cursor-pointer" data={faXmark} />
								</span>
							</label>

							<SearchButton onclick={makeData} tooltipData="검색" useTooltip={true} />
						</SearchFieldContent>
					</SearchField>
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					{#if user.role === 'Admin' || user.role === 'Super-Admin'}
						<TableTop onUpdate={changeSearchParams} {pageSize} total={$faqStore.pageTotal ?? 0}>
							{#snippet right()}
								<div class="pl-3">
									<button
										class="btn btn-error btn-sm"
										onclick={() => goto(`${localUrl}/create?${searchParams}`)}
										type="button"
									>
										<Icon data={faPlus} />
										글쓰기
									</button>
								</div>
							{/snippet}
						</TableTop>
					{:else}
						<TableTop onUpdate={changeSearchParams} {pageSize} total={$faqStore.pageTotal ?? 0} />
					{/if}

					<table class="table table-pin-rows">
						<colgroup>
							<col style="width: 7%" />
							<col style="width: 15%" />
							<col style="width: 8%" />
							<col />
						</colgroup>

						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th>번호</th>
								<th>
									<Icon data={faLayerGroup} />
									제품
								</th>
								<th>
									<Icon data={faFontAwesome} />
									유형
								</th>
								<th>제목</th>
							</tr>
						</thead>

						<tbody>
							{#if $faqStore.items}
								{#each $faqStore.items as item, index}
									<!--전체 게시물 루프-->
									<tr class="hover:bg-base-content/10">
										<td class="p-1 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="p-1">
											<b>{item.cate1}</b>
											{#if item.cate2}
												> {item.cate2}
											{/if}
										</td>
										<td class="p-1 text-center">
											{item.solution_code}
										</td>
										<td class="p-1">
											<a href="{localUrl}/view?id={item.id}&{searchParams}">
												{item.subject}
											</a>
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if $faqStore.pageTotal && $faqStore.pageTotal > 0}
					<Paginate store={faqStore} {localUrl} onPageChange={handlePageChange} {searchParams} />
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>
