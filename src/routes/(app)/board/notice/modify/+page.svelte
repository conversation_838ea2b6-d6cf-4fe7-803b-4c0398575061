<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import { executeMessage, handleCatch } from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Back from '$components/UI/Back.svelte';
	import Tiptap from '$components/Tiptap/Tiptap.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faCancel } from '@fortawesome/free-solid-svg-icons/faCancel';

	const user: User = getUser();

	const id = page.url.searchParams.get('id');
	const searchParams = page.url.search;
	const apiUrl = `/wms/board/${id}`;
	const prevUrl = `/board/notice/view${searchParams}`;
	let isLoading = $state(false); // 로딩중 표시 여부

	let editor: Tiptap = $state();
	let article;
	let subject = $state('');
	let content = $state(''); // 수정시 사용
	let editorContent = ''; // editor의 content 저장
	let checked = $state(false);

	async function makeData() {
		isLoading = true;

		try {
			const { status, data } = await authClient.get(apiUrl);
			if (status === 200 && data.success) {
				article = data.data.article;

				subject = article.subject;
				content = article.content;
				checked = article.f_notice === 'Y';
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		isLoading = true;

		if (!subject) {
			await executeMessage('제목을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		if (editor) {
			editorContent = editor.getHTML() as string;
		}
		if (!editorContent) {
			await executeMessage('내용을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		try {
			const payload = {
				user: user,
				subject: subject,
				content: editorContent,
				notice: checked ? 'Y' : 'N',
				show: 'Y'
			};

			const { status, data } = await authClient.put(apiUrl, payload);
			if (status === 200 && data.success) {
				toast.success('수정 되었습니다.');

				await goto(prevUrl);
			} else {
				toast.error(data.error);
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '공지사항', url: '/board/notice' },
		{ title: '공지사항 리스트', url: '/board/notice' },
		{ title: '공지사항 수정', url: '#' }
	];
</script>

<svelte:head>
	<title>공지사항 > 공지사항 수정</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<Back />
				
				<div class="w-full px-3">
					<div class="w-full flex flex-col p-4 shadow-lg max-w-4xl">
						<div class="w-full h-[680px] flex flex-col">
							<input bind:value={subject} class="input input-bordered outline-none" placeholder="제목" type="text">
							
							<Tiptap bind:this={editor} content={content} />
						</div>
						
						<!-- Buttons -->
						<div class="w-full flex items-center justify-between">
							<div>
								<label>
									<input bind:checked={checked} class="checkbox checkbox-primary" type="checkbox" /> 중요 공지
								</label>
							</div>
							
							<div class="flex">
								<button class="btn btn-primary"
												onclick={handleSubmit}
												type="button"
								>
									<Icon data={faSave} />
									수정
								</button>
								
								<div class="px-2"></div>
								
								<button class="btn btn-secondary"
												onclick={() => goto(prevUrl)}
												type="button"
								>
									<Icon data={faCancel} />
									취소
								</button>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>