<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';
	import {
		CARRYOUT_PRODUCT_STATUS_ONBOARD,
		carryoutProductStore,
		getCarryoutProductStatusName,
		loadItems
	} from '$stores/carryoutProductStore';
	import {
		executeAsk,
		executeMessage,
		formatDateTimeToString, formatDateToString, getBeginAt,
		getNumberFormat,
		getPayload, handleCatch,
		handleDownload
	} from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import SearchProductCarryoutStatus from '$components/Snippets/SearchProductCarryoutStatus.svelte';
	import SearchQaidCarryoutProduct from '$components/Snippets/SearchQaidCarryoutProduct.svelte';
	import SearchRepairStatus from '$components/Snippets/SearchRepairStatus.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';
	import ButtonSelectedDelete from '$components/Button/SelectedDelete.svelte';

	import Icon from 'svelte-awesome';
	import { faCheckDouble } from '@fortawesome/free-solid-svg-icons/faCheckDouble';

	const user: User = getUser();
	const apiUrl = '/wms/carryout/products';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let carryoutId = $state(page.url.searchParams.get('id') ?? '');
	let beginAt = $state(carryoutId ? null : getBeginAt());
	let endAt = $state(carryoutId ? null : formatDateToString(new Date()));
	let productCarryinYN = $state(page.url.searchParams.get('productCarryinYN') || '');
	let repairStatus = $state(page.url.searchParams.get('repairStatus') ?? ''); // 수리상태: renovate_status
	let searchType = 'qaid';
	let keyword = $state('');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let pageCarryoutAt = $state(''); // 현재 페이지: 외주반출날짜
	let pageCarryoutStatus = 0; // 현재 페이지: 외주 상품 상태

	// 반입반출 버튼을 위한 개수
	let count = $state({
		carryout: 0,
		carryin: 0
	});

	// 반출 관련 변수들
	let isCarryoutInput = $state(false);
	let focusCarryout: HTMLInputElement; // 페이지 로딩시 입고검수 input에 커서 포커싱
	let checkCarryoutQaid = $state(''); // 반출할 QAID
	let checkCarryoutQaidId = $state(''); // 반출할 QAID의 상품 index

	// 반입 관련 변수들
	let isCarryinInput = $state(false);
	let focusCarryin: HTMLInputElement; // 페이지 로딩시 입고검수 input에 커서 포커싱
	let checkCarryinQaid = $state(''); // 반입할 QAID
	let checkCarryinQaidId = $state(''); // 반입할 QAID의 상품 index
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			exportType: 'carryouts',
			carryoutId: carryoutId,
			beginAt: beginAt ?? '',
			endAt: endAt ?? '',
			repairStatus: repairStatus,
			productCarryinYN: productCarryinYN,
			searchType: searchType,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($carryoutProductStore) {
			startNo = $carryoutProductStore.pageStartNo ?? 0;

			pageCarryoutAt = $carryoutProductStore.carryout?.carryout_at;
			pageCarryoutStatus = $carryoutProductStore.carryout?.status;
			count = $carryoutProductStore.count;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(newPage: number) {
		p = newPage.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;
		if (value.detail.carryoutStatusGroup || value.detail.carryoutStatusGroup === '') productCarryinYN = value.detail.carryoutStatusGroup;
		if (value.detail.repairStatusGroup || value.detail.repairStatusGroup === '') repairStatus = value.detail.repairStatusGroup;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;

		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	// 반출 추가를 위한 QAID input 박스
	async function toggleCarryout() {
		isCarryoutInput = !isCarryoutInput;

		if (isCarryoutInput && isCarryinInput) {
			isCarryinInput = false;
		}

		if (isCarryoutInput) {
			focusCarryout.value = '';
			await tick();
			focusCarryout.focus();
		}
	}

	// 반입 추가를 위한 QAID input 박스
	async function toggleCarryin() {
		isCarryinInput = !isCarryinInput;

		if (isCarryinInput && isCarryoutInput) {
			isCarryoutInput = false;
		}

		if (isCarryinInput) {
			focusCarryin.value = '';
			await tick();
			focusCarryin.focus();
		}
	}

	/**
	 * 반출상품 추가
	 *
	 * @param event
	 */
	async function carryoutQaid(event: Event) {
		event.preventDefault();

		try {
			const payload = {
				_method: 'PATCH',
				carryoutId: carryoutId,
				qaid: checkCarryoutQaid
			};

			const { status, data } = await authClient.post(`${apiUrl}/export`, payload);

			if (status === 200 && data.success) {
				toast.success(`[ ${checkCarryoutQaid} ] 외주 반출 완료`);
				checkCarryoutQaid = '';

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	/**
	 * 반입상품 추가
	 *
	 * @param event
	 */
	async function carryinQaid(event: Event) {
		event.preventDefault();

		try {
			const payload = {
				_method: 'PATCH',
				qaid: checkCarryinQaid
			};

			const { status, data } = await authClient.post(`${apiUrl}/import`, payload);

			if (status === 200 && data.success) {
				toast.success(`반출 상품 [ ${checkCarryinQaid} ] 반입 완료`);
				checkCarryinQaid = '';

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	/**
	 * 선택 삭제
	 */
	async function deleteCarryoutProduct(event: Event) {
		event.preventDefault();

		const count = ids.length;
		if (count === 0) {
			await executeMessage('외주반출대상에서 제외할 상품을 선택해주시기 바랍니다.');
			return false;
		}

		let deleteMessage = `${count === 1 ? '1개의' : `${count}개의`} 상품들`;
		const ask = await executeAsk(`✔ 외주반출대상에서 상품을 제외합니다.\n\n선택한 ${deleteMessage}을 외주반출대상에서 제외하시겠습니까?`, 'info');
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				_method: 'DELETE',
				ids: ids
			};

			const { status, data } = await authClient.post(`${apiUrl}/destroy`, payload);
			if (status === 200 && data.success) {
				toast.success(`선택된 상품들을 삭제했습니다.`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		carryoutProductStore.set({});
		
		// 메뉴에서 바로 넘어 왔을 경우:: 최초 1회만 실행
		if (!carryoutId) {
			productCarryinYN = 'N';
		}

		await makeData();
	});

	const pageTitle = '반출/반입 > 상품 목록';
	const breadcrumbs: Breadcrumb[] = [
		{ title: '반출/반입', url: '/carryout' },
		{ title: '외주 반출 목록', url: '/carryout' },
		{ title: '반출 상품 목록', url: '/carryout/products' }
	];
</script>

<svelte:head>
	<title>{pageTitle}</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					{#if carryoutId}
						<SearchField>
							<SearchFieldTitle title="외주 반출 날짜" />
							<SearchFieldContent>
								{pageCarryoutAt}
							</SearchFieldContent>
						</SearchField>
					{/if}
					<SearchWarehousingDate beginAt={beginAt} endAt={endAt} onUpdate={changeSearchParams} />
					
					<SearchRepairStatus onUpdate={changeSearchParams} repairStatusGroup={repairStatus} />
					
					<SearchProductCarryoutStatus carryoutStatusGroup={productCarryinYN} onUpdate={changeSearchParams} />
					
					<SearchQaidCarryoutProduct keyword={keyword} onUpdate={changeSearchParams}>
						<ButtonExcelDownload onclick={async (e) => {
							e.preventDefault();
							isLoading = true;
							
							const payload = getPayload(apiSearchParams);
							payload.id = carryoutId;
							
							await handleDownload(EXCEL_DOWNLOAD_URL, payload);
							isLoading = false;
						}} useTooltip={true} />
					</SearchQaidCarryoutProduct>
					
					{#if pageCarryoutAt}
						<SearchField>
							<SearchFieldTitle title="외주 반출 추가">
								<input bind:checked={isCarryoutInput}
											 class="checkbox checkbox-warning"
											 onclick={toggleCarryout}
											 type="checkbox"
								/>
							</SearchFieldTitle>
							<SearchFieldContent>
								{#if isCarryoutInput && !isCarryinInput}
									<input bind:value={checkCarryoutQaidId} type="hidden">
									<input bind:this={focusCarryout}
												 bind:value={checkCarryoutQaid}
												 class="input input-bordered input-sm w-48 focus:ring-0 focus:outline-none"
												 onkeydown={e => { if (e.key === "Enter") { carryoutQaid(e); } }}
												 placeholder="QAID 입력"
												 type="text" />
									<button class="btn btn-warning btn-sm"
													onclick={carryoutQaid}
													type="button"
									>
										<Icon data={faCheckDouble} />
										외주 반출 추가
									</button>
								{/if}
								<span class="ml-2">
									* 외주반출 상품(QAID)를 스캔하면 아래 목록에 추가된 상품을 확인 할 수 있습니다.
								</span>
							</SearchFieldContent>
						</SearchField>
					{/if}
					
					{#if count.carryout > 0}
						<SearchField>
							<SearchFieldTitle title="반출 상품 반입">
							<input bind:checked={isCarryinInput}
										 class="checkbox checkbox-warning"
										 onclick={toggleCarryin}
										 type="checkbox"
							/>
							</SearchFieldTitle>
							<SearchFieldContent>
								{#if isCarryinInput && !isCarryoutInput}
									<input bind:value={checkCarryinQaidId} type="hidden">
									<input bind:this={focusCarryin}
												 bind:value={checkCarryinQaid}
												 class="input input-bordered input-sm w-48 focus:ring-0 focus:outline-none"
												 onkeydown={e => { if (e.key === "Enter") { carryinQaid(e); } }}
												 placeholder="QAID 입력"
												 type="text" />
									<button class="btn btn-info btn-sm"
													onclick={carryinQaid}
													type="button"
									>
										<Icon data={faCheckDouble} />
										반출상품 반입
									</button>
								{/if}
								<span class="ml-2">
									* 외주반출 상품(QAID)를 스캔하면 아래 목록에 해상 상품이 반출제외(삭제) 됨을 확인 할 수 있습니다.
								</span>
							</SearchFieldContent>
						</SearchField>
					{/if}
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$carryoutProductStore.pageTotal ?? 0}>
						{#snippet left()}
							<div class="pl-3" >
								<ButtonSelectedDelete onclick={async (event: Event) => await deleteCarryoutProduct(event)} useTooltip={true} />
							</div>
						{/snippet}
					</TableTop>
					
					<table class="table text-xs table-zebra table-pin-rows">
						<thead class="uppercase">
						<tr class="bg-base-content text-base-300 text-center">
							<th rowspan="2">
								<input checked={allChecked}
											 onchange={() => toggleAllCheck($carryoutProductStore.items)}
											 type="checkbox"
								/>
							</th>
							<th rowspan="2">번호</th>
							<th rowspan="2">입고일자</th>
							<th rowspan="2">외주반출일</th>
							<th rowspan="2">반출담당</th>
							<th rowspan="2">QAID</th>
							<th colspan="2">카테고리</th>
							<th rowspan="2">상품명</th>
							<th rowspan="2">상품금액</th>
							{#if productCarryinYN === 'Y'}
								<th rowspan="2">점검상태</th>
								<th rowspan="2">수리상태</th>
								<th rowspan="2">재생등급</th>
								<th rowspan="2">수리금액</th>
								<th rowspan="2">수리내용</th>
								<th rowspan="2">처리상태</th>
								<th rowspan="2">수리일자</th>
								<!--<th rowspan="2">수리(접속)키</th>-->
								<th rowspan="2">반입일자</th>
								<th rowspan="2">반입담당</th>
							{/if}
						</tr>
						<tr class="bg-base-content text-base-300 text-center">
							<td class="text-center">4차</td>
							<td class="text-center">5차</td>
						</tr>
						</thead>
						
						<tbody>
						{#if $carryoutProductStore.items}
							{#each $carryoutProductStore.items as item, index}
								<tr class="hover:bg-base-content/10">
									<td class="w-[50px] p-0.5 text-center">
										{#if item.status === CARRYOUT_PRODUCT_STATUS_ONBOARD}
											<input bind:checked={idChecked[index]}
														 bind:group={ids}
														 value={item.id}
														 type="checkbox"
											/>
										{/if}
									</td>
									<td class="w-[50px] p-0.5 text-center">{getNumberFormat(startNo - index)}</td>
									<td class="w-[90px] max-w-[90px] p-0.5 text-center">{item.req.req_at}</td>
									<td class="w-[90px] max-w-[90px] p-0.5 text-center">{item.carryout.carryout_at}</td>
									<td class="w-[50px] p-0.5 text-center">{item.checked_user.name}</td>
									<td class="w-[100px] max-w-[100px] p-0.5 text-center">{item.qaid}</td>
									<td class="w-[100px] p-0.5 text-center">{item.cate4}</td>
									<td class="w-[100px] p-0.5 text-center">{item.cate5}</td>
									<td class="p-0.5">{item.product_name}</td>
									<td class="w-[70px] p-0.5 text-right">{getNumberFormat(item.amount)}</td>
									{#if productCarryinYN === 'Y'}
										<td class="w-[100px] p-0.5">
											{#if item.process_check}
												{item.process_check.name}
											{/if}
										</td>
										<td class="w-[100px] p-0.5">
											{#if item.process_repair}
												{item.process_repair.name}
											{/if}
										</td>
										<td class="w-[100px] p-0.5">
											{#if item.process_grade}
												{item.process_grade.name}
											{/if}
										</td>
										<td class="w-[50px] p-0.5 text-right">
											{#if item.invoice2 === 0}
												-
											{:else}
												{getNumberFormat(item.invoice2)}
											{/if}
										</td>
										<td class="w-[100px] p-0.5">
											{item.memo ?? ''}
										</td>
										<td class="w-[100px] p-0.5">{getCarryoutProductStatusName(item.status)}</td>
										<td class="w-[90px] max-w-[90px] p-0.5 text-center">
											{#if item.renovate_at}
												{formatDateTimeToString(item.renovate_at)}
											{:else}
												-
											{/if}
										</td>
										<!--<td class="w-[50px] p-0.5">{item.carryout.token.token}</td>-->
										<td class="w-[90px] max-w-[90px] p-0.5 text-center">{formatDateTimeToString(item.carryin_at)}</td>
										<td class="w-[50px] p-0.5">
											{#if item.carryin_user}
												{item.carryin_user.name}
											{/if}
										</td>
									{/if}
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				{#if $carryoutProductStore.pageTotal && $carryoutProductStore.pageTotal > 0}
					<Paginate
						store={carryoutProductStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>