<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import {
		copyToClipboard,
		executeAsk,
		executeMessage,
		formatDateToString,
		getNumberFormat,
		handleCatch
	} from '$lib/Functions';
	import {
		CARRYOUT_STATUS_CARRIED_IN,
		CARRYOUT_STATUS_CARRIED_OUT,
		carryoutStore,
		getCarryoutStatusName,
		loadItems
	} from '$stores/carryoutStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faScrewdriverWrench } from '@fortawesome/free-solid-svg-icons/faScrewdriverWrench';
	import { faCircleQuestion } from '@fortawesome/free-regular-svg-icons/faCircleQuestion';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faTruckRampBox } from '@fortawesome/free-solid-svg-icons/faTruckRampBox';
	import { faPeopleCarryBox } from '@fortawesome/free-solid-svg-icons/faPeopleCarryBox';

	const user: User = getUser();
	const apiUrl = '/wms/carryout';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') ?? '');
	let endAt = $state(page.url.searchParams.get('endAt') ?? '');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = '';
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let modalTypeText = $state('등록');

	let dialogCarryoutId = ''; // 수정시 사용
	let dialogCarryoutMemoId = ''; // 수정시 사용
	let dialogCarryoutDate: string|null = $state(null);
	let dialogCarryoutStatus = $state('10');
	let dialogCarryoutMemo = $state('');
	let dialogToken = $state(''); //
	// 모달창: 미등록상품 등록/수정 창 종료========

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			beginAt: beginAt ?? '',
			endAt: endAt ?? '',
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($carryoutStore) {
			startNo = $carryoutStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(newPage: number) {
		p = newPage.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;

		makeData();
	}

	// 기존 함수: carryin
	async function carryin(e: MouseEvent, id: number) {
		e.preventDefault();

		const ask = await executeAsk('반입처리를 하는 경우 외부수리대상에서 제외됩니다.\n해당 외주반출건에 대해 반입처리하시겠습니까?');
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				carryoutId: id
			};

			const { status, data } = await authClient.put(`${apiUrl}/import`, payload);
			if (status === 200 && data.success) {
				toast.success('반입처리가 완료 되었습니다.');

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function carryout(id: number) {
		const ask = await executeAsk('외주반출처리를 하는 경우 내부점검대상에서 임시(반입완료전까지)제외됩니다.\n외주반출처리하시겠습니까?');
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				carryoutId: id
			};

			const { status, data } = await authClient.put(`${apiUrl}/export`, payload);
			if (status === 200 && data.success) {
				toast.success('반입처리가 완료 되었습니다.');

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function deleteCarryout(e: MouseEvent, id: number) {
		const ask = await executeAsk('해당 처리건을 삭제하시겠습니까?');
		if (!ask) {
			return false;
		}

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/destroy/${id}`);
			if (status === 200 && data.success) {
				toast.success('삭제 되었습니다.');

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	function repairIn() {
		console.log('새 클라이언트를 제작해야 하나...');
		// window.open("/renovate/renovator_login.php", "_blank");
	}

	/**
	 * 외주 반출 리스트 등록시 모달창
	 */
	async function createCarryout() {
		dialogCarryoutId = '';
		dialogCarryoutDate = formatDateToString(new Date());
		dialogCarryoutMemoId = '';
		dialogCarryoutMemo = '';

		modalTypeText = '등록';

		modal.showModal();
	}

	/**
	 * 외주 반출 리스트 수정시 모달창
	 * @param id
	 */
	async function updateCarryout(id: string) {
		const item = $carryoutStore.items.find(item => item.id === id);

		if (item) {
			dialogCarryoutId = id;
			dialogCarryoutDate = item.carryout_at;
			dialogCarryoutMemo = item.memo;
			dialogToken = item.token.token;

			modalTypeText = '수정';

			modal.showModal();
		} else {
			await executeMessage('수정이 불가능합니다.', 'error');
		}
	}

	async function storeCarryout() {
		isLoading = true;

		if (!dialogCarryoutDate) {
			await executeMessage('입고날짜를 입력해 주세요.', 'error');
			return false;
		}

		try {
			const payload = {
				id: dialogCarryoutId,
				'carryout_at': dialogCarryoutDate,
				status: dialogCarryoutStatus,
				memo: dialogCarryoutMemo
			};

			const { status, data } = await authClient.post(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				toast.success(`${modalTypeText} 되었습니다.`);
				modal.close();

				await makeData();

				dialogCarryoutDate = formatDateToString(new Date());
				dialogCarryoutMemo = '';
				dialogToken = '';
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	onMount(async () => {
		carryoutStore.set({});
		await makeData();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '반출/반입', url: '/carryout' },
		{ title: '외주 반출 목록', url: '/carryout' }
	];
</script>

<svelte:head>
	<title>반출/반입 > 외주 반출 목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					<SearchWarehousingDate beginAt={beginAt} endAt={endAt}
																 onUpdate={changeSearchParams}
																 title="외주 반출날짜">
						<div class="relative">
							<label class="sr-only" for="table-search">Search</label>
							<button class="btn btn-ghost"
											id="search_button"
											onclick={makeData}
											type="button"
							>
								<Icon data={faSearch} />
							</button>
						</div>
					</SearchWarehousingDate>
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$carryoutStore.pageTotal ?? 0}>
						{#snippet right()}
										<div class="pl-3" >
								<button class="btn btn-primary btn-sm"
												onclick={createCarryout}
												type="button"
								>
									<Icon class="w-4 h-4 mr-2" data={faPlus} />
									신규 등록
								</button>
							</div>
									{/snippet}
					</TableTop>
					
					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
						<tr class="bg-base-content text-base-300 text-center">
							<th>번호</th>
							<th>외주반출날짜</th>
							<th>총 상품수</th>
							<th>반출</th>
							<th>반입</th>
							<th>수리완료</th>
							<th>수리중</th>
							<th>등록일시</th>
							<th>메모</th>
							<th>상태</th>
							<!--
							<th class="tooltip cursor-help" data-tip="키를 더블클릭하면 복사 됩니다.">
								수리(접속)키
								<Icon data={faCircleQuestion} />
							</th>
							-->
							<th>작업</th>
						</tr>
						</thead>
						
						<tbody>
						{#if $carryoutStore.items}
							{#each $carryoutStore.items as item, index}
								<tr class="hover:bg-base-content/10 text-center">
									<td>{getNumberFormat(startNo - index)}</td>
									<td>
										<p class="cursor-pointer"
											 onclick={() =>  updateCarryout(item.id)}
											 role="presentation">
											{item.carryout_at}
											<Icon data={faPenToSquare} scale={0.7} />
										</p>
									</td>
									<td class="text-right">
										<a href="/carryout/products?id={item.id}">{getNumberFormat(item.count_total)}</a>
									</td>
									<td class="text-right">
										<a href="/carryout/products?id={item.id}&productCarryinYN=N">{getNumberFormat(item.count_carryout)}</a>
									</td>
									<td class="text-right">
										<a href="/carryout/products?id={item.id}&productCarryinYN=Y">{getNumberFormat(item.count_carryin)}</a>
									</td>
									<td class="text-right">
										<a href="/carryout/products?id={item.id}&repairStatus=20">{getNumberFormat(item.count_renovated)}</a>
									</td>
									<td class="text-right">
										<a href="/carryout/products?id={item.id}&repairStatus=10">{getNumberFormat(item.count_remained)}</a>
									</td>
									<td>{item.created_at.substring(0, 10)}</td>
									<td>
										{item.memo ?? ''}
									</td>
									<td>
										{getCarryoutStatusName(item.status)}
									</td>
									<!--
									<td class="cursor-cell" ondblclick={() => copyToClipboard(item.token.token)}>
										{item.token.token}
									</td>
									-->
									<td>
										{#if item.status === CARRYOUT_STATUS_CARRIED_OUT && item.count_renovated > 0}
											<button type="button" class="btn btn-accent btn-xs"
															onclick={async (e) => await carryin(e, item.id)}
											>
												<Icon data={faPeopleCarryBox} />
												전체 반입
											</button>
										{/if}
										
										{#if item.status === CARRYOUT_STATUS_CARRIED_IN}
											<button type="button" class="btn btn-warning btn-xs"
															onclick={async () => await carryout(item.id)}
											>
												<Icon data={faTruckRampBox} />
												전체 반출
											</button>
										{/if}
										
										{#if item.status === CARRYOUT_STATUS_CARRIED_OUT}
											<button type="button" class="btn btn-success btn-xs"
															onclick={async () => await goto(`/carryout/products?id=${item.id}`)}
											>
												<Icon data={faPlus} />
												상품 추가
											</button>
										{/if}
										
										{#if item.count_renovated === 0 && item.count_remained === 0}
											<button type="button" class="btn btn-error btn-xs"
															onclick={async (e) => {await deleteCarryout(e, item.id)}}
											>
												<Icon data={faTrash} />
												삭제
											</button>
										{/if}
									</td>
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				{#if $carryoutStore.pageTotal && $carryoutStore.pageTotal > 0}
					<Paginate
						store={carryoutStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
			
			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>
					
					<h3 class="font-bold text-lg">외주 반출처리 {modalTypeText}</h3>
					
					<div class="mt-4 grid grid-cols-2 gap-4">
						<div>
							<label class="label text-sm" for="carryout_date">반출 날짜</label>
							<input bind:value={dialogCarryoutDate}
										 class="input input-bordered w-full"
										 id="carryout_date"
										 type="date">
						</div>
						<div>
							<label class="label text-sm" for="carryout_status">처리 상태</label>
							<select bind:value={dialogCarryoutStatus}
											class="select select-bordered w-full"
											disabled={modalTypeText==='등록'}
											id="carryout_status"
							>
								<option value="10">반출</option>
								<option value="30">반입</option>
								<option value="90">취소</option>
							</select>
						</div>
					</div>
					
					<div class="mt-2">
						<label class="label text-sm" for="unregistered_barcode">수리(접속)키</label>
						<input bind:value={dialogToken}
									 class="input input-bordered w-full cursor-not-allowed"
									 maxlength="6" minlength="6" placeholder="자동생성(A000000)" readonly type="text">
					</div>
					
					<div class="mt-2">
						<label class="label text-sm" for="unregistered_memo">메모</label>
						<textarea bind:value={dialogCarryoutMemo}
											class="textarea textarea-bordered w-full"
											id="carryout_memo"></textarea>
					</div>
					
					<div class="modal-action flex justify-between">
						<div>
							<button class="btn btn-primary"
											onclick={() => storeCarryout()}
							>
								<Icon class="w-5 h-5" data={faSave} />
								{modalTypeText}
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button class="btn btn-warning tooltip tooltip-left"
												data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>