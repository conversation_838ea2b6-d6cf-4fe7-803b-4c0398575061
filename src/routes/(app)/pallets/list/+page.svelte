<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types/types';
	import { toast } from 'svoast';
	import { openWebviewWindow } from '$lib/services/windowService';
	import { buildPalletPrintParams } from '$lib/utils/printUtils';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToFullString,
		formatDateTimeToString,
		formatDateToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import {
		getPalletStatusName,
		loadPallets,
		PALLET_STATUS_CLOSED,
		PALLET_STATUS_EXPORTED,
		palletStore
	} from '$stores/palletStore';
	import { getProcessGradeColorButton } from '$stores/processStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchPalletStatus from '$components/Snippets/SearchPalletStatus.svelte';
	import SearchPalletCode from '$components/Snippets/SearchPalletCode.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import SearchExportedDate from '$components/Snippets/SearchExportedDate.svelte';
	import ButtonSave from '$components/Button/Save.svelte';
	import ButtonSaveAll from '$components/Button/SaveAll.svelte';
	import ButtonExport from '$components/Button/Export.svelte';
	import ButtonPrint from '$components/Button/Print.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-solid-svg-icons/faSave';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faFileExcel } from '@fortawesome/free-regular-svg-icons/faFileExcel';
	import { faTruckArrowRight } from '@fortawesome/free-solid-svg-icons/faTruckArrowRight';
	import { faScrewdriverWrench } from '@fortawesome/free-solid-svg-icons/faScrewdriverWrench';
	import { faBackspace } from '@fortawesome/free-solid-svg-icons/faBackspace';
	import { authClient } from '$lib/services/AxiosBackend';

	let user: User = getUser();
	let apiUrl = '/wms/pallets/list';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') ?? '');
	let endAt = $state(page.url.searchParams.get('endAt') ?? '');
	let palletStatus = $state(page.url.searchParams.get('palletStatus') ?? '');
	let keyword = $state(''); // 검색어
	let tmpExportDate = $state(formatDateToString(new Date()));
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	
	// 출고일 상태 관리
	let exportedDateInput = $state(page.url.searchParams.get('exportedDate') ?? '');
	
	// 초기 상태 검증: 출고완료 상태가 아닌데 출고일이 설정된 경우 초기화
	if (palletStatus !== PALLET_STATUS_EXPORTED.toString() && exportedDateInput) {
		exportedDateInput = '';
	}
	
	let exportedDate = $derived(
		palletStatus === PALLET_STATUS_EXPORTED.toString() 
			? exportedDateInput
			: ''
	);
	// 검색관련 끝==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let checkboxes: [] = $state([]); // 선택된 항목 찾기
	let palletIds: number[] = $state([]); // 전체 엑셀 다운로드에 사용될 변수
	// 각 팔레트별 출고일 입력값을 보관하는 상태 맵
	let exportDateById: Record<number, string> = $state({});
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			beginAt: beginAt,
			endAt: endAt,
			exported_at: exportedDate,
			status: palletStatus,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadPallets(`${apiUrl}?${apiSearchParams}`, user);

		if ($palletStore) {
			startNo = $palletStore.pageStartNo ?? 0;
		}

		// 리스트 데이터 로드 후, 각 행의 초기 출고일 값을 상태로 동기화
		// (사용자가 수정한 값은 서버 반영 후 다시 최신값으로 갱신됨)
		const itemsList = ((($palletStore as any) ?? {}).items ?? []) as any[];
		exportDateById = itemsList.reduce<Record<number, string>>((acc: Record<number, string>, item: any) => {
			acc[item.id] = formatDateTimeToString(item.exported_at) ?? '';
			return acc;
		}, {});

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(newPage: number) {
		p = newPage.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;
		if (value.detail.exportedDate !== undefined) exportedDateInput = value.detail.exportedDate;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		
		// 팔레트 상태가 변경되는 경우
		if (value.detail.palletStatusGroup !== undefined) {
			const newStatus = value.detail.palletStatusGroup;
			palletStatus = newStatus;
			
			// 출고완료 상태가 아닌 경우 출고일 검색 조건 초기화
			if (newStatus !== PALLET_STATUS_EXPORTED.toString()) {
				exportedDateInput = '';
			}
		}
		
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;

		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	// 출고 취소: 상태(ids) 기반으로 리팩터링
	async function cancelOut(idsToRollback: number[], firstCode?: string) {
		if (!idsToRollback || idsToRollback.length < 1) {
			alert('선택된 팔레트가 없습니다.');
			return false;
		}

		const msgLabel = firstCode
			? idsToRollback.length > 1
				? `${firstCode} 외 ${idsToRollback.length - 1}개`
				: `${firstCode}`
			: `${idsToRollback.length}개`;
		const message = `팔레트[ ${msgLabel} ]의 출고를 취소`;
		const ask = await executeAsk(`${message} 하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = { palletIds: idsToRollback };
			const { status, data } = await authClient.put(`/wms/pallets/rollback-exports`, payload);
			if (status === 200 && data.success) {
				toast.success(`${message}`);
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}
	
	// - targetIds: 출고 대상 팔레트 ID 목록 (필수)
	// - firstCode: 단일/첫 번째 팔레트 코드(메시지 표기를 위해 선택적으로 전달)
	async function checkout(targetIds: number[], firstCode?: string) {
		// 유효성 검사: 대상 ID 필수
		if (!targetIds || targetIds.length < 1) {
			alert('선택된 팔레트가 없습니다.');
			return false;
		}

		// 메시지 표기를 위한 코드 결정 (전달되지 않은 경우 스토어에서 조회)
		let codeForMessage = firstCode;
		if (!codeForMessage) {
			const items = (($palletStore?.items ?? []) as Array<{ id: number; pallet_info: { code: string } }>);
			const firstId = targetIds[0];
			const found = items.find((it) => it.id === firstId);
			codeForMessage = found?.pallet_info?.code ?? `${firstId}`;
		}

		const msgLabel = targetIds.length > 1 ? `${codeForMessage} 외 ${targetIds.length - 1}개` : `${codeForMessage}`;
		const message = `팔레트[ ${msgLabel} ]를 출고`;
		const ask = await executeAsk(`${message} 하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = { palletIds: targetIds };
			const { status, data } = await authClient.post(`/wms/pallets/exports`, payload);
			if (status === 200 && data.success) {
				toast.success(`${message}`);
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	// 선택한 팔레트 코드 인쇄 (Svelte 스타일: 상태 기반 + 서비스 사용)
	// - DOM 탐색/속성 의존 제거, ids + $palletStore.items 활용
	async function printCheckedPalletCode() {
		// 방어 로직: 선택 항목 확인
		if (!ids || ids.length < 1) {
			alert('선택된 팔레트가 없습니다.');
			return false;
		}

		const items = ($palletStore?.items ?? []) as Array<{ id: number; exported_at?: string | null; pallet_info: { code: string } }>;
		const { exportDateParam, palletCodeList } = buildPalletPrintParams(ids as number[], items);

		if (!palletCodeList) {
			alert('인쇄할 팔레트 코드가 없습니다.');
			return false;
		}

		const search = new URLSearchParams();
		if (exportDateParam) search.set('export_date', exportDateParam);
		search.set('pallet_code_list', palletCodeList);

		await openWebviewWindow(`/print/pallets?${search.toString()}`);
	}

	function printPalletCode(grade: string, level: string, column: string, exported_at: string) {
		// 단일 팔레트 라벨 인쇄: 서비스로 캡슐화된 창 열기 사용
		const exportedAt = formatDateTimeToString(exported_at) ?? tmpExportDate;
		const params = new URLSearchParams({ level, column, grade_name: grade, export_date: exportedAt });
		openWebviewWindow(`/print/label?${params.toString()}`, 'Print', { width: 1280, height: 800 });
	}

	// 출고일 저장
	async function saveExportDate(idsToSave: number[], firstCode?: string) {
		// 유효성 검사: 선택 항목
		if (!idsToSave || idsToSave.length < 1) {
			alert('선택된 팔레트가 없습니다.');
			return false;
		}

		// 유효성 검사: 출고일 입력 여부
		const missingDateIds = idsToSave.filter((id) => !exportDateById[id]);
		if (missingDateIds.length > 0) {
			alert('출고일을 입력해주세요.');
			return false;
		}

		// 메시지 구성: 단일 저장 시 코드 표시, 일괄 저장 시 개수만 표시
		const msgLabel = firstCode
			? idsToSave.length > 1
				? `${firstCode} 외 ${idsToSave.length - 1}개`
				: `${firstCode}`
			: `${idsToSave.length}개`;
		const message = `팔레트[ ${msgLabel} ]의 출고날짜를 저장`;
		const ask = await executeAsk(`${message} 하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			// API 페이로드: id -> 출고일 매핑
			const payload = idsToSave.map((id) => ({
				palletId: id,
				exportDate: exportDateById[id]
			}));

			const { status, data } = await authClient.put(`/wms/pallets/save-export-date`, payload);
			if (status === 200 && data.success) {
				toast.success(`${message}`);
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		palletStore.set({});

		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '출고', url: '/pallets/list' },
		{ title: '출고 팔레트 목록', url: '/pallets/list' }
	];
</script>

<svelte:head>
	<title>출고 > 출고 팔레트 목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					{#if palletStatus === PALLET_STATUS_EXPORTED.toString()}
						<SearchWarehousingDate
							{beginAt}
							{endAt}
							onUpdate={changeSearchParams}
							title="점검날짜"
						/>
						<SearchExportedDate
							exportedDate={exportedDateInput}
							onUpdate={changeSearchParams}
							title="출고일"
						/>
					{/if}
					<SearchPalletStatus onUpdate={changeSearchParams} palletStatusGroup={palletStatus} />
					<SearchPalletCode {keyword} onUpdate={changeSearchParams}>
						<ButtonSaveAll
							onclick={async (e) => {
								e.preventDefault();
								isLoading = true;

								const payload = getPayload(apiSearchParams);

								palletIds = [];
								$palletStore.items.forEach((item) => {
									palletIds.push(item.id);
								});
								payload.palletIds = palletIds;

								if (beginAt && endAt) {
									payload.beginAt = beginAt;
									payload.endAt = endAt;
								}

								await handleDownload(EXCEL_DOWNLOAD_URL, payload);
								isLoading = false;
							}}
							tooltipData="전체 저장"
							useTooltip={true}
						/>

						<ButtonSave
							onclick={async (e) => {
								e.preventDefault();
								isLoading = true;

								if (ids.length < 1) {
									await executeMessage(
										'선택된 팔레트가 없습니다.\n다운로드할 팔레트를 선택해 주세요.'
									);
									isLoading = false;
									return false;
								}

								const payload = getPayload(apiSearchParams);
								payload.exportType = 'pallets',
								payload.palletIds = ids;

								await handleDownload(EXCEL_DOWNLOAD_URL, payload);
								isLoading = false;
							}}
							tooltipData="선택 저장"
							useTooltip={true}
						/>

						{#if palletStatus === PALLET_STATUS_CLOSED.toString()}
							<ButtonExport onclick={() => checkout(ids as unknown as number[])} tooltipData="선택 출고" useTooltip={true} />
						{/if}
						{#if palletStatus === PALLET_STATUS_EXPORTED.toString()}
							<button class="btn btn-info btn-sm ml-4 tooltip tooltip-bottom"
											data-tip="선택된 팔레트의 월간 마감 양식을 출력합니다."
											onclick={async (e) => {
								e.preventDefault();
								isLoading = true;

								if (ids.length < 1) {
									await executeMessage(
										'선택된 팔레트가 없습니다.\n다운로드할 팔레트를 선택해 주세요.'
									);
									isLoading = false;
									return false;
								}

								const payload = getPayload(apiSearchParams);
								payload.exportType = 'monthlyClosing';
								payload.palletIds = ids;

								await handleDownload(EXCEL_DOWNLOAD_URL, payload);
								isLoading = false;
							}}
											type="button"
							>
								<Icon data={faFileExcel} /> 월간 마감
							</button>
							
							<ButtonPrint onclick={printCheckedPalletCode} useTooltip={true} />
						{/if}
					</SearchPalletCode>
					
					<SearchField>
						<SearchFieldTitle title="임시 출고일" />
						<SearchFieldContent>
							<input
								bind:value={tmpExportDate}
								class="input input-sm input-bordered w-36 focus:ring-0 focus:outline-none"
								id="tmp_export_date"
								name="tmp_export_date"
								type="date"
							/> <span class="text-xs">* 임시 출고일은 <u>출력시</u>에만 사용됩니다.</span>
						</SearchFieldContent>
					</SearchField>
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$palletStore.pageTotal ?? 0} />

					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th>
									<input
										checked={allChecked}
										onchange={() => toggleAllCheck($palletStore.items)}
										type="checkbox"
									/>
								</th>
								<th>번호</th>
								<th>팔레트 번호</th>
								<th>상품 총 금액</th>
								<th>타입</th>
								<th>적재수량</th>
								<th>상태</th>
								{#if palletStatus !== PALLET_STATUS_EXPORTED.toString()}
									<th>추가점검</th>
								{/if}
								<th>엑셀저장</th>
								<th>출고</th>
								{#if palletStatus === PALLET_STATUS_EXPORTED.toString()}
									<th>출고상태변환일</th>
									<th>출고일</th>
									<th>
										<button class="btn btn-primary btn-xs" onclick={() => saveExportDate(ids)}>
											<Icon data={faSave} />
											선택일괄저장
										</button>
									</th>
								{/if}
							</tr>
						</thead>

						<tbody>
							{#if $palletStore.items}
								{#each $palletStore.items as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="text-center">
											<input type="hidden" name="pallet_id" value={item.id} />
											<input
												bind:checked={idChecked[index]}
												bind:group={ids}
												bind:this={checkboxes[index]}
												value={item.id}
												data-pallet-id={item.id}
												data-pallet-code={item.pallet_info.code}
												data-export-date={item.exported_at}
												type="checkbox"
											/>
										</td>
										<td class="p-1 text-left">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="p-2 min-w-40 text-center">
											<button
												class="btn btn-ghost btn-xs"
												onclick={() => {
													let grade_name = item.repair_grade_id !== null ? item.repair_grade.name : item.grade_name;
													printPalletCode(
														grade_name,
														item.pallet_info.level,
														item.pallet_info.column,
														item.exported_at ?? tmpExportDate
													);
												}}
											>
												{item.pallet_info.code}
												<Icon data={faPrint} />
											</button>
										</td>
										<td class="p-1 text-right">
											<a href="/pallets/products?id={item.id}&palletStatus={palletStatus}">
												{getNumberFormat(item.product_amount)}
											</a>
										</td>
										<td class="p-1 text-center">
											{@html getProcessGradeColorButton(item)}
										</td>
										<td class="p-1 text-right">
											<a href="/pallets/products?id={item.id}&palletStatus={palletStatus}">
												{getNumberFormat(item.product_count)}
											</a>
										</td>
										<td class="p-1 text-center">
											{getPalletStatusName(item.status)}
										</td>
										{#if item.status !== PALLET_STATUS_EXPORTED}
											<td class="p-1 min-w-32 text-center">
												{#if item.status !== PALLET_STATUS_EXPORTED && item.status !== PALLET_STATUS_CLOSED}
													<button
														class="btn btn-info btn-xs"
														data-location-id={item.location_id}
														data-pallet-code={item.pallet_info.code}
														onclick={() =>
															goto(`/pallets/create/?pallet_number=${item.pallet_info.code}`)}
														type="button"
													>
														<Icon data={faScrewdriverWrench} />
														추가 점검
													</button>
												{/if}
											</td>
										{/if}
										<td class="p-1 min-w-32 text-center">
											<button
												class="btn btn-success btn-xs"
												data-location-id={item.location_id}
												data-id={item.id}
												onclick={async (e) => {
													e.preventDefault();
													isLoading = true;

													const payload = getPayload(apiSearchParams);
													payload.palletCode = item.pallet_info.code;
													payload.palletIds = [item.id];

													await handleDownload(EXCEL_DOWNLOAD_URL, payload);
													isLoading = false;
												}}
												type="button"
											>
												<Icon data={faFileExcel} />
												엑셀저장
											</button>
										</td>
										<td class="p-1 min-w-32 text-center">
											{#if item.status === PALLET_STATUS_CLOSED}
                       	<button
													class="btn btn-warning btn-xs"
													data-pallet-code={item.pallet_info.code}
													data-id={item.id}
                          onclick={() => checkout([item.id], item.pallet_info.code)}
													type="button"
												>
													<Icon data={faTruckArrowRight} />
													출고처리
												</button>
											{/if}

											{#if item.status === PALLET_STATUS_EXPORTED}
												<button
													class="btn btn-error btn-xs"
													onclick={() => cancelOut([item.id], item.pallet_info.code)}
													type="button"
												>
													<Icon data={faBackspace} />
													출고취소
												</button>
											{/if}
										</td>
										{#if item.status === PALLET_STATUS_EXPORTED}
											<td class="text-center">
												{formatDateTimeToFullString(item.checked_at)}
											</td>
											<td class="text-center">
												<input
													type="date"
													name="export_date"
													maxlength="10"
													placeholder="YYYY-MM-DD"
													bind:value={exportDateById[item.id]}
													style="padding: 0 10px; text-align: center; display: inline-block; width: 120px;"
												/>
														</td>
														<td class="min-w-40 text-center">
												<button
													type="button"
													class="btn btn-neutral-content btn-xs"
													data-pallet-code={item.pallet_info.code}
													data-export-date={item.exported_at}
													data-id={item.id}
													onclick={() => saveExportDate([item.id], item.pallet_info.code)}
												>
													<Icon data={faSave} />
													출고날짜 저장
												</button>
											</td>
										{/if}
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				{#if $palletStore.pageTotal && $palletStore.pageTotal > 0}
					<Paginate
						store={palletStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>
