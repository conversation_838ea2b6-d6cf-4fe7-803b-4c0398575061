<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';
	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import { getFromLocalStorage, saveToLocalStorage } from '$lib/utils/StorageManager';
	import {
		executeAsk,
		executeMessage,
		formatDateToString,
		getBeginAt,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import { loadProduct, productStore } from '$stores/productStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import TaskTable from '$components/UI/TaskTable.svelte';
	import Paginate from '$lib/components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import SearchProductCheckedStatus from '$components/Snippets/SearchProductCheckedStatus.svelte';
	import SearchProcessGrade from '$components/Snippets/SearchProcessGrade.svelte';
	import SearchProductStatus from '$components/Snippets/SearchProductStatus.svelte';
	import SearchPalletStatus from '$components/Snippets/SearchPalletStatus.svelte';
	import SearchCategory from '$components/Snippets/SearchCategory.svelte';
	import SearchRg from '$components/Snippets/SearchRg.svelte';
	import SearchAg from '$components/Snippets/SearchAg.svelte';
	import ButtonReset from '$components/Button/Reset.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';
	import ButtonSelectedDelete from '$components/Button/SelectedDelete.svelte';
	import SearchButton from '$components/Button/Search.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	import Icon from 'svelte-awesome';
	import { faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';

	let user: User = getUser();
	const apiUrl = '/wms/products/tasks';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부
	let focusSearchInput: HTMLInputElement;
	let isSearchExpanded = $state(getFromLocalStorage('isSearchExpanded', 'false') === 'true'); // 검색 확장 상태

	// 검색 query string 시작 ==========
	let reqIdSelected = $state(page.url.searchParams.get('reqId') ?? ''); // 완료되지 않은 요청서 목록(선택 값)
	let reqType = $state(page.url.searchParams.get('reqType') ?? '');
	let beginAt = $state(page.url.searchParams.get('beginAt') || getBeginAt());
	let endAt = $state(page.url.searchParams.get('endAt') || formatDateToString(new Date()));
	let checkedStatus = $state(page.url.searchParams.get('checkedStatus') ?? '');
	let productStatus = $state(page.url.searchParams.get('productStatus') ?? '');
	let processCd = $state('');
	let palletStatus = $state(page.url.searchParams.get('palletStatus') ?? '');
	let cate4 = $state('');
	let cate5 = $state('');
	let searchType = $state('qaid');
	let keyword = $state(''); // 검색어
	let display_keyword = $state('');
	let isRg = $state('N'); // RG 상품 보여주기
	let isAg = $state('N'); // Aging 상품 보여주기
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	// 검색 query string 종료 ==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData(search = false) {
		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			exportType: 'products',
			reqId: reqIdSelected,
			beginAt: beginAt,
			endAt: endAt,
			checkedStatus: checkedStatus,
			productStatus: productStatus,
			processCd: processCd,
			palletStatus: palletStatus,
			cate4: cate4,
			cate5: cate5,
			searchType: searchType,
			keyword: display_keyword,
			isRg: isRg,
			isAg: isAg,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const apiParams = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = apiParams.toString(); // api

		await loadProduct(`${apiUrl}?${apiSearchParams}`, user);

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.reqId) reqIdSelected = value.detail.reqId;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;
		if (value.detail.checkedStatusGroup || value.detail.checkedStatusGroup === '')
			checkedStatus = value.detail.checkedStatusGroup;
		if (value.detail.productStatusGroup || value.detail.productStatusGroup === '')
			productStatus = value.detail.productStatusGroup;
		if (value.detail.processGradeGroup || value.detail.processGradeGroup === '')
			processCd = value.detail.processGradeGroup;
		if (value.detail.palletStatusGroup || value.detail.palletStatusGroup === '')
			palletStatus = value.detail.palletStatusGroup;
		if (value.detail.cate4 || value.detail.cate4 === '') cate4 = value.detail.cate4;
		if (value.detail.cate5 || value.detail.cate5 === '') cate5 = value.detail.cate5;
		if (value.detail.isRg) isRg = value.detail.isRg;
		if (value.detail.isAg) isAg = value.detail.isAg;
		if (value.detail.searchType) searchType = value.detail.searchType;
		// if (value.detail.keyword || value.detail.keyword === '') display_keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	// 검색 실행 함수
	async function performSearch() {
		display_keyword = keyword.trim();
		await makeData(true);

		keyword = '';
		focusSearchInput.value = '';

		await tick();
		focusSearchInput.focus();
	}

	/**
	 * 선택 삭제
	 */
	async function handleDeleteProduct(event: Event) {
		event.preventDefault();

		const deleteCount = ids.length;
		if (deleteCount < 1) {
			await executeMessage('삭제할 상품을 선택해 주세요.');
			return false;
		}

		const result = await executeAsk(
			'점검 대상에서 상품을 삭제합니댜.\n\n선택한 상품들의 등록을 취소(삭제) 하시겠습니까?',
			'warning'
		);
		if (!result) {
			return false;
		}

		try {
			const payload = {
				reqId: reqIdSelected,
				ids: ids
			};

			const { status, data } = await authClient.post(`/wms/products/destroy`, payload);
			if (status === 200 && data.success) {
				toast.success(`선택된 상품들을 삭제했습니다.`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	/**
	 * 엑셀 다운로드 핸들러
	 */
	async function handleExcelDownload(e: Event) {
		e.preventDefault();
		isLoading = true;

		const payload = getPayload(apiSearchParams);
		if (reqType === '2') {
			payload.type = 'apple'; // 애플 제품 다운로드
		} else {
			payload.type = 'tasks'; // 일반 제품 다운로드
		}

		await handleDownload(EXCEL_DOWNLOAD_URL, payload);
		isLoading = false;
	}

	// onMount는 한 번 실행되고 나면 페이지가 변경되지 않는 한 다시 실행되지 않는다.
	onMount(async () => {
		productStore.set({});

		await makeData();

		focusSearchInput.focus();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works' },
		{ title: '작업목록', url: '/works/tasks' }
	];
</script>

<svelte:head>
	<title>입고 > 작업목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<!-- 확장 검색 영역 (토글 가능) -->
					{#if isSearchExpanded}
						<div class="space-y-1">
							<SearchProductCheckedStatus
								checkedStatusGroup={checkedStatus}
								onUpdate={changeSearchParams}
							></SearchProductCheckedStatus>
							<SearchProductStatus
								onUpdate={changeSearchParams}
								productStatusGroup={productStatus}
							/>
							<SearchProcessGrade onUpdate={changeSearchParams} processGradeGroup={processCd} />
							<SearchPalletStatus onUpdate={changeSearchParams} palletStatusGroup={palletStatus} />

							{#if $productStore.cate4}
								<SearchCategory
									category={$productStore.cate4}
									cate4Selected={cate4}
									cate5Selected={cate5}
									onUpdate={changeSearchParams}
								/>
							{/if}
						</div>
					{/if}

					<!-- 기본 표시 영역: 입고날짜/검색 항목 -->
					{#if reqIdSelected}
						<SearchField>
							<SearchFieldTitle title="입고 날짜" />
							<SearchFieldContent>
								<span class="font-bold">{$productStore.requestAt ?? ''}</span>

								<SearchRg {isRg} onUpdate={changeSearchParams} />
								<SearchAg {isAg} onUpdate={changeSearchParams} />
							</SearchFieldContent>
						</SearchField>
					{:else}
						<SearchWarehousingDate {beginAt} {endAt} onUpdate={changeSearchParams}>
							<SearchRg {isRg} onUpdate={changeSearchParams} />
							<SearchAg {isAg} onUpdate={changeSearchParams} />
						</SearchWarehousingDate>
					{/if}

					<!-- 검색 항목과 초기화 버튼 -->
					<SearchField>
						<SearchFieldTitle title="검색 항목" />
						<SearchFieldContent>
							<input
								bind:value={keyword}
								bind:this={focusSearchInput}
								class="input input-sm input-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary w-60 text-sm"
								placeholder="QAID/바코드/로트번호 검색"
								onkeydown={(e) => {
									if (e.key === 'Enter') {
										performSearch();
									}
								}}
								type="text"
							/>

							<SearchButton
								onclick={() => {
									performSearch();
								}}
								useTooltip={true}
								margin="ml-2"
							/>

							<ButtonReset localUrl={`${localUrl}`} useTooltip={true} />
							<ButtonExcelDownload onclick={handleExcelDownload} useTooltip={true} />
						</SearchFieldContent>
					</SearchField>
				</SearchUI>

				<!-- 검색어 표시 (항상 표시) -->
				<DisplayKeyword display_keyword1={display_keyword} />

				<!-- 검색 확장 버튼 - 검색어 표시 여부에 따라 위치 조정 -->
				<div class="flex justify-center {display_keyword ? 'mt-0' : '-mt-2'}">
					<button
						class="px-4 py-1 text-sm text-base-content/70 hover:text-primary hover:bg-base-200/50 rounded-b-lg border border-t-0 border-base-300 hover:border-primary transition-all duration-200"
						onclick={() => {
							isSearchExpanded = !isSearchExpanded;
							saveToLocalStorage('isSearchExpanded', isSearchExpanded.toString());
						}}
					>
						<span class="flex items-center gap-1">
							{#if isSearchExpanded}
								<Icon data={faChevronDown} scale={0.7} />
								<span>검색 축소</span>
							{:else}
								<Icon data={faChevronRight} scale={0.7} />
								<span>검색 확장</span>
							{/if}
						</span>
					</button>
				</div>

				<!-- 검색 영역과 테이블 사이 간격 -->
				<div class="h-2"></div>

				<!-- 리스트 시작 -->
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$productStore.pageTotal ?? 0}>
						{#snippet left()}
							<div class="pl-3">
								<ButtonSelectedDelete
									onclick={async (event: Event) => await handleDeleteProduct(event)}
									useTooltip={true}
								/>
							</div>
						{/snippet}
					</TableTop>

					<TaskTable
						bind:ids
						startNo={$productStore.pageStartNo ?? 0}
						products={$productStore.products ?? []}
					/>
				</div>

				<!-- Pagination -->
				{#if $productStore.pageTotal && $productStore.pageTotal > 0}
					<Paginate
						store={productStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>
