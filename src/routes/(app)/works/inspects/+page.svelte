<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload,
		isOverDueDate
	} from '$lib/Functions';
	import { loadProduct, productStore } from '$stores/productStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchCategory from '$components/Snippets/SearchCategory.svelte';
	import SearchRg from '$components/Snippets/SearchRg.svelte';
	import SearchAg from '$components/Snippets/SearchAg.svelte';
	import SearchQaid from '$components/Snippets/SearchQaid.svelte';
	import ButtonSelectedDelete from '$components/Button/SelectedDelete.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';
	import MessageModal from '$components/UI/MessageModal.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	import Icon from 'svelte-awesome';
	import { faCheckDouble } from '@fortawesome/free-solid-svg-icons/faCheckDouble';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	let user: User = getUser();
	const apiUrl = '/wms/inspections';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let reqIdSelected = $state(page.url.searchParams.get('reqId') ?? ''); // 완료되지 않은 요청서 목록(선택 값)
	let cate4 = $state('');
	let cate5 = $state('');
	let searchType = $state('qaid');
	let keyword = $state(''); // 검색어
	let display_keyword = $state('');
	let isRg = $state('N'); // RG 상품 보여주기
	let isAg = $state('N'); // Aging 상품 보여주기
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let focusCheckQaidInput: HTMLInputElement; // 페이지 로딩시 입고검수 input에 커서 포커싱
	let checkQaid = $state(''); // 검수할 QAID
	let checkQaidIndex = $state(''); // 검수할 QAID의 상품 index

	let showProductName = $state('입고검수를 하면 상품 이름이 나옵니다.');

	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			exportType: 'products',
			reqId: reqIdSelected,
			cate4: cate4,
			cate5: cate5,
			searchType: searchType,
			keyword: display_keyword,
			isRg: isRg,
			isAg: isAg,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadProduct(`${apiUrl}?${apiSearchParams}`, user);

		if ($productStore) {
			startNo = $productStore.pageStartNo ?? 0;
		}

		await activeFocus();

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.reqId) reqIdSelected = value.detail.reqId;
		if (value.detail.cate4 || value.detail.cate4 === '') cate4 = value.detail.cate4;
		if (value.detail.cate5 || value.detail.cate5 === '') cate5 = value.detail.cate5;
		if (value.detail.isRg || value.detail.isRg === '') isRg = value.detail.isRg;
		if (value.detail.isAg || value.detail.isAg === '') isAg = value.detail.isAg;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') display_keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	async function activeFocus() {
		checkQaid = '';

		if (focusCheckQaidInput) {
			focusCheckQaidInput.value = '';

			// autofocus 속성 토글
			focusCheckQaidInput.removeAttribute('autofocus');
			await tick();
			focusCheckQaidInput.setAttribute('autofocus', '');
			focusCheckQaidInput.focus();
		}
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	/**
	 * 검수대기 요청서 리스트를 받아온다.(가장 오래된 것 부터)
	 */
	async function getUncheckedList() {
		try {
			const { status, data } = await authClient.get(`/wms/reqs/unchecked`);

			if (status === 200 && data.success) {
				if (data.data.items[0].id) {
					reqIdSelected = String(data.data.items[0].id);
				}
			}
		} catch (e) {
			console.error(e);
		}
	}

	/**
	 * (개별)검수완료 처리<br>
	 * 기존 함수명: checkQaid 현재 변수명과 충돌로 인해 변경
	 */
	async function handleCheckQaid(event: Event) {
		event.preventDefault();

		if (!checkQaid) {
			showModal('입고 검수할 QAID를 입력해 주세요.', 'warning', 'QAID 입력 오류');
			return false;
		}

		try {
			const payload = {
				_method: 'PATCH',
				reqId: reqIdSelected,
				qaid: checkQaid
			};

			const { status, data } = await authClient.post(`${apiUrl}/pass`, payload);

			if (status === 200 && data.success) {
				toast.success(`${checkQaid} 검수완료`);
				showProductName = data.data.product_name;

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
				await activeFocus();
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '입고 검수 오류');
		}
	}

	/**
	 * 선택 삭제
	 */
	async function handleDeleteProduct(event: Event) {
		event.preventDefault();

		const deleteCount = ids.length;
		if (deleteCount < 1) {
			await executeMessage('삭제할 상품을 선택해 주세요.');
			return false;
		}

		const result = await executeAsk(
			'점검 대상에서 상품을 삭제합니댜.\n\n선택한 상품들의 등록을 취소(삭제) 하시겠습니까?',
			'warning'
		);
		if (!result) {
			return false;
		}

		try {
			const payload = {
				reqId: reqIdSelected,
				ids: ids
			};

			const { status, data } = await authClient.post(`/wms/products/destroy`, payload);
			if (status === 200 && data.success) {
				toast.success(`선택된 상품들을 삭제했습니다.`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e) {
			console.error(e);
		}
	}

	/**
	 * 검수 통과(미입고 처리)
	 *
	 * @param id
	 * @param unchecked 검수 대기 상품의 개수
	 */
	async function completeInspection(id: string, unchecked = 0) {
		if (!id) {
			await executeMessage(
				'검수 통과(미입고 처리)에 오류가 있습니다.\n프로그래머에게 문의해 주세요.',
				'error'
			);
			return false;
		}

		let message = unchecked > 20 ? `상품이 20개 이상이면 시간이 많이 걸릴 수 있습니다.\n\n` : '';

		message +=
			'해당 요청건의 등록된 상품을 모두 [검수 통과(미입고 처리)]처리 하시겠습니까?\n\n입고 검수(대기)에 남아 있는 상품은 미입고로 처리 됩니다.';

		const ask = await executeAsk(message);
		if (!ask) {
			return false;
		}

		isLoading = true;
		try {
			const { status, data } = await authClient.put(`/wms/inspections/complete/${id}`);

			if (status === 200 && data.success) {
				await executeMessage('검수 통과(미입고 처리) 작업이 완료 되었습니다.');
				await getUncheckedList();
				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		} finally {
			isLoading = false;
		}
	}

	// 모달창 관련 변수
	let modal: MessageModal;
	let modalType = $state<'error' | 'warning' | 'success'>('error');
	let modalTitle = $state('');
	let modalMessage = $state('');

	// 모달창 표시 함수
	function showModal(
		message: string,
		type: 'error' | 'warning' | 'success' = 'error',
		title: string = '알림'
	) {
		modalType = type;
		modalTitle = title;
		modalMessage = message;

		if (modal) {
			modal.show();
		} else {
			console.error('modal이 undefined입니다. 컴포넌트가 아직 마운트되지 않았을 수 있습니다.');
			// 대안으로 alert 사용 (임시)
			executeMessage(`${title}: ${message}`);
		}
	}

	// 모달 닫힘 핸들러
	function handleModalClose() {
		setTimeout(() => {
			if (focusCheckQaidInput) {
				checkQaid = '';
				focusCheckQaidInput.focus();
				console.log('모달 닫힌 후 포커스 재설정');
			}
		}, 100);
	}

	onMount(async () => {
		productStore.set({});
		if (!reqIdSelected) {
			await getUncheckedList();
		}
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works' },
		{ title: '검수하기(검수대기)', url: '/works/inspects' }
	];
</script>

<svelte:head>
	<title>입고 > 검수하기(검수대기)</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<SearchField>
						<SearchFieldTitle title="입고 날짜" />
						<SearchFieldContent>
							{#if $productStore.inspects}
								<select
									bind:value={reqIdSelected}
									class="select select-bordered select-sm"
									onchange={async () => {
										display_keyword = '';
										await makeData();
									}}
								>
									{#each $productStore.inspects as item (item.id)}
										<option value={String(item.id)}>{item.req_at}</option>
									{/each}
								</select>
							{:else}
								모든 요청서가 검수완료 되었습니다.
							{/if}

							<SearchRg {isRg} onUpdate={changeSearchParams} />
							<SearchAg {isAg} onUpdate={changeSearchParams} />
						</SearchFieldContent>
					</SearchField>

					{#if $productStore.cate4}
						<SearchCategory
							category={$productStore.cate4}
							cate4Selected={cate4}
							cate5Selected={cate5}
							onUpdate={changeSearchParams}
						/>
					{/if}

					<SearchQaid {keyword} onUpdate={changeSearchParams} {searchType}>
						<ButtonExcelDownload
							onclick={async (e) => {
								e.preventDefault();
								isLoading = true;

								const payload = getPayload(apiSearchParams);
								payload.type = 'inspects';

								await handleDownload(EXCEL_DOWNLOAD_URL, payload);
								isLoading = false;
							}}
							useTooltip={true}
						/>

						<button
							class="btn btn-error btn-sm ml-2 tooltip tooltip-top"
							data-tip="해당 요청건의 상품을 모두 [검수 통과(미입고 처리)]처리 합니다."
							onclick={async () => {
								// $productStore.inspects 배열에서 id = reqIdSelected와 일치하는 항목 찾기
								let uncheckedCount = 0;
								if ($productStore.inspects && Array.isArray($productStore.inspects)) {
									const selectedInspect = $productStore.inspects.find(
										(inspect) => inspect.id === Number(reqIdSelected)
									);
									uncheckedCount = selectedInspect?.req_count?.unchecked || 0;
								}

								await completeInspection(reqIdSelected, uncheckedCount);
							}}
						>
							미입고 처리
						</button>
					</SearchQaid>

					<DisplayKeyword display_keyword1={display_keyword} />
				</SearchUI>

				<div
					class="w-full px-2 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between"
				>
					<div class="w-full">
						<div class="w-full flex">
							<div class="flex items-center w-48 p-1 bg-orange-700 text-white">입고검수</div>
							<div class="flex flex-grow items-center pl-5 p-3 bg-orange-200">
								<input bind:value={checkQaidIndex} type="hidden" />
								<label class="input input-bordered input-sm flex items-center justify-center gap-2">
									<input
										bind:this={focusCheckQaidInput}
										bind:value={checkQaid}
										class="grow bg-base-100"
										onkeydown={async (e) => {
											if (e.key === 'Enter') {
												await handleCheckQaid(e);
											}
										}}
										type="text"
										placeholder="QAID 입력"
									/>

									<span
										onclick={async () => {
											await activeFocus();
										}}
										role="presentation"
									>
										<Icon class="cursor-pointer" data={faXmark} />
									</span>
								</label>

								<button
									class="btn btn-sm bg-orange-500 ml-3 hover:bg-orange-700 text-white"
									onclick={handleCheckQaid}
								>
									<Icon data={faCheckDouble} />
									입고검수
								</button>

								<span class="ml-1 text-xs text-black">
									* 검수중인 상품(QAID)의 로트번호를 검색 후 '입고검수'를 진행하면 검수와 동시에
									검수대기에서 빠지는 것을 확인할 수 있습니다.
								</span>
							</div>
						</div>

						<div
							class="p-2.5 bg-neutral-950 text-white text-5xl font-bold max-w-full break-words leading-tight"
						>
							{showProductName}
						</div>
					</div>
				</div>

				<div class="pt-3"></div>

				<!-- 리스트 시작 -->
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$productStore.pageTotal ?? 0}>
						{#snippet left()}
							<div class="pl-3">
								<ButtonSelectedDelete
									onclick={async (event: Event) => await handleDeleteProduct(event)}
									useTooltip={true}
								/>
							</div>
						{/snippet}
					</TableTop>

					{#snippet tableHeader()}
						<tr class="bg-base-content text-base-300 text-center">
							<th class="p-0.5">
								<input
									checked={allChecked}
									onchange={() => toggleAllCheck($productStore.products)}
									type="checkbox"
								/>
							</th>
							<th class="p-0.5">번호</th>
							<th class="p-0.5">입고날짜</th>
							<th class="p-0.5">로트번호</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">
								상품명<br />
								바코드
							</th>
							<th class="p-0.5">중복여부</th>
							<th class="p-0.5">단가</th>
						</tr>
					{/snippet}

					<table class="table text-xs table-pin-rows table-zebra">
						<thead class="uppercase">
							{@render tableHeader()}
						</thead>

						<tfoot class="uppercase">
							{@render tableHeader()}
						</tfoot>

						<tbody>
							{#if $productStore.products}
								{#each $productStore.products as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
											<input
												bind:checked={idChecked[index]}
												bind:group={ids}
												value={item.id}
												type="checkbox"
											/>
										</td>
										<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">
											<span
												class={item.status === 10 && isOverDueDate(item.req.req_at, item.rg)
													? 'font-bold text-red-700'
													: ''}
											>
												{item.req.req_at}
											</span>
										</td>
										<td class="w-[110px] min-w-[70px] max-w-[110px] p-0.5 text-center">
											{item.lot.name}
										</td>
										<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
											<div class="flex items-center">
												<div class="w-1/2 p-0">{item.cate4.name}</div>
												{#if item.cate5}
													<div class="w-1/2 p-0">{item.cate5.name}</div>
												{/if}
											</div>
										</td>
										<td class="w-[100px] max-w-[120px] p-0.5 text-center cursor-cell">
											<span
												class="flex justify-center"
												onclick={() => (checkQaid = item.qaid)}
												role="presentation"
											>
												{item.qaid}

												{#if item.rg === 'Y'}
													<Icon data={faRocket} class="mx-0.5 text-red-700" />
												{/if}
											</span>
										</td>
										<td class=" p-0.5">
											<p>{item.name}</p>
											<p class="text-xs text-base-content/50">{item.barcode}</p>
										</td>
										<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
											{#if item.duplicated === 'N'}
												-
											{:else}
												중복
											{/if}
										</td>
										<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">
											{getNumberFormat(item.amount)}
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>

				{#if $productStore.pageTotal && $productStore.pageTotal > 0}
					<!-- Pagination -->
					<Paginate
						store={productStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>

			<MessageModal
				bind:this={modal}
				type={modalType}
				title={modalTitle}
				message={modalMessage}
				onClose={handleModalClose}
			/>
		</div>
	{/snippet}
</AppLayout>
