<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { getUser } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import {
		checkFileSize,
		executeAsk,
		executeMessage,
		formatDateToString,
		getBeginAt,
		getNumberFormat,
		handleCatch
	} from '$lib/Functions';

	import {
		inspectionStatusColor,
		inspectionStore,
		loadInspection,
		REQ_STATUS_CHECKED,
		REQ_STATUS_COMPLETED,
		REQ_STATUS_REGISTERED
	} from '$stores/inspectionStore';
	import {
		PRODUCT_CHECKED_STATUS_CHECKED, PRODUCT_CHECKED_STATUS_UNCHECKED,
		PRODUCT_STATUS_CARRIED_OUT,
		PRODUCT_STATUS_CHECKED_ON_PALLET, PRODUCT_STATUS_DELETED,
		PRODUCT_STATUS_EXPORTED,
		PRODUCT_STATUS_HELD,
		PRODUCT_STATUS_REGISTERED, PRODUCT_STATUS_REPAIRED, PRODUCT_STATUS_WAITING, productStore
	} from '$stores/productStore';
	import { PALLET_STATUS_CLOSED, PALLET_STATUS_LOADED } from '$stores/palletStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import ButtonReqRepair from '$components/Button/ReqRepair.svelte';
	import ButtonReqAdd from '$components/Button/ReqAdd.svelte';
	import ButtonReqPass from '$components/Button/ReqPass.svelte';
	import ButtonReqDelete from '$components/Button/ReqDelete.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faUserPen } from '@fortawesome/free-solid-svg-icons/faUserPen';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faApple } from '@fortawesome/free-brands-svg-icons/faApple';
	import { faLink } from '@fortawesome/free-solid-svg-icons/faLink';

	const user = getUser();
	const apiUrl = '/wms/reqs';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') || getBeginAt());
	let endAt = $state(page.url.searchParams.get('endAt') || formatDateToString(new Date()));
	let reqType = $state(page.url.searchParams.get('reqType') || '');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = '';
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// 모달창 시작 =============
	let modal: HTMLDialogElement;
	let modalTypeText = $state('등록');

	let modalReqId = ''; // 수정시 사용
	let modalReqType = $state('1'); // 수정시 사용
	let modalReqAt = $state(formatDateToString(new Date()));
	let modalReqStartRow = $state("3");
	let modalReqStatusStr = $state('0');
	let modalMemo = $state('');
	let isUpload = $state(true); // 업로드 input을 보여주느냐?
	// 모달창 종료 =============

	// 링크등록 모달 상태 =============
	let linkModal: HTMLDialogElement;
	let linkModalFile: File | null = $state(null);
	let linkModalFileName = $state('');
	let linkModalFileSize = $state('');
	let linkModalIsUploading = $state(false);
	let linkModalUploadProgress = $state(0);
	let linkModalUploadResult = $state<{
		success: boolean;
		message: string;
		data?: {
			processed_rows: number;
			created_records: number;
			skipped_records: number;
		};
	} | null>(null);
	let linkModalStatistics = $state<{
		total_links: number;
		links_with_product_id: number;
		links_with_item_id: number;
		complete_links: number;
		completion_rate: number;
	} | null>(null);
	// 링크등록 모달 상태 종료 =============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			beginAt: beginAt,
			endAt: endAt,
			reqType: reqType,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadInspection(`${apiUrl}?${apiSearchParams}`, user);

		if ($inspectionStore) {
			startNo = $inspectionStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(newPage: number) {
		p = newPage.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;

		makeData();
	}

	/**
	 * 검수 통과(미입고 처리)
	 *
	 * @param id
	 * @param unchecked 검수 대기 상품의 개수
	 */
	async function completeInspection(id: string, unchecked = 0) {
		if (!id) {
			await executeMessage('검수 통과(미입고 처리)에 오류가 있습니다.\n프로그래머에게 문의해 주세요.', 'error');
			return false;
		}
		
		let message = unchecked > 20
			? `상품이 20개 이상이면 시간이 많이 걸릴 수 있습니다.\n\n`
			: '';

		message += "해당 요청건의 등록된 상품을 모두 [검수 통과(미입고 처리)]처리 하시겠습니까?\n\n입고 검수(대기)에 남아 있는 상품은 미입고로 처리 됩니다.";

		const ask = await executeAsk(message);
		if (!ask) {
			return false;
		}

		isLoading = true;
		try {
			const { status, data } = await authClient.put(`/wms/inspections/complete/${id}`);

			if (status === 200 && data.success) {
				await executeMessage('검수 통과(미입고 처리) 작업이 완료 되었습니다.');
				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 요청서 삭제
	 *
	 * @param id
	 */
	async function deleteReq(id: string) {
		const result = await executeAsk('요청서 삭제를 선택하셨습니다.\n이 요청서에 속한 모든 상품과 데이터들이 삭제 됩니다.\n\n정말 이 요청서를 삭제 하시겠습니까?', 'warning');
		if (!result) {
			return false;
		}

		if (!id) {
			await executeMessage('요청서 삭제에 오류가 있습니다.\n프로그래머에게 문의해 주세요.', 'error');
			return false;
		}

		isLoading = true;
		
		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				await executeMessage('요청서가 삭제 되었습니다.');
				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	function clearDialog() {
		modalReqId = '';
		modalReqType = '1';
		modalReqAt = formatDateToString(new Date());
		modalReqStartRow = "3";
		modalMemo = '';
		isUpload = true;
	}

	async function createReq() {
		modalTypeText = '등록';

		clearDialog();
		modal.showModal();
	}

	async function editReq(id: string, type: string = 'memo') {
		const item = $inspectionStore.items.find(item => item.id === id);
		console.log(item);

		if (item) {
			modalTypeText = '수정';

			modalReqId = id;
			modalReqType = item.req_type.toString();
			modalReqAt = item.req_at;
			modalMemo = item.memo ?? '';
			modalReqStatusStr = item.status.toString();
			isUpload = type === 'upload';

			modal.showModal();
		} else {
			await executeMessage('수정이 불가능합니다.', 'error');
		}
	}

	function prepareFormData() {
		let formData = new FormData();

		if (modalReqId) {
			formData.append('req_id', modalReqId);
			formData.append('status', modalReqStatusStr);
		}
		formData.append('req_at', modalReqAt);
		formData.append('req_type', modalReqType);
		formData.append('start_row', modalReqStartRow);
		formData.append('memo', modalMemo);
		formData.append('userId', `${user?.id}`);
		formData.append('redirect', localUrl);

		return formData;
	}

	function uploadExcelFile(file: HTMLInputElement, formData: FormData) {
		if (isUpload && file.files && file.files.length > 0) {
			formData.append('excel', file.files[0]);
			return true;
		}

		return false;
	}

	// 링크등록 관련 함수들 =============
	
	/**
	 * 링크등록 모달 열기
	 */
	async function openLinkModal() {
		// 통계 데이터 로드
		await loadLinkStatistics();
		linkModal.showModal();
	}

	/**
	 * 링크등록 모달 닫기
	 */
	function closeLinkModal() {
		linkModal.close();
		// 상태 초기화
		linkModalFile = null;
		linkModalFileName = '';
		linkModalFileSize = '';
		linkModalIsUploading = false;
		linkModalUploadProgress = 0;
		linkModalUploadResult = null;
	}

	/**
	 * 파일 선택 처리
	 */
	function handleFileSelect(event: Event) {
		const target = event.target as HTMLInputElement;
		const file = target.files?.[0];
		
		if (!file) {
			linkModalFile = null;
			linkModalFileName = '';
			linkModalFileSize = '';
			return;
		}

		// 파일 검증
		const isValidFile = validateVidFile(file);
		if (!isValidFile.success) {
			executeMessage(isValidFile.message, 'error');
			target.value = '';
			return;
		}

		linkModalFile = file;
		linkModalFileName = file.name;
		linkModalFileSize = formatFileSize(file.size);
		linkModalUploadResult = null; // 이전 결과 초기화
	}

	/**
	 * Vid.xlsx 파일 검증
	 */
	function validateVidFile(file: File): { success: boolean; message: string } {
		// 파일 확장자 검증
		const fileName = file.name.toLowerCase();
		const validExtensions = ['.xlsx', '.xls'];
		const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext));
		
		if (!hasValidExtension) {
			return {
				success: false,
				message: 'Excel 파일(.xlsx, .xls)만 업로드 가능합니다.'
			};
		}

		// 파일 타입 검증
		const validTypes = [
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'application/vnd.ms-excel'
		];
		if (!validTypes.includes(file.type)) {
			return {
				success: false,
				message: 'Excel 파일만 업로드 가능합니다.'
			};
		}

		// 파일 크기 검증 (2MB)
		const maxSize = 2 * 1024 * 1024; // 2MB
		if (file.size > maxSize) {
			return {
				success: false,
				message: '파일 크기는 2MB를 초과할 수 없습니다.'
			};
		}

		return { success: true, message: '' };
	}

	/**
	 * 파일 크기 포맷팅
	 */
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	/**
	 * 링크 통계 데이터 로드
	 */
	async function loadLinkStatistics() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/vid/statistics`);
			
			if (status === 200 && data.success) {
				linkModalStatistics = data.data;
			} else {
				console.error('통계 데이터 로드 실패:', data.message);
			}
		} catch (e: any) {
			console.error('통계 데이터 로드 오류:', e);
		}
	}

	/**
	 * Vid.xlsx 파일 업로드
	 */
	async function uploadVidFile() {
		if (!linkModalFile) {
			await executeMessage('업로드할 파일을 선택해주세요.', 'error');
			return;
		}

		linkModalIsUploading = true;
		linkModalUploadProgress = 0;
		linkModalUploadResult = null;

		try {
			const formData = new FormData();
			formData.append('vid_file', linkModalFile);

			// 진행률 시뮬레이션 (실제로는 XMLHttpRequest를 사용해야 함)
			const progressInterval = setInterval(() => {
				if (linkModalUploadProgress < 90) {
					linkModalUploadProgress += Math.random() * 10;
				}
			}, 200);

			const { status, data } = await authClient.post(`${apiUrl}/vid/upload`, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});

			clearInterval(progressInterval);
			linkModalUploadProgress = 100;

			if (status === 200 && data.success) {
				linkModalUploadResult = {
					success: true,
					message: data.message,
					data: data.data
				};
				
				// 통계 데이터 새로고침
				await loadLinkStatistics();
				
				// 성공 메시지 표시
				await executeMessage('파일이 성공적으로 업로드되었습니다.');
			} else {
				linkModalUploadResult = {
					success: false,
					message: data.message || '업로드에 실패했습니다.'
				};
			}
		} catch (e: any) {
			linkModalUploadResult = {
				success: false,
				message: e.response?.data?.message || '업로드 중 오류가 발생했습니다.'
			};
		} finally {
			linkModalIsUploading = false;
		}
	}

	// 링크등록 관련 함수들 종료 =============

	async function storeReq() {
		if (!modalReqAt) {
			await executeMessage('입고 날짜를 입력해 주세요.');
			return false;
		}

		const file = document.getElementById('dialog_upload') as HTMLInputElement;
		const formData = prepareFormData();
		const excelUploaded = uploadExcelFile(file, formData);

		if (isUpload && !excelUploaded) {
			await executeMessage('파일을 업로드 할 수 없습니다.', 'error');
			return false;
		}

		isLoading = true;
		
		try {
			let tempApiUrl = apiUrl;
			if (modalReqId) {
				tempApiUrl = `${apiUrl}/${modalReqId}`;
			}

			const { status, data } = await authClient.post(tempApiUrl, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});

			if (status === 200 && data.success) {
				if (isUpload && file.files && file.files.length > 0) {
					await executeMessage('엑셀 파일이 정상적으로 업로드 되었습니다.\n입고 등록을 시작합니다.');
				} else {
					await executeMessage(`${modalTypeText} 되었습니다.`);
				}

				await makeData();
				clearDialog();
				modal.close();
			} else {
				await executeMessage('요청서 등록에 실패 했습니다.\n프로그래머에게 문의해 주세요.');
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	async function updateCount(id: number) {
		isLoading = true;
		
		try {
			const { status, data } = await authClient.get(`${apiUrl}/update-count/${id}`);

			if (status === 200 && data.success) {
				toast.success('카운터 업데이트 완료');
				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	onMount(async () => {
		inspectionStore.set({});
		await makeData();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});
	
	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works' },
		{ title: '입고 목록', url: '/works' }
	];
</script>

<svelte:head>
	<title>입고 > 입고 목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					<SearchWarehousingDate {beginAt} {endAt} onUpdate={changeSearchParams}>
						<div class="relative pl-3">
							<label class="sr-only" for="table-search">분류</label>
								<select bind:value={reqType}
												class="select select-bordered select-sm focus:ring-0 focus:outline-none"
												onchange={async () => {
													await makeData();
												}}
								>
									<option value="">입고 분류 선택</option>
									<option value="1">일반</option>
									<option value="2">애플</option>
								</select>
						</div>
					</SearchWarehousingDate>
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$inspectionStore.pageTotal ?? 0}>
						{#snippet right()}
							<div class="pl-3 flex gap-2" >
								<button class="btn btn-info btn-sm"
												onclick={openLinkModal}
												type="button"
								>
									➀링크등록<Icon data={faLink} />
								</button>
								<button class="btn btn-primary btn-sm"
												onclick={createReq}
												type="button"
								>
									➁입고 등록<Icon data={faPlus} />
								</button>
							</div>
						{/snippet}
					</TableTop>
					
					{#snippet tableHeder()}
						<tr class="bg-base-content text-base-300 text-center">
							<th>번호</th>
							<th>입고일</th>
							<th>
								<p>입고</p>
								<p class="text-error">(미입고)</p>
							</th>
							<th>중복</th>
							<th class="min-w-[70px] max-w-[70px] p-0.5">입고검수<br>(대기)</th>
							<th>외주반출</th>
							<th>점검대기</th>
							<th>점검완료<br>(대기 신청)</th>
							<th>적재중</th>
							<th>출고대기<br>(마감)</th>
							<th>출고완료</th>
							<th>보류<br>(삭제)</th>
							<th>등록일시</th>
							<th>메모</th>
							<th>상태</th>
							<th>검수일시</th>
							<th>작업</th>
						</tr>
					{/snippet}
					
					<table class="table table-xs text-xs table-zebra table-pin-rows">
						<thead class="uppercase">
							{@render tableHeder()}
						</thead>
						
						<tfoot class="uppercase">
							{@render tableHeder()}
						</tfoot>
						
						<tbody>
						{#if $inspectionStore.items}
							{#each $inspectionStore.items as item, index}
								<tr class="hover:bg-base-content/10">
									<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
										{#if user.role === 'Admin' || user.role === 'Super-Admin'}
											<span role="presentation" class="cursor-pointer"
														onclick={async () => {
															await updateCount(item.id)
														}}>
												{getNumberFormat(startNo - index)}
											</span>
										{:else}
											{getNumberFormat(startNo - index)}
										{/if}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5 text-center " onclick={async () => { await editReq(item.id) }}>
										<div class="w-full cursor-pointer tooltip" data-tip="요청서 수정/상품 추가">
											<p>
												<span>{item.req_at}</span>
												<span><Icon data={faPenToSquare} /></span>
											</p>
											<p class="flex justify-center">
												{#if item.req_type === 2}
													<span class="flex items-center gap-1 text-error">
														<Icon data={faApple} />
														<span>애플</span>
													</span>
												{:else}
													<span>일반</span>
												{/if}
											</p>
										</div>
									</td>
									<td class="p-0.5 text-right cursor-pointer">
										<p role="presentation"
											 onclick={async () => {await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}`)}}
										>
											{getNumberFormat(item.total_count)}
										</p>
										<p role="presentation"
											 class="mt-2 text-error"
											 onclick={async () => {await goto(`/works/undelivered?reqId=${item.id}&reqType=${item.req_type}`)}}
										>
											{getNumberFormat(item.req_count.undelivered)}
										</p>
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {await goto(`/works/duplicates?reqId=${item.id}&reqType=${item.req_type}`)}}
									>
										{getNumberFormat(item.req_count.duplicated)}
									</td>
									{#if item.req_count.unchecked > 0}
										<td class="min-w-[70px] max-w-[70px] p-0.5 text-right cursor-pointer"
												onclick={async () => {
													await goto(`/works/inspects?reqId=${item.id}&reqType=${item.req_type}`)
												}}
										>
											{getNumberFormat(item.req_count.unchecked)}
										</td>
									{:else}
										<td class="min-w-[70px] max-w-[70px] p-0.5 text-right">
											{getNumberFormat(item.req_count.unchecked)}
										</td>
									{/if}
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&productStatus=${PRODUCT_STATUS_CARRIED_OUT}`)
											}}
									>
										{getNumberFormat(item.req_count.carryout)}
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&checkedStatus=${PRODUCT_CHECKED_STATUS_CHECKED}&productStatus=${PRODUCT_STATUS_REGISTERED}`)
											}}
									>
										{getNumberFormat(item.req_count.checked)}
									</td>
									<td class="p-0.5 text-right cursor-pointer">
										<span class="mr-1 tooltip tooltip-bottom"
											 data-tip="점검/수리 완료"
											 role="presentation"
											 onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&checkedStatus=${PRODUCT_CHECKED_STATUS_CHECKED}&productStatus=${PRODUCT_STATUS_REPAIRED}`)
											}}
										>
											{getNumberFormat(item.req_count.repaired)}
										</span>
										(<span class="text-secondary tooltip tooltip-bottom"
											 data-tip="구성품 신청중(대기)"
											 role="presentation"
											 onclick={async () => {
											await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&checkedStatus=${PRODUCT_CHECKED_STATUS_CHECKED}&productStatus=${PRODUCT_STATUS_WAITING}`)
											}}
										>
											{getNumberFormat(item.req_count.waiting)}
										</span>)
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&checkedStatus=${PRODUCT_CHECKED_STATUS_CHECKED}&productStatus=${PRODUCT_STATUS_CHECKED_ON_PALLET}&palletStatus=${PALLET_STATUS_LOADED}`)
											}}
									>
										{getNumberFormat(item.req_count.checkout)}
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&checkedStatus=${PRODUCT_CHECKED_STATUS_CHECKED}&productStatus=${PRODUCT_STATUS_CHECKED_ON_PALLET}&palletStatus=${PALLET_STATUS_CLOSED}`)
											}}
									>
										{getNumberFormat(item.req_count.exporting)}
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&productStatus=${PRODUCT_STATUS_EXPORTED}`)
											}}
									>
										{getNumberFormat(item.req_count.completed)}
									</td>
									<td class="p-0.5 text-right cursor-pointer"
											onclick={async () => {
												await goto(`/works/tasks?reqId=${item.id}&reqType=${item.req_type}&productStatus=${PRODUCT_STATUS_DELETED}`)
											}}
									>
										{getNumberFormat(item.req_count.deleted)}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5 text-center">
										{item.created_at.substring(0, 10)}
									</td>
									<td class="w-[150px] min-w-[150px] p-0">
										{item.memo ?? ''}
									</td>
									<td class="w-[70px] min-w-[70px] max-w-[70px] p-0.5 text-center">
										{@html inspectionStatusColor(item.status)}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5 whitespace-normal">
										{#if item.checked_at}
											<div class="w-full flex items-center justify-center gap-1">
												<div class="flex-shrink-0">
													<Icon data={faUserPen} />
												</div>
												<div>
													<p>{item.checked_at.substring(0, 10)}</p>
													{#if item.checked_user}
														<p>{item.checked_user.name}</p>
													{/if}
												</div>
											</div>
										{:else}
											<div class="w-full text-center">
												-
											</div>
										{/if}
									</td>
									<td class="p-0.5 text-center">
										<div class="w-full flex items-center justify-center">
											{#if item.status === REQ_STATUS_REGISTERED}
												<ButtonReqAdd useTooltip={true} margin="p-1" onclick={async () => {await editReq(item.id, 'upload')}} />
											{/if}
											
											{#if item.req_count.unchecked > 0}
												<ButtonReqPass useTooltip={true} margin="p-1" onclick={async () => {await completeInspection(item.id, item.req_count.unchecked)}} />
											{/if}
											
											{#if item.req_count.checked > 0}
												<ButtonReqRepair useTooltip={true} margin="p-1" onclick={async () => {await goto(`/works/repairs`)}} />
											{/if}
											
											{#if item.status === REQ_STATUS_REGISTERED}
												<ButtonReqDelete useTooltip={true} margin="p-1" onclick={async () => {await deleteReq(item.id)}} />
											{/if}
										</div>
									</td>
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
					
					<!-- Pagination -->
					{#if $inspectionStore.pageTotal && $inspectionStore.pageTotal > 0}
						<Paginate
							store={inspectionStore}
							{localUrl}
							onPageChange={handlePageChange}
							{searchParams}
						/>
					{/if}
				</div>
			</section>
			
			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">
							<Icon class="cursor-pointer" data={faXmark} />
						</button>
					</form>
					
					<h3 class="font-bole text-lg">입고 등록</h3>
					
					<div class="mt-4 grid grid-cols-2 gap-4">
						<div>
						<label class="label text-sm font-bold" for="dialog_request_date">입고날짜</label>
						<input bind:value={modalReqAt}
									 class="input input-bordered w-full"
									 id="dialog_request_date"
									 type="date">
						</div>
						<div>
							<label class="label text-sm font-bold" for="dialog_type">분류</label>
							<select bind:value={modalReqType}
											class="select select-bordered w-full"
											id="dialog_type"
							>
								<option value="1">일반</option>
								<option value="2">애플</option>
							</select>
						</div>
					</div>
					
					<div class="mt-2">
						<label class="label text-sm font-bold" for="dialog_memo">메모</label>
						<textarea bind:value={modalMemo}
											class="textarea textarea-bordered textarea-lg w-full"
											id="dialog_memo"></textarea>
					</div>
					
					{#if isUpload}
						<div class="mt-2 font-bold">
							<label class="label text-sm font-bold" for="dialog_start_row">시작 행 번호</label>
							<select bind:value={modalReqStartRow}
											class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
											name="start_row"
											id="dialog_start_row"
							>
								<option value="2">2</option>
								<option value="3">3</option>
							</select>
							(제목 행을 제외한 <span class="text-error underline">시작 행 번호</span>를 꼭 확인해 주세요.)
						</div>
						
						<div class="mt-2">
							<label class="label text-sm font-bold" for="dialog_upload">파일 업로드</label>
							<input accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
										 class="file-input file-input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="dialog_upload"
										 name="excel"
										 type="file"
										 onchange={checkFileSize}
							/>
						</div>
					{/if}
					
					{#if modalTypeText === '수정'}
						<div class="mt-2">
							<label class="label text-sm font-bold" for="product_status">상태</label>
							<select bind:value={modalReqStatusStr}
											class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
							>
								<option value="10">등록</option>
								<option value="30">점검중</option>
								<option value="50">완료</option>
							</select>
						</div>
					{/if}
					
					<div class="modal-action flex justify-between">
						<div>
							<button class="btn btn-primary"
											onclick={() => storeReq()}
											type="button"
							>
								<Icon class="w-5 h-5" data={faSave} />
								입고 {modalTypeText}
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button class="btn btn-warning tooltip tooltip-left"
												data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
			
			<!-- 링크등록 모달 -->
			<dialog class="modal" bind:this={linkModal}>
				<div class="modal-box w-3/4 max-w-4xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" onclick={closeLinkModal}>
							<Icon class="cursor-pointer" data={faXmark} />
						</button>
					</form>
					
					<h3 class="font-bold text-lg mb-4">링크등록 - Viid 엑셀 파일 업로드</h3>
					
					<!-- 통계 대시보드 -->
					{#if linkModalStatistics}
						<div class="bg-base-200 p-4 rounded-lg mb-4">
							<h4 class="font-semibold text-base mb-3">📊 링크 자료 통계</h4>
							<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
								<div class="stat bg-base-100 rounded-lg p-3">
									<div class="stat-title text-xs">전체 링크</div>
									<div class="stat-value text-lg">{linkModalStatistics.total_links.toLocaleString()}</div>
								</div>
								<div class="stat bg-base-100 rounded-lg p-3">
									<div class="stat-title text-xs">Product ID 있음</div>
									<div class="stat-value text-lg">{linkModalStatistics.links_with_product_id.toLocaleString()}</div>
								</div>
								<div class="stat bg-base-100 rounded-lg p-3">
									<div class="stat-title text-xs">Item ID 있음</div>
									<div class="stat-value text-lg">{linkModalStatistics.links_with_item_id.toLocaleString()}</div>
								</div>
								<div class="stat bg-base-100 rounded-lg p-3">
									<div class="stat-title text-xs">완성도</div>
									<div class="stat-value text-lg">{linkModalStatistics.completion_rate.toFixed(1)}%</div>
									<div class="stat-desc">
										{linkModalStatistics.complete_links.toLocaleString()} / {linkModalStatistics.total_links.toLocaleString()}
									</div>
								</div>
							</div>
						</div>
					{/if}
					
					<!-- 파일 업로드 영역 -->
					<div class="space-y-4">
						<!-- 파일 선택 -->
						<div>
							<label class="label">
								<span class="label-text font-semibold">링크 등록용 엑셀 파일 선택</span>
								<span class="label-text-alt text-error">* 필수</span>
							</label>
							<div class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
								<input
									type="file"
									accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
									class="file-input file-input-bordered w-full max-w-xs"
									onchange={handleFileSelect}
									disabled={linkModalIsUploading}
								/>
														<div class="mt-2 text-sm text-base-content/70">
							<p>지원 형식: <span class="font-mono">.xlsx</span> 또는 <span class="font-mono">.xls</span></p>
							<p>최대 크기: 2MB</p>
						</div>
							</div>
						</div>
						
						<!-- 선택된 파일 정보 -->
						{#if linkModalFileName}
							<div class="bg-base-100 p-4 rounded-lg border">
								<h4 class="font-semibold mb-2">선택된 파일</h4>
								<div class="flex items-center gap-2">
									<Icon data={faLink} class="text-primary" />
									<span class="font-mono">{linkModalFileName}</span>
									<span class="text-sm text-base-content/70">({linkModalFileSize})</span>
								</div>
							</div>
						{/if}
						
						<!-- 업로드 진행률 -->
						{#if linkModalIsUploading}
							<div class="bg-base-100 p-4 rounded-lg border">
								<h4 class="font-semibold mb-2">업로드 진행 중...</h4>
								<progress class="progress progress-primary w-full" value={linkModalUploadProgress} max="100"></progress>
								<div class="text-center mt-2 text-sm">{Math.round(linkModalUploadProgress)}%</div>
							</div>
						{/if}
						
						<!-- 업로드 결과 -->
						{#if linkModalUploadResult}
							<div class="bg-base-100 p-4 rounded-lg border">
								{#if linkModalUploadResult.success}
									<div class="flex items-center gap-2 text-success mb-2">
										<Icon data={faSave} />
										<h4 class="font-semibold">업로드 성공!</h4>
									</div>
									<p class="text-sm mb-3">{linkModalUploadResult.message}</p>
									{#if linkModalUploadResult.data}
										<div class="grid grid-cols-3 gap-4 text-sm">
											<div class="stat bg-base-200 rounded p-2">
												<div class="stat-title text-xs">처리된 행</div>
												<div class="stat-value text-base">{linkModalUploadResult.data.processed_rows.toLocaleString()}</div>
											</div>
											<div class="stat bg-base-200 rounded p-2">
												<div class="stat-title text-xs">새로 생성</div>
												<div class="stat-value text-base">{linkModalUploadResult.data.created_records.toLocaleString()}</div>
											</div>
											<div class="stat bg-base-200 rounded p-2">
												<div class="stat-title text-xs">건너뛴 레코드</div>
												<div class="stat-value text-base">{linkModalUploadResult.data.skipped_records.toLocaleString()}</div>
											</div>
										</div>
									{/if}
								{:else}
									<div class="flex items-center gap-2 text-error mb-2">
										<Icon data={faXmark} />
										<h4 class="font-semibold">업로드 실패</h4>
									</div>
									<p class="text-sm">{linkModalUploadResult.message}</p>
								{/if}
							</div>
						{/if}
					</div>
					
					<!-- 모달 액션 버튼 -->
					<div class="modal-action flex justify-between">
						<div>
							<button
								class="btn btn-primary"
								onclick={uploadVidFile}
								disabled={!linkModalFile || linkModalIsUploading}
								type="button"
							>
								{#if linkModalIsUploading}
									<span class="loading loading-spinner loading-sm"></span>
								{:else}
									<Icon data={faSave} />
								{/if}
								파일 업로드
							</button>
						</div>
						<div>
							<button
								class="btn btn-warning tooltip tooltip-left"
								data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								onclick={closeLinkModal}
							>
								<Icon data={faXmark} />
								닫기
							</button>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
