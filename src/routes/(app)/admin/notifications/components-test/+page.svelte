<script lang="ts">
	import { onMount } from 'svelte';
	import AppLayout from '$lib/components/Layouts/AppLayout.svelte';
	import TitleBar from '$lib/components/UI/TitleBar.svelte';
	import {
		PriorityBadge,
		StatusBadge,
		ReadStatusBadge,
		TargetTypeIcon,
		TargetSelector,
		DeliveryStatusIndicator,
		NotificationPreview,
		EmptyState,
		LoadingSpinner
	} from '$lib/components/Notification/Common';
	import type { NotificationGroup } from '$lib/types/notification';
	import type { Member } from '$lib/types/types';

	// 테스트 데이터
	let selectedTargetType = $state<'all' | 'group' | 'individual'>('all');
	let selectedTargetId = $state<number | undefined>(undefined);
	let previewTitle = $state('테스트 알림 제목');
	let previewContent = $state('이것은 테스트 알림 내용입니다.\n여러 줄로 작성할 수 있습니다.');
	let previewPriority = $state<'low' | 'normal' | 'high' | 'urgent'>('normal');
	let previewActionUrl = $state('https://example.com');

	// 모의 데이터
	const mockGroups: NotificationGroup[] = [
		{
			id: 1,
			name: '개발팀',
			description: '소프트웨어 개발팀',
			members_count: 5,
			created_by: 1,
			created_at: '2024-01-01',
			updated_at: '2024-01-01'
		},
		{
			id: 2,
			name: '디자인팀',
			description: 'UI/UX 디자인팀',
			members_count: 3,
			created_by: 1,
			created_at: '2024-01-01',
			updated_at: '2024-01-01'
		}
	];

	const mockMembers: Member[] = [
		{
			id: 1,
			name: '김개발',
			email: '<EMAIL>',
			status: 1,
			created_at: '2024-01-01',
			updated_at: '2024-01-01'
		},
		{
			id: 2,
			name: '이디자인',
			email: '<EMAIL>',
			status: 1,
			created_at: '2024-01-01',
			updated_at: '2024-01-01'
		}
	];

	function handleTargetChange(targetType: 'all' | 'group' | 'individual', targetId?: number) {
		selectedTargetType = targetType;
		selectedTargetId = targetId;
	}

	const breadcrumbs = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '컴포넌트 테스트', url: '/admin/notifications/components-test' }
	];
</script>

<AppLayout>
	<TitleBar {breadcrumbs} />

	<div class="container mx-auto p-6 space-y-8">
		<div class="text-center">
			<h1 class="text-3xl font-bold mb-2">알림 관리 공통 컴포넌트 테스트</h1>
			<p class="text-base-content/70">구현된 공통 컴포넌트들의 동작을 확인합니다.</p>
		</div>

		<!-- 배지 컴포넌트들 -->
		<div class="card bg-base-100 shadow-sm border border-base-300">
			<div class="card-body">
				<h2 class="card-title">배지 컴포넌트</h2>
				<div class="space-y-4">
					<!-- 우선순위 배지 -->
					<div>
						<h3 class="font-semibold mb-2">우선순위 배지</h3>
						<div class="flex flex-wrap gap-2">
							<PriorityBadge priority="low" />
							<PriorityBadge priority="normal" />
							<PriorityBadge priority="high" />
							<PriorityBadge priority="urgent" />
						</div>
					</div>

					<!-- 상태 배지 -->
					<div>
						<h3 class="font-semibold mb-2">상태 배지</h3>
						<div class="flex flex-wrap gap-2">
							<StatusBadge status="draft" />
							<StatusBadge status="sent" />
							<StatusBadge status="cancelled" />
						</div>
					</div>

					<!-- 읽음 상태 배지 -->
					<div>
						<h3 class="font-semibold mb-2">읽음 상태 배지</h3>
						<div class="flex flex-wrap gap-2">
							<ReadStatusBadge isRead={true} />
							<ReadStatusBadge isRead={false} />
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 대상 타입 아이콘 -->
		<div class="card bg-base-100 shadow-sm border border-base-300">
			<div class="card-body">
				<h2 class="card-title">대상 타입 아이콘</h2>
				<div class="flex flex-wrap gap-4">
					<TargetTypeIcon targetType="all" showText={true} />
					<TargetTypeIcon targetType="group" showText={true} />
					<TargetTypeIcon targetType="individual" showText={true} />
				</div>
			</div>
		</div>

		<!-- 대상 선택기 -->
		<div class="card bg-base-100 shadow-sm border border-base-300">
			<div class="card-body">
				<h2 class="card-title">대상 선택기</h2>
				<TargetSelector
					{selectedTargetType}
					{selectedTargetId}
					groups={mockGroups}
					members={mockMembers}
					onTargetChange={handleTargetChange}
				/>
			</div>
		</div>

		<!-- 전송 상태 표시기 -->
		<div class="card bg-base-100 shadow-sm border border-base-300">
			<div class="card-body">
				<h2 class="card-title">전송 상태 표시기</h2>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<DeliveryStatusIndicator
						totalRecipients={100}
						deliveredCount={85}
						readCount={60}
						failedCount={5}
					/>
					<DeliveryStatusIndicator
						totalRecipients={50}
						deliveredCount={30}
						readCount={20}
						isDelivering={true}
					/>
				</div>
			</div>
		</div>

		<!-- 알림 미리보기 -->
		<div class="card bg-base-100 shadow-sm border border-base-300">
			<div class="card-body">
				<h2 class="card-title">알림 미리보기</h2>
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
					<div class="space-y-2">
						<label class="form-control">
							<div class="label">
								<span class="label-text">제목</span>
							</div>
							<input
								type="text"
								class="input input-bordered"
								bind:value={previewTitle}
								placeholder="알림 제목을 입력하세요"
							/>
						</label>
						<label class="form-control">
							<div class="label">
								<span class="label-text">내용</span>
							</div>
							<textarea
								class="textarea textarea-bordered h-24"
								bind:value={previewContent}
								placeholder="알림 내용을 입력하세요"
							></textarea>
						</label>
					</div>
					<div class="space-y-2">
						<label class="form-control">
							<div class="label">
								<span class="label-text">우선순위</span>
							</div>
							<select class="select select-bordered" bind:value={previewPriority}>
								<option value="low">낮음</option>
								<option value="normal">보통</option>
								<option value="high">높음</option>
								<option value="urgent">긴급</option>
							</select>
						</label>
						<label class="form-control">
							<div class="label">
								<span class="label-text">액션 URL</span>
							</div>
							<input
								type="url"
								class="input input-bordered"
								bind:value={previewActionUrl}
								placeholder="https://example.com"
							/>
						</label>
					</div>
				</div>
				<NotificationPreview
					title={previewTitle}
					content={previewContent}
					priority={previewPriority}
					targetType={selectedTargetType}
					targetName={selectedTargetType === 'group'
						? mockGroups.find((g) => g.id === selectedTargetId)?.name
						: selectedTargetType === 'individual'
							? mockMembers.find((m) => m.id === selectedTargetId)?.name
							: undefined}
					targetCount={selectedTargetType === 'all'
						? mockMembers.length
						: selectedTargetType === 'group'
							? mockGroups.find((g) => g.id === selectedTargetId)?.members_count || 0
							: 1}
					actionUrl={previewActionUrl}
					senderName="관리자"
					showMobilePreview={true}
				/>
			</div>
		</div>

		<!-- 빈 상태 및 로딩 -->
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div class="card bg-base-100 shadow-sm border border-base-300">
				<div class="card-body">
					<h2 class="card-title">빈 상태</h2>
					<EmptyState
						title="알림이 없습니다"
						description="아직 전송된 알림이 없습니다. 새 알림을 작성해보세요."
						actionText="알림 작성"
						actionHandler={() => alert('알림 작성 페이지로 이동')}
					/>
				</div>
			</div>
			<div class="card bg-base-100 shadow-sm border border-base-300">
				<div class="card-body">
					<h2 class="card-title">로딩 스피너</h2>
					<LoadingSpinner text="알림을 불러오는 중..." />
				</div>
			</div>
		</div>
	</div>
</AppLayout>
