<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';
	import type { NotificationFilter, Notification } from '$lib/types/notification';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import HistoryList from '$components/Notification/History/HistoryList.svelte';
	import StatusIndicator from '$components/Notification/History/StatusIndicator.svelte';
	import NotificationDetailModal from '$components/Notification/History/NotificationDetailModal.svelte';

	import {
		historyStore,
		loadNotificationHistory,
		refreshHistory,
		cancelNotification,
		getNotificationDetail,
		debouncedSearch,
		historyCacheManager,
		historyPerformanceMonitor
	} from '$lib/stores/notification/historyStore';

	import {
		getPriorityText,
		getPriorityColor,
		getTargetTypeText,
		NOTIFICATION_PRIORITIES,
		NOTIFICATION_STATUSES,
		TARGET_TYPES
	} from '$lib/types/notification';

	let user: User = getUser();

	// 브레드크럼 설정
	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '알림 히스토리', url: '#' }
	];

	// 필터 상태
	let searchTerm = $state('');
	let selectedPriority = $state('');
	let selectedStatus = $state('');
	let selectedTargetType = $state('');
	let dateFrom = $state('');
	let dateTo = $state('');

	// 정렬 상태
	let sortBy = $state('created_at');
	let sortOrder = $state<'asc' | 'desc'>('desc');

	// 모달 상태
	let selectedNotification = $state<Notification | null>(null);
	let showDetailModal = $state(false);
	let showCancelModal = $state(false);
	let cancelLoading = $state(false);

	// URL 파라미터에서 초기 필터 설정
	onMount(() => {
		const urlParams = page.url.searchParams;

		// URL 파라미터에서 필터 값 읽기
		searchTerm = urlParams.get('search') || '';
		selectedPriority = urlParams.get('priority') || '';
		selectedStatus = urlParams.get('status') || '';
		selectedTargetType = urlParams.get('target_type') || '';
		dateFrom = urlParams.get('date_from') || '';
		dateTo = urlParams.get('date_to') || '';

		// 특별한 날짜 필터 처리
		const dateFilter = urlParams.get('date');
		if (dateFilter === 'today') {
			const today = new Date().toISOString().split('T')[0];
			dateFrom = today;
			dateTo = today;
		}

		// 초기 데이터 로드
		loadHistoryData();
	});

	// 페이지네이션 상태 (GuestSearch 패턴 적용)
	let p = $state('1');
	let pageSize = $state('16');
	let searchParams = $state('');
	let localUrl = $state('/admin/notifications/history');
	let startNo = $state(0);

	// 스토어 데이터 반응형 바인딩
	const storeData = $derived($historyStore);
	const notifications = $derived(storeData.notifications || []);
	const loading = $derived(storeData.loading || false);
	const error = $derived(storeData.error);
	const currentPage = $derived(storeData.pageCurrentPage || 1);
	const totalItems = $derived(storeData.pageTotal || 0);

	// 필터 객체 생성
	const currentFilter = $derived.by(() => {
		const filter: NotificationFilter = {};

		if (searchTerm.trim()) filter.search = searchTerm.trim();
		if (selectedPriority) filter.priority = selectedPriority;
		if (selectedStatus) filter.status = selectedStatus;
		if (selectedTargetType) filter.target_type = selectedTargetType;
		if (dateFrom) filter.date_from = dateFrom;
		if (dateTo) filter.date_to = dateTo;

		return filter;
	});

	// 성능 최적화된 히스토리 데이터 로드
	async function loadHistoryData(newPage: number = 1) {
		// 페이지 상태 업데이트
		p = newPage.toString();

		// 검색 파라미터 생성
		const params = new URLSearchParams({
			p: p,
			pageSize: pageSize,
			...Object.fromEntries(
				Object.entries(currentFilter).filter(([_, value]) => value !== undefined && value !== '')
			)
		});

		searchParams = params.toString();

		// 성능 최적화된 로드 함수 사용 (페이지 크기 포함)
		await loadNotificationHistory(user, newPage, currentFilter, parseInt(pageSize));

		// 시작 번호 계산
		if ($historyStore) {
			startNo = $historyStore.pageStartNo ?? 0;
		}
	}

	// 성능 최적화된 검색 실행 (디바운스 적용)
	function handleSearch() {
		p = '1'; // 검색 시 첫 페이지로 이동

		// 디바운스된 검색 사용 (성능 최적화)
		debouncedSearch(user, currentFilter, parseInt(pageSize));
		updateURL();
	}

	// 필터 초기화
	function resetFilters() {
		searchTerm = '';
		selectedPriority = '';
		selectedStatus = '';
		selectedTargetType = '';
		dateFrom = '';
		dateTo = '';
		loadHistoryData(1);
		updateURL();
	}

	// URL 업데이트
	function updateURL() {
		const params = new URLSearchParams();

		if (searchTerm.trim()) params.set('search', searchTerm.trim());
		if (selectedPriority) params.set('priority', selectedPriority);
		if (selectedStatus) params.set('status', selectedStatus);
		if (selectedTargetType) params.set('target_type', selectedTargetType);
		if (dateFrom) params.set('date_from', dateFrom);
		if (dateTo) params.set('date_to', dateTo);

		const url = params.toString() ? `?${params.toString()}` : '';
		goto(`/admin/notifications/history${url}`, { replaceState: true });
	}

	// 성능 최적화된 새로고침
	async function handleRefresh() {
		await refreshHistory(user, currentFilter, parseInt(pageSize));
	}

	// 페이지 변경 핸들러 (GuestSearch 패턴 적용)
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await loadHistoryData(newPage);
	}

	// 검색 파라미터 변경 핸들러 (GuestSearch 패턴 적용)
	async function changeSearchParams(newParams: string) {
		// URL 파라미터 업데이트 후 데이터 다시 로드
		const params = new URLSearchParams(newParams);
		const pageSize = params.get('pageSize');

		if (pageSize) {
			// 페이지 크기가 변경된 경우 첫 페이지로 이동
			await loadHistoryData(1);
		}

		updateURL();
	}

	// 정렬 변경
	function handleSort(field: string) {
		if (sortBy === field) {
			sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
		} else {
			sortBy = field;
			sortOrder = 'desc';
		}
		loadHistoryData(1);
	}

	// 알림 상세 보기
	async function handleNotificationClick(notification: Notification) {
		const detail = await getNotificationDetail(user, notification.id);
		if (detail) {
			selectedNotification = detail.data;
			showDetailModal = true;
		}
	}

	// 알림 취소 확인
	function handleCancelClick(notification: Notification) {
		console.log('알림 취소 확인(handleCancelClick): ', notification);
		selectedNotification = notification;
		showCancelModal = true;
	}

	// 알림 취소 실행
	async function confirmCancel() {
		if (selectedNotification && !cancelLoading) {
			console.log('알림 취소 실행(confirmCancel): ', selectedNotification);
			cancelLoading = true;
			try {
				const success = await cancelNotification(user, selectedNotification.id);
				if (success) {
					showCancelModal = false;
					selectedNotification = null;
					await loadHistoryData(currentPage);
				}
			} finally {
				cancelLoading = false;
			}
		}
	}

	// 모달 닫기
	function closeModals() {
		if (!cancelLoading) {
			showDetailModal = false;
			showCancelModal = false;
			selectedNotification = null;
		}
	}

	// 빠른 날짜 필터
	function setQuickDateFilter(type: 'today' | 'week' | 'month') {
		const today = new Date();
		const todayStr = today.toISOString().split('T')[0];

		switch (type) {
			case 'today':
				dateFrom = todayStr;
				dateTo = todayStr;
				break;
			case 'week':
				const weekAgo = new Date(today);
				weekAgo.setDate(weekAgo.getDate() - 7);
				dateFrom = weekAgo.toISOString().split('T')[0];
				dateTo = todayStr;
				break;
			case 'month':
				const monthAgo = new Date(today);
				monthAgo.setMonth(monthAgo.getMonth() - 1);
				dateFrom = monthAgo.toISOString().split('T')[0];
				dateTo = todayStr;
				break;
		}

		handleSearch();
	}
</script>

<svelte:head>
	<title>알림 히스토리</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full p-4">
					<!-- 페이지 헤더 -->
					<div class="mb-6">
						<h1 class="text-2xl font-bold text-base-content mb-2">알림 히스토리</h1>
						<p class="text-base-content/70">전송된 알림의 기록을 확인하고 관리하세요</p>
					</div>

					<!-- 검색 및 필터 영역 -->
					<SearchUI>
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
							<!-- 검색어 입력 -->
							<div class="form-control">
								<label class="label" for="search-input">
									<span class="label-text text-sm">검색</span>
								</label>
								<input
									id="search-input"
									type="text"
									placeholder="제목 또는 내용 검색..."
									class="input input-bordered input-sm"
									bind:value={searchTerm}
									onkeydown={(e) => e.key === 'Enter' && handleSearch()}
								/>
							</div>

							<!-- 우선순위 필터 -->
							<div class="form-control">
								<label class="label" for="priority-select">
									<span class="label-text text-sm">우선순위</span>
								</label>
								<select
									id="priority-select"
									class="select select-bordered select-sm"
									bind:value={selectedPriority}
									onchange={handleSearch}
								>
									<option value="">전체</option>
									{#each NOTIFICATION_PRIORITIES as priority}
										<option value={priority.value}>{priority.text}</option>
									{/each}
								</select>
							</div>

							<!-- 상태 필터 -->
							<div class="form-control">
								<label class="label" for="status-select">
									<span class="label-text text-sm">상태</span>
								</label>
								<select
									id="status-select"
									class="select select-bordered select-sm"
									bind:value={selectedStatus}
									onchange={handleSearch}
								>
									<option value="">전체</option>
									{#each NOTIFICATION_STATUSES as status}
										<option value={status.value}>{status.text}</option>
									{/each}
								</select>
							</div>

							<!-- 대상 타입 필터 -->
							<div class="form-control">
								<label class="label" for="target-type-select">
									<span class="label-text text-sm">대상</span>
								</label>
								<select
									id="target-type-select"
									class="select select-bordered select-sm"
									bind:value={selectedTargetType}
									onchange={handleSearch}
								>
									<option value="">전체</option>
									{#each TARGET_TYPES as targetType}
										<option value={targetType.value}>{targetType.text}</option>
									{/each}
								</select>
							</div>

							<!-- 시작 날짜 -->
							<div class="form-control">
								<label class="label" for="date-from">
									<span class="label-text text-sm">시작 날짜</span>
								</label>
								<input
									id="date-from"
									type="date"
									class="input input-bordered input-sm"
									bind:value={dateFrom}
									onchange={handleSearch}
								/>
							</div>

							<!-- 종료 날짜 -->
							<div class="form-control">
								<label class="label" for="date-to">
									<span class="label-text text-sm">종료 날짜</span>
								</label>
								<input
									id="date-to"
									type="date"
									class="input input-bordered input-sm"
									bind:value={dateTo}
									onchange={handleSearch}
								/>
							</div>
						</div>

						<!-- 빠른 필터 및 액션 버튼 -->
						<div class="flex flex-wrap gap-2 mt-4">
							<!-- 빠른 날짜 필터 -->
							<div class="flex gap-1">
								<button class="btn btn-ghost btn-sm" onclick={() => setQuickDateFilter('today')}>
									오늘
								</button>
								<button class="btn btn-ghost btn-sm" onclick={() => setQuickDateFilter('week')}>
									최근 7일
								</button>
								<button class="btn btn-ghost btn-sm" onclick={() => setQuickDateFilter('month')}>
									최근 30일
								</button>
							</div>

							<div class="divider divider-horizontal"></div>

							<!-- 액션 버튼들 -->
							<button class="btn btn-primary btn-sm" onclick={handleSearch}>
								<i class="fas fa-search"></i>
								검색
							</button>

							<button
								class="btn btn-ghost btn-sm"
								onclick={resetFilters}
								disabled={!searchTerm &&
									!selectedPriority &&
									!selectedStatus &&
									!selectedTargetType &&
									!dateFrom &&
									!dateTo}
							>
								<i class="fas fa-undo"></i>
								초기화
							</button>

							<button
								class="btn btn-ghost btn-sm"
								class:loading
								onclick={handleRefresh}
								disabled={loading}
							>
								<i class="fas fa-sync-alt"></i>
								새로고침
							</button>

							<!-- 캐시 관리 버튼 (개발 모드에서만 표시) -->
							{#if import.meta.env.DEV}
								<div class="dropdown dropdown-end">
									<div tabindex="0" role="button" class="btn btn-ghost btn-sm">
										<i class="fas fa-cog"></i>
										성능
									</div>
									<ul
										tabindex="0"
										class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow"
									>
										<li>
											<button onclick={() => historyCacheManager.cleanup()}>
												<i class="fas fa-broom"></i>
												캐시 정리
											</button>
										</li>
										<li>
											<button onclick={() => historyCacheManager.clearAll()}>
												<i class="fas fa-trash"></i>
												캐시 전체 삭제
											</button>
										</li>
										<li>
											<button onclick={() => console.log(historyCacheManager.getStatus())}>
												<i class="fas fa-info"></i>
												캐시 상태 확인
											</button>
										</li>
										<li>
											<button onclick={() => console.log(historyPerformanceMonitor.getMetrics())}>
												<i class="fas fa-chart-line"></i>
												성능 메트릭 확인
											</button>
										</li>
									</ul>
								</div>
							{/if}
						</div>
					</SearchUI>

					<!-- TableTop 컴포넌트 (GuestSearch 패턴 적용) -->
					<TableTop onUpdate={changeSearchParams} {pageSize} total={totalItems} />

					<!-- 추가 액션 버튼 -->
					<div class="flex justify-end mb-4">
						<button
							class="btn btn-outline btn-sm"
							onclick={() => goto('/admin/notifications/send')}
						>
							<i class="fas fa-paper-plane"></i>
							새 알림 전송
						</button>
					</div>

					<!-- 에러 메시지 -->
					{#if error}
						<div class="alert alert-error mb-6">
							<i class="fas fa-exclamation-triangle"></i>
							<span>{error}</span>
							<div>
								<button class="btn btn-sm btn-ghost" onclick={handleRefresh}> 다시 시도 </button>
							</div>
						</div>
					{/if}

					<!-- 알림 히스토리 목록 -->
					<div class="card bg-base-100 shadow-sm">
						<div class="card-body p-0">
							<HistoryList
								{notifications}
								{loading}
								{sortBy}
								{sortOrder}
								onsort={(event: any) => handleSort(event.field)}
								onnotificationclick={(event: any) => handleNotificationClick(event.notification)}
								oncancelclick={(event: any) => handleCancelClick(event.notification)}
							/>
						</div>
					</div>

					<!-- 성능 최적화된 페이지네이션 -->
					{#if totalItems && totalItems > 0}
						<Paginate
							store={historyStore}
							{localUrl}
							onPageChange={handlePageChange}
							{searchParams}
							maxLinks={10}
							enablePerformanceMonitoring={false}
							enableSmartPageSize={false}
						/>
					{/if}
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>

<!-- 알림 상세 모달 -->
{#if selectedNotification}
	<NotificationDetailModal
		notification={selectedNotification}
		{user}
		show={showDetailModal}
		onclose={closeModals}
	/>
{/if}

<!-- 알림 취소 확인 모달 -->
{#if showCancelModal && selectedNotification}
	<div class="modal modal-open">
		<div class="modal-box max-w-md">
			<h3 class="font-bold text-lg mb-4">
				<i class="fas fa-ban text-error mr-2"></i>
				알림 취소 확인
			</h3>

			<div class="space-y-4">
				<!-- 알림 정보 -->
				<div class="bg-base-200 p-4 rounded-lg">
					<h4 class="font-semibold text-sm text-base-content/70 mb-2">취소할 알림</h4>
					<p class="font-medium">{selectedNotification.title}</p>
					<p class="text-sm text-base-content/60 mt-1">
						{#if selectedNotification.sent_at}
							전송시간: {new Date(selectedNotification.sent_at).toLocaleString('ko-KR')}
						{/if}
					</p>
				</div>

				<!-- 수신자 정보 -->
				{#if selectedNotification.recipients_count}
					<div class="bg-base-200 p-4 rounded-lg">
						<h4 class="font-semibold text-sm text-base-content/70 mb-2">수신자 현황</h4>
						<div class="flex justify-between items-center">
							<span class="text-sm">전체 수신자</span>
							<span class="font-medium">{selectedNotification.recipients_count}명</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm">읽은 사용자</span>
							<span class="font-medium">{selectedNotification.read_count || 0}명</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm text-error">읽지 않은 사용자</span>
							<span class="font-medium text-error">
								{(selectedNotification.recipients_count || 0) -
									(selectedNotification.read_count || 0)}명
							</span>
						</div>
					</div>
				{/if}

				<!-- 경고 메시지 -->
				<div class="alert alert-warning">
					<i class="fas fa-exclamation-triangle"></i>
					<div class="text-sm">
						<p class="font-medium">취소 시 다음과 같이 처리됩니다:</p>
						<ul class="list-disc list-inside mt-2 space-y-1">
							<li>아직 읽지 않은 사용자의 알림이 제거됩니다</li>
							<li>이미 읽은 알림은 그대로 유지됩니다</li>
							<li>취소된 알림은 복구할 수 없습니다</li>
						</ul>
					</div>
				</div>
			</div>

			<div class="modal-action">
				<button
					class="btn btn-error"
					class:loading={cancelLoading}
					onclick={confirmCancel}
					disabled={cancelLoading}
				>
					{#if cancelLoading}
						<span class="loading loading-spinner loading-sm"></span>
						취소 중...
					{:else}
						<i class="fas fa-ban"></i>
						알림 취소
					{/if}
				</button>
				<button class="btn" onclick={closeModals} disabled={cancelLoading}>
					<i class="fas fa-times"></i>
					닫기
				</button>
			</div>
		</div>
		<div
			class="modal-backdrop"
			onclick={() => !cancelLoading && closeModals()}
			role="button"
			tabindex="0"
			onkeydown={(e) => e.key === 'Escape' && !cancelLoading && closeModals()}
		></div>
	</div>
{/if}
