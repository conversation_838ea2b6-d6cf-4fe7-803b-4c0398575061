<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';
	import type { GroupFilter, NotificationGroup } from '$lib/types/notification';

	import {
		deleteGroup,
		loadGroups,
		groupStore,
		resetGroupStore
	} from '$lib/stores/notification/groupStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	// 그룹 폼 컴포넌트 import
	import GroupForm from '$lib/components/Notification/Group/GroupForm.svelte';
	// 그룹 상세 컴포넌트 import
	import GroupDetail from '$lib/components/Notification/Group/GroupDetail.svelte';

	// Font Awesome 아이콘 import
	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';

	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';
	import { getNumberFormat } from '$lib/Functions';

	let user: User = getUser();
	let currentPage = $state(1);
	let searchTerm = $state('');
	let loading = $state(false);
	let selectedGroupId = $state<number | null>(null);
	let showCreateModal = $state(false);
	let showEditModal = $state(false);
	let showDetailModal = $state(false);
	let showDeleteModal = $state(false);
	let groupToDelete = $state<NotificationGroup | null>(null);

	// URL 파라미터 변경 감지 (브라우저 뒤로가기/앞으로가기 대응만)
	// 초기 로드는 onMount에서 처리하므로 여기서는 제외
	let isPageMounted = false;
	$effect(() => {
		// 페이지가 마운트된 후에만 URL 변경 감지
		if (!isPageMounted) return;

		const urlParams = new URLSearchParams(page.url.search);
		const selected = urlParams.get('selected');
		const newSelectedId = selected ? parseInt(selected, 10) : null;

		// 현재 상태와 URL이 다를 때만 업데이트 (중복 업데이트 방지)
		if (selectedGroupId !== newSelectedId) {
			selectedGroupId = newSelectedId;
			showDetailModal = !!newSelectedId;
		}
	});

	// 그룹 데이터 로드
	onMount(async () => {
		resetGroupStore();

		// 초기 URL 파라미터 처리 (상태 설정만, 데이터 로드 전에)
		const urlParams = new URLSearchParams(page.url.search);
		const selected = urlParams.get('selected');
		if (selected) {
			selectedGroupId = parseInt(selected, 10);
			showDetailModal = true;
		}

		// 데이터 로드는 한 번만 실행
		await loadGroupData();

		// 페이지 마운트 완료 표시 (URL 변경 감지 활성화)
		isPageMounted = true;
	});

	// 그룹 데이터 로드 함수
	async function loadGroupData() {
		loading = true;
		try {
			const filter: GroupFilter = {
				search: searchTerm.trim()
			};

			await loadGroups(user, currentPage, filter);
		} finally {
			loading = false;
		}
	}

	// 검색 핸들러 (디바운싱 적용)
	let searchTimeout: NodeJS.Timeout;
	function handleSearch() {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(async () => {
			currentPage = 1; // 검색 시 첫 페이지로 이동
			await loadGroupData();
		}, 300);
	}

	// 페이지 변경 핸들러
	async function handlePageChange(page: number) {
		currentPage = page;
		await loadGroupData();
	}

	// 새로고침 핸들러
	async function handleRefresh() {
		await loadGroupData();
	}

	// 그룹 생성 모달 열기
	function openCreateModal() {
		showCreateModal = true;
	}

	// 그룹 수정 모달 열기
	function openEditModal(group: NotificationGroup) {
		selectedGroupId = group.id;
		showEditModal = true;
	}

	// 그룹 상세 보기 (Shallow Routing)
	function viewGroupDetail(group: NotificationGroup) {
		// 즉시 상태 업데이트
		selectedGroupId = group.id;
		showDetailModal = true;

		// URL 업데이트 (비동기)
		const url = new URL(page.url);
		url.searchParams.set('selected', group.id.toString());
		goto(url.toString(), { replaceState: true });
	}

	// 그룹 상세 모달 닫기
	function closeDetailModal() {
		// 즉시 상태 업데이트
		showDetailModal = false;
		selectedGroupId = null;

		// URL 업데이트 (비동기)
		const url = new URL(page.url);
		url.searchParams.delete('selected');
		goto(url.toString(), { replaceState: true });
	}

	// 그룹 삭제 확인 모달 열기
	function openDeleteModal(group: NotificationGroup) {
		groupToDelete = group;
		showDeleteModal = true;
	}

	// 그룹 삭제 실행
	async function handleDeleteGroup() {
		if (!groupToDelete) return;

		const deletedGroupId = groupToDelete.id;
		const success = await deleteGroup(user, deletedGroupId);
		if (success) {
			showDeleteModal = false;
			groupToDelete = null;

			// 삭제된 그룹이 현재 선택된 그룹이면 선택 해제
			if (selectedGroupId === deletedGroupId) {
				selectedGroupId = null;
				showDetailModal = false;
				// URL에서도 제거
				const url = new URL(page.url);
				url.searchParams.delete('selected');
				goto(url.toString(), { replaceState: true });
			}

			await loadGroupData(); // 목록 새로고침
		}
	}

	// 모달 닫기 핸들러들
	function closeCreateModal() {
		showCreateModal = false;
	}

	function closeEditModal() {
		showEditModal = false;
		selectedGroupId = null;
	}

	function closeDeleteModal() {
		showDeleteModal = false;
		groupToDelete = null;
	}

	// 그룹 생성/수정 완료 핸들러
	function handleGroupSaved() {
		closeCreateModal();
		closeEditModal();
		loadGroupData(); // 목록 새로고침
	}

	// 그룹 상세 정보 업데이트 핸들러
	function handleGroupUpdated() {
		loadGroupData(); // 목록 새로고침 (멤버 수 업데이트 등)
	}

	// 반응형 데이터
	const groups = $derived($groupStore.groups || []);
	const hasGroups = $derived(groups.length > 0);
	let startNo = $derived($groupStore.pageStartNo ?? 0);

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '그룹 관리', url: '#' }
	];
</script>

<svelte:head>
	<title>그룹 관리</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full p-4">
					<!-- 페이지 헤더 -->
					<div class="mb-6">
						<div
							class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
						>
							<div>
								<h1 class="text-2xl font-bold text-base-content mb-2">그룹 관리</h1>
								<p class="text-base-content/70">
									직원들을 그룹으로 관리하여 효율적으로 알림을 전송하세요
								</p>
							</div>
							<div class="flex gap-2">
								<button class="btn btn-primary btn-sm" onclick={openCreateModal}>
									<Icon data={faPlus} />
									새 그룹 생성
								</button>
								<button
									class="btn btn-ghost btn-sm"
									class:loading
									onclick={handleRefresh}
									disabled={loading}
								>
									<Icon data={faRefresh} />
									새로고침
								</button>
							</div>
						</div>
					</div>

					<!-- 좌우 분할 레이아웃 -->
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-300px)]">
						<!-- 왼쪽: 그룹 목록 -->
						<div class="flex flex-col">
							<!-- 검색 및 필터 영역 -->
							<SearchUI>
								<div class="flex flex-wrap gap-2 items-center">
									<div class="form-control">
										<label class="label" for="group-search">
											<span class="label-text text-sm">그룹 검색</span>
										</label>
										<div class="relative">
											<input
												id="group-search"
												type="text"
												placeholder="그룹명 또는 설명으로 검색..."
												class="input input-bordered input-sm w-full max-w-xs pl-10"
												bind:value={searchTerm}
												oninput={handleSearch}
											/>
											<Icon
												data={faSearch}
												class="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50 w-4 h-4"
											/>
										</div>
									</div>

									<div class="form-control">
										<div class="label">
											<span class="label-text text-sm">검색 초기화</span>
										</div>
										<button
											class="btn btn-ghost btn-sm"
											onclick={() => {
												searchTerm = '';
												handleSearch();
											}}
											disabled={!searchTerm}
										>
											초기화
										</button>
									</div>
								</div>
							</SearchUI>

							<!-- 그룹 목록 -->
							<div class="flex-1 mt-4 overflow-hidden">
								{#if loading}
									<div class="flex justify-center items-center py-12">
										<span class="loading loading-spinner loading-lg"></span>
										<span class="ml-2">그룹 목록을 불러오는 중...</span>
									</div>
								{:else if hasGroups}
									<div class="h-full flex flex-col">
										<div class="flex-1 overflow-y-auto">
											<table class="table table-zebra w-full">
												<thead class="sticky top-0 bg-base-100 z-10">
													<tr>
														<th class="text-center w-16">#</th>
														<th class="text-center w-24">활성</th>
														<th>그룹명</th>
														<th class="text-center w-40">멤버</th>
														<th class="text-center w-24">작업</th>
													</tr>
												</thead>
												<tbody>
													{#each groups as group, index}
														<tr
															class="hover:bg-base-200 cursor-pointer"
															class:bg-primary-100={selectedGroupId === group.id}
															onclick={() => viewGroupDetail(group)}
														>
															<td class="text-center">
																{getNumberFormat(startNo - index)}
															</td>
															<td class="text-center">
																<div class="font-medium text-base-content">
																	{#if group.is_active}
																		<div class="badge badge-success badge-sm">활성</div>
																	{:else}
																		<div class="badge badge-warning badge-sm">비활성</div>
																	{/if}
																</div>
															</td>
															<td>
																<div class="font-medium text-base-content">
																	{group.name}
																</div>
																<div class="text-xs text-base-content/70 truncate max-w-[200px]">
																	{group.description || '설명 없음'}
																</div>
															</td>
															<td class="text-center">
																<div class="badge badge-info badge-sm">
																	<Icon data={faUsers} class="w-3 h-3 mr-1" />
																	{group.members_count}명
																</div>
															</td>
															<td class="text-center">
																<div class="flex justify-center gap-1">
																	<button
																		class="btn btn-ghost btn-xs"
																		onclick={(e) => {
																			e.stopPropagation();
																			openEditModal(group);
																		}}
																		title="수정"
																	>
																		<Icon data={faEdit} />
																	</button>
																	<button
																		class="btn btn-ghost btn-xs text-error"
																		onclick={(e) => {
																			e.stopPropagation();
																			openDeleteModal(group);
																		}}
																		title="삭제"
																	>
																		<Icon data={faTrash} />
																	</button>
																</div>
															</td>
														</tr>
													{/each}
												</tbody>
											</table>
										</div>

										<!-- 페이지네이션 -->
										<div class="mt-4 border-t pt-4">
											<Paginate
												store={groupStore}
												localUrl="/admin/notifications/groups"
												onPageChange={handlePageChange}
												searchParams={searchTerm ? `search=${encodeURIComponent(searchTerm)}` : ''}
											/>
										</div>
									</div>
								{:else}
									<!-- 빈 상태 -->
									<div class="text-center py-12">
										<div class="mb-4">
											<Icon data={faUsers} class="w-16 h-16 text-base-content/30" />
										</div>
										<h3 class="text-lg font-medium text-base-content mb-2">
											{searchTerm ? '검색 결과가 없습니다' : '등록된 그룹이 없습니다'}
										</h3>
										<p class="text-base-content/70 mb-4">
											{searchTerm
												? '다른 검색어로 시도해보세요'
												: '새 그룹을 생성하여 직원들을 효율적으로 관리하세요'}
										</p>
										{#if !searchTerm}
											<button class="btn btn-primary" onclick={openCreateModal}>
												<Icon data={faPlus} />
												첫 번째 그룹 생성
											</button>
										{/if}
									</div>
								{/if}
							</div>
						</div>

						<!-- 오른쪽: 그룹 상세 정보 -->
						<div class="flex flex-col">
							<div class="bg-base-200 rounded-lg p-4 h-full">
								{#if selectedGroupId}
									<GroupDetail
										show={true}
										groupId={selectedGroupId}
										onclose={() => {}}
										onupdated={handleGroupUpdated}
										embedded={true}
									/>
								{:else}
									<div class="flex flex-col items-center justify-center h-full text-center">
										<div class="mb-4">
											<Icon data={faUsers} class="w-20 h-20 text-base-content/20" />
										</div>
										<h3 class="text-xl font-medium text-base-content/60 mb-2">그룹을 선택하세요</h3>
										<p class="text-base-content/50">
											왼쪽 목록에서 그룹을 클릭하면<br />
											상세 정보와 멤버 목록을 확인할 수 있습니다
										</p>
									</div>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>

		<!-- 그룹 생성 모달 -->
		<GroupForm show={showCreateModal} onclose={closeCreateModal} onsaved={handleGroupSaved} />

		<!-- 그룹 수정 모달 -->
		<GroupForm
			show={showEditModal}
			groupId={selectedGroupId}
			onclose={closeEditModal}
			onsaved={handleGroupSaved}
		/>

		<!-- 그룹 상세 모달 (모바일용) -->
		{#if showDetailModal}
			<div class="modal modal-open lg:hidden">
				<div class="modal-box max-w-4xl w-11/12 max-h-[90vh]">
					<GroupDetail
						show={true}
						groupId={selectedGroupId}
						onclose={closeDetailModal}
						onupdated={handleGroupUpdated}
					/>
				</div>
				<div
					class="modal-backdrop"
					onclick={closeDetailModal}
					role="button"
					tabindex="-1"
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							closeDetailModal();
						}
					}}
				></div>
			</div>
		{/if}

		<!-- 그룹 삭제 확인 모달 -->
		{#if showDeleteModal && groupToDelete}
			<div class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg mb-4">그룹 삭제 확인</h3>
					<p class="mb-4">
						<strong>"{groupToDelete.name}"</strong> 그룹을 정말 삭제하시겠습니까?
					</p>
					<p class="text-sm text-warning mb-6">
						이 작업은 되돌릴 수 없습니다. 그룹과 관련된 모든 멤버십 정보가 삭제됩니다.
					</p>
					<div class="modal-action">
						<button class="btn btn-ghost" onclick={closeDeleteModal}> 취소 </button>
						<button class="btn btn-error" onclick={handleDeleteGroup}>
							<Icon data={faTrash} />
							삭제
						</button>
					</div>
				</div>
				<div
					class="modal-backdrop"
					onclick={closeDeleteModal}
					role="button"
					tabindex="-1"
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							closeDeleteModal();
						}
					}}
				></div>
			</div>
		{/if}
	{/snippet}
</AppLayout>
