<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';
	import type { TemplateFilter, NotificationTemplate } from '$lib/types/notification';

	import {
		deleteTemplate,
		loadTemplates,
		templateStore,
		resetTemplateStore
	} from '$stores/notification/templateStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import TemplateForm from '$components/Notification/Template/TemplateForm.svelte';

	// Font Awesome 아이콘 import
	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faFileAlt } from '@fortawesome/free-solid-svg-icons/faFileAlt';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';
	import { faSortAmountDown } from '@fortawesome/free-solid-svg-icons/faSortAmountDown';
	import { faSortAmountUp } from '@fortawesome/free-solid-svg-icons/faSortAmountUp';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';

	import { getNumberFormat } from '$lib/Functions';
	import { getPriorityText, getPriorityColor } from '$lib/types/notification';

	let user: User = getUser();
	let currentPage = $state(1);
	let searchTerm = $state('');
	let priorityFilter = $state('');
	let sortBy = $state('usage_count'); // 기본값: 사용 빈도순
	let sortOrder = $state('desc'); // 기본값: 내림차순
	let loading = $state(false);
	let selectedTemplate = $state<NotificationTemplate | null>(null);
	let showCreateModal = $state(false);
	let showEditModal = $state(false);
	let showViewModal = $state(false);
	let showDeleteModal = $state(false);
	let templateToDelete = $state<NotificationTemplate | null>(null);

	// 템플릿 데이터 로드
	onMount(async () => {
		resetTemplateStore();
		await loadTemplateData();
	});

	// 템플릿 데이터 로드 함수
	async function loadTemplateData() {
		loading = true;
		try {
			const filter: TemplateFilter = {
				search: searchTerm.trim(),
				priority: priorityFilter || undefined
			};

			// 정렬 파라미터 추가
			const filterWithSort = {
				...filter,
				sort_by: sortBy,
				sort_order: sortOrder
			};

			await loadTemplates(user, currentPage, filterWithSort);
		} finally {
			loading = false;
		}
	}

	// 검색 핸들러 (디바운싱 적용)
	let searchTimeout: NodeJS.Timeout;
	function handleSearch() {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(async () => {
			currentPage = 1; // 검색 시 첫 페이지로 이동
			await loadTemplateData();
		}, 300);
	}

	// 필터 변경 핸들러
	function handleFilterChange() {
		currentPage = 1;
		loadTemplateData();
	}

	// 정렬 변경 핸들러
	function handleSortChange(newSortBy: string) {
		if (sortBy === newSortBy) {
			// 같은 컬럼 클릭 시 정렬 순서 변경
			sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
		} else {
			// 다른 컬럼 클릭 시 해당 컬럼으로 정렬 (기본: 내림차순)
			sortBy = newSortBy;
			sortOrder = 'desc';
		}
		currentPage = 1;
		loadTemplateData();
	}

	// 페이지 변경 핸들러
	async function handlePageChange(page: number) {
		currentPage = page;
		await loadTemplateData();
	}

	// 새로고침 핸들러
	async function handleRefresh() {
		await loadTemplateData();
	}

	// 템플릿 생성 모달 열기
	function openCreateModal() {
		showCreateModal = true;
	}

	// 템플릿 수정 모달 열기
	function openEditModal(template: NotificationTemplate) {
		selectedTemplate = template;
		showEditModal = true;
	}

	// 템플릿 보기 모달 열기
	function openViewModal(template: NotificationTemplate) {
		selectedTemplate = template;
		showViewModal = true;
	}

	// 템플릿 삭제 확인 모달 열기
	function openDeleteModal(template: NotificationTemplate) {
		templateToDelete = template;
		showDeleteModal = true;
	}

	// 템플릿 삭제 실행
	async function handleDeleteTemplate() {
		if (!templateToDelete) return;

		const success = await deleteTemplate(user, templateToDelete.id);
		if (success) {
			showDeleteModal = false;
			templateToDelete = null;
			await loadTemplateData(); // 목록 새로고침
		}
	}

	// 모달 닫기 핸들러들
	function closeCreateModal() {
		showCreateModal = false;
	}

	function closeEditModal() {
		showEditModal = false;
		selectedTemplate = null;
	}

	function closeViewModal() {
		showViewModal = false;
		selectedTemplate = null;
	}

	function closeDeleteModal() {
		showDeleteModal = false;
		templateToDelete = null;
	}

	// 템플릿 생성/수정 완료 핸들러
	function handleTemplateSaved() {
		closeCreateModal();
		closeEditModal();
		loadTemplateData(); // 목록 새로고침
	}

	// 반응형 데이터
	const templates = $derived($templateStore.templates || []);
	const hasTemplates = $derived(templates.length > 0);
	let startNo = $derived($templateStore.pageStartNo ?? 0);

	// 정렬 아이콘 표시 함수
	function getSortIcon(column: string) {
		if (sortBy !== column) return undefined;
		return sortOrder === 'asc' ? faSortAmountUp : faSortAmountDown;
	}

	// 우선순위 옵션
	const priorityOptions = [
		{ value: '', text: '전체' },
		{ value: 'low', text: '낮음' },
		{ value: 'normal', text: '보통' },
		{ value: 'high', text: '높음' },
		{ value: 'urgent', text: '긴급' }
	];

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '템플릿 관리', url: '#' }
	];
</script>

<svelte:head>
	<title>템플릿 관리</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full p-4">
					<!-- 페이지 헤더 -->
					<div class="mb-6">
						<div
							class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
						>
							<div>
								<h1 class="text-2xl font-bold text-base-content mb-2">템플릿 관리</h1>
								<p class="text-base-content/70">
									자주 사용하는 알림 내용을 템플릿으로 저장하여 빠르게 재사용하세요
								</p>
							</div>
							<div class="flex gap-2">
								<button class="btn btn-primary btn-sm" onclick={openCreateModal}>
									<Icon data={faPlus} />
									새 템플릿 생성
								</button>
								<button
									class="btn btn-ghost btn-sm"
									class:loading
									onclick={handleRefresh}
									disabled={loading}
								>
									<Icon data={faRefresh} />
									새로고침
								</button>
							</div>
						</div>
					</div>

					<!-- 검색 및 필터 영역 -->
					<SearchUI>
						<div class="flex flex-wrap gap-4 items-end">
							<div class="form-control">
								<label class="label" for="template-search">
									<span class="label-text text-sm">템플릿 검색</span>
								</label>
								<div class="relative">
									<input
										id="template-search"
										type="text"
										placeholder="템플릿명 또는 제목으로 검색..."
										class="input input-bordered input-sm w-full max-w-xs pl-10"
										bind:value={searchTerm}
										oninput={handleSearch}
									/>
									<Icon
										data={faSearch}
										class="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50 w-4 h-4"
									/>
								</div>
							</div>

							<div class="form-control">
								<label class="label" for="priority-filter">
									<span class="label-text text-sm">우선순위</span>
								</label>
								<select
									id="priority-filter"
									class="select select-bordered select-sm w-full max-w-xs"
									bind:value={priorityFilter}
									onchange={handleFilterChange}
								>
									{#each priorityOptions as option}
										<option value={option.value}>{option.text}</option>
									{/each}
								</select>
							</div>

							<div class="form-control">
								<div class="label">
									<span class="label-text text-sm">필터 초기화</span>
								</div>
								<button
									class="btn btn-ghost btn-sm"
									onclick={() => {
										searchTerm = '';
										priorityFilter = '';
										handleFilterChange();
									}}
									disabled={!searchTerm && !priorityFilter}
								>
									초기화
								</button>
							</div>
						</div>
					</SearchUI>

					<!-- 템플릿 목록 -->
					<div class="mt-6">
						{#if loading}
							<div class="flex justify-center items-center py-12">
								<span class="loading loading-spinner loading-lg"></span>
								<span class="ml-2">템플릿 목록을 불러오는 중...</span>
							</div>
						{:else if hasTemplates}
							<div class="overflow-x-auto">
								<table class="table table-zebra w-full">
									<thead>
										<tr>
											<th class="text-center w-16">#</th>
											<th
												class="cursor-pointer hover:bg-base-200"
												onclick={() => handleSortChange('name')}
											>
												<div class="flex items-center justify-between">
													<span>템플릿명</span>
													{#if getSortIcon('name')}
														<Icon data={getSortIcon('name')} class="w-4 h-4" />
													{/if}
												</div>
											</th>
											<th>제목</th>
											<th class="text-center w-24">우선순위</th>
											<th
												class="text-center w-32 cursor-pointer hover:bg-base-200"
												onclick={() => handleSortChange('usage_count')}
											>
												<div class="flex items-center justify-center gap-1">
													<span>사용횟수</span>
													{#if getSortIcon('usage_count')}
														<Icon data={getSortIcon('usage_count')} class="w-4 h-4" />
													{/if}
												</div>
											</th>
											<th
												class="text-center w-40 cursor-pointer hover:bg-base-200"
												onclick={() => handleSortChange('created_at')}
											>
												<div class="flex items-center justify-center gap-1">
													<span>생성일</span>
													{#if getSortIcon('created_at')}
														<Icon data={getSortIcon('created_at')} class="w-4 h-4" />
													{/if}
												</div>
											</th>
											<th class="text-center w-32">작업</th>
										</tr>
									</thead>
									<tbody>
										{#each templates as template, index}
											<tr class="hover:bg-base-200">
												<td class="text-center">
													{getNumberFormat(startNo - index)}
												</td>
												<td>
													<div class="font-medium text-base-content">
														{template.name}
													</div>
												</td>
												<td>
													<div class="font-medium text-base-content truncate max-w-[300px]">
														{template.title}
													</div>
													<div class="text-xs text-base-content/70 truncate max-w-[300px]">
														{template.content.replace(/<[^>]*>/g, '').substring(0, 50)}...
													</div>
												</td>
												<td class="text-center">
													<div class="badge {getPriorityColor(template.priority)} badge-sm">
														{getPriorityText(template.priority)}
													</div>
												</td>
												<td class="text-center">
													<div class="badge badge-info badge-sm">
														{getNumberFormat(template.usage_count)}회
													</div>
												</td>
												<td class="text-center">
													<div class="text-sm text-base-content/70">
														{new Date(template.created_at).toLocaleDateString('ko-KR')}
													</div>
												</td>
												<td class="text-center">
													<div class="flex justify-center gap-1">
														<button
															class="btn btn-ghost btn-xs"
															onclick={() => openViewModal(template)}
															title="보기"
														>
															<Icon data={faEye} />
														</button>
														<button
															class="btn btn-ghost btn-xs"
															onclick={() => openEditModal(template)}
															title="수정"
														>
															<Icon data={faEdit} />
														</button>
														<button
															class="btn btn-ghost btn-xs text-error"
															onclick={() => openDeleteModal(template)}
															title="삭제"
														>
															<Icon data={faTrash} />
														</button>
													</div>
												</td>
											</tr>
										{/each}
									</tbody>
								</table>
							</div>

							<!-- 페이지네이션 -->
							<div class="mt-6">
								<Paginate
									store={templateStore}
									localUrl="/admin/notifications/templates"
									onPageChange={handlePageChange}
									searchParams={[
										searchTerm ? `search=${encodeURIComponent(searchTerm)}` : '',
										priorityFilter ? `priority=${priorityFilter}` : '',
										`sort_by=${sortBy}`,
										`sort_order=${sortOrder}`
									]
										.filter(Boolean)
										.join('&')}
								/>
							</div>
						{:else}
							<!-- 빈 상태 -->
							<div class="text-center py-12">
								<div class="mb-4">
									<Icon data={faFileAlt} class="w-16 h-16 text-base-content/30" />
								</div>
								<h3 class="text-lg font-medium text-base-content mb-2">
									{searchTerm || priorityFilter
										? '검색 결과가 없습니다'
										: '등록된 템플릿이 없습니다'}
								</h3>
								<p class="text-base-content/70 mb-4">
									{searchTerm || priorityFilter
										? '다른 검색 조건으로 시도해보세요'
										: '새 템플릿을 생성하여 자주 사용하는 알림 내용을 저장하세요'}
								</p>
								{#if !searchTerm && !priorityFilter}
									<button class="btn btn-primary" onclick={openCreateModal}>
										<Icon data={faPlus} />
										첫 번째 템플릿 생성
									</button>
								{/if}
							</div>
						{/if}
					</div>
				</div>
			</section>
		</div>

		<!-- 템플릿 생성 모달 -->
		<TemplateForm
			show={showCreateModal}
			{user}
			onclose={closeCreateModal}
			onsaved={handleTemplateSaved}
		/>

		<!-- 템플릿 수정 모달 -->
		{#if showEditModal && selectedTemplate}
			<TemplateForm
				show={showEditModal}
				{user}
				template={selectedTemplate}
				onclose={closeEditModal}
				onsaved={handleTemplateSaved}
			/>
		{/if}

		<!-- 템플릿 보기 모달 -->
		{#if showViewModal && selectedTemplate}
			<div class="modal modal-open">
				<div class="modal-box max-w-2xl">
					<h3 class="font-bold text-lg mb-4">템플릿 상세 정보</h3>

					<div class="space-y-4">
						<div>
							<div class="label">
								<span class="label-text font-medium">템플릿명</span>
							</div>
							<div class="text-base-content">{selectedTemplate.name}</div>
						</div>

						<div>
							<div class="label">
								<span class="label-text font-medium">제목</span>
							</div>
							<div class="text-base-content">{selectedTemplate.title}</div>
						</div>

						<div>
							<div class="label">
								<span class="label-text font-medium">내용</span>
							</div>
							<div class="bg-base-200 p-3 rounded-lg text-base-content whitespace-pre-wrap">
								{selectedTemplate.content}
							</div>
						</div>

						<div class="grid grid-cols-2 gap-4">
							<div>
								<div class="label">
									<span class="label-text font-medium">우선순위</span>
								</div>
								<div class="badge {getPriorityColor(selectedTemplate.priority)}">
									{getPriorityText(selectedTemplate.priority)}
								</div>
							</div>

							<div>
								<div class="label">
									<span class="label-text font-medium">사용횟수</span>
								</div>
								<div class="badge badge-info">
									{getNumberFormat(selectedTemplate.usage_count)}회
								</div>
							</div>
						</div>

						<div>
							<div class="label">
								<span class="label-text font-medium">생성일</span>
							</div>
							<div class="text-base-content/70">
								{new Date(selectedTemplate.created_at).toLocaleString('ko-KR')}
							</div>
						</div>
					</div>

					<div class="modal-action">
						<button class="btn btn-ghost" onclick={closeViewModal}>닫기</button>
						<button
							class="btn btn-primary"
							onclick={() => {
								closeViewModal();
								if (selectedTemplate) {
									openEditModal(selectedTemplate);
								}
							}}
						>
							<Icon data={faEdit} />
							수정
						</button>
					</div>
				</div>
				<div
					class="modal-backdrop"
					onclick={closeViewModal}
					role="button"
					tabindex="-1"
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							closeViewModal();
						}
					}}
				></div>
			</div>
		{/if}

		<!-- 템플릿 삭제 확인 모달 -->
		{#if showDeleteModal && templateToDelete}
			<div class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg mb-4">템플릿 삭제 확인</h3>
					<p class="mb-4">
						<strong>"{templateToDelete.name}"</strong> 템플릿을 정말 삭제하시겠습니까?
					</p>
					<p class="text-sm text-warning mb-6">
						이 작업은 되돌릴 수 없습니다. 삭제된 템플릿은 복구할 수 없습니다.
					</p>
					<div class="modal-action">
						<button class="btn btn-ghost" onclick={closeDeleteModal}>취소</button>
						<button class="btn btn-error" onclick={handleDeleteTemplate}>
							<Icon data={faTrash} />
							삭제
						</button>
					</div>
				</div>
				<div
					class="modal-backdrop"
					onclick={closeDeleteModal}
					role="button"
					tabindex="-1"
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							closeDeleteModal();
						}
					}}
				></div>
			</div>
		{/if}
	{/snippet}
</AppLayout>
