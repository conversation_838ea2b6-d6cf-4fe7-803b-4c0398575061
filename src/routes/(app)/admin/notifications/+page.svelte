<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { getUser, isUser, type User } from '$lib/User';
	import { checkAdminAndRedirect } from '$lib/Functions';
	import AppLayout from '$lib/components/Layouts/AppLayout.svelte';
	import TitleBar from '$lib/components/UI/TitleBar.svelte';
	import type { Breadcrumb } from '$lib/types/types';

	let user: User = getUser();

	// 브레드크럼 설정
	const breadcrumbs: Breadcrumb[] = [
		{ title: '홈', url: '/' },
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' }
	];

	// 관리자 권한 확인
	onMount(async () => {
		if (isUser()) {
			const user = getUser();
			await checkAdminAndRedirect(user, '/');
		}
	});

	// 메뉴 항목들
	const menuItems = [
		{
			title: '대시보드',
			description: '알림 전송 현황과 통계를 확인합니다',
			icon: 'fas fa-chart-line',
			url: '/admin/notifications/dashboard',
			color: 'bg-blue-500'
		},
		{
			title: '알림 전송',
			description: '새로운 알림을 작성하고 전송합니다',
			icon: 'fas fa-paper-plane',
			url: '/admin/notifications/send',
			color: 'bg-green-500'
		},
		{
			title: '그룹 관리',
			description: '직원 그룹을 생성하고 관리합니다',
			icon: 'fas fa-users',
			url: '/admin/notifications/groups',
			color: 'bg-purple-500'
		},
		{
			title: '알림 히스토리',
			description: '전송된 알림의 기록을 확인합니다',
			icon: 'fas fa-history',
			url: '/admin/notifications/history',
			color: 'bg-orange-500'
		},
		{
			title: '템플릿 관리',
			description: '알림 템플릿을 생성하고 관리합니다',
			icon: 'fas fa-file-alt',
			url: '/admin/notifications/templates',
			color: 'bg-indigo-500'
		}
	];

	// 메뉴 클릭 핸들러
	function handleMenuClick(url: string) {
		goto(url);
	}
</script>

<AppLayout {user}>
	<TitleBar {breadcrumbs} />

	<div class="container mx-auto px-4 py-6">
		<!-- 페이지 헤더 -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 mb-2">
				<i class="fas fa-bell mr-3 text-blue-600"></i>
				알림 관리 시스템
			</h1>
			<p class="text-gray-600">직원들에게 실시간 알림을 전송하고 관리할 수 있습니다.</p>
		</div>

		<!-- 메뉴 그리드 -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{#each menuItems as item}
				<div
					class="card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
					onclick={() => handleMenuClick(item.url)}
					onkeydown={(e) => e.key === 'Enter' && handleMenuClick(item.url)}
					role="button"
					tabindex="0"
				>
					<div class="card-body">
						<!-- 아이콘 -->
						<div class="flex items-center mb-4">
							<div
								class="w-12 h-12 rounded-lg {item.color} flex items-center justify-center text-white mr-4"
							>
								<i class="{item.icon} text-xl"></i>
							</div>
							<h2 class="card-title text-lg font-semibold">
								{item.title}
							</h2>
						</div>

						<!-- 설명 -->
						<p class="text-gray-600 text-sm leading-relaxed">
							{item.description}
						</p>

						<!-- 이동 버튼 -->
						<div class="card-actions justify-end mt-4">
							<button class="btn btn-primary btn-sm">
								이동
								<i class="fas fa-arrow-right ml-1"></i>
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- 빠른 액세스 섹션 -->
		<div class="mt-12">
			<h2 class="text-xl font-semibold text-gray-900 mb-4">
				<i class="fas fa-bolt mr-2 text-yellow-500"></i>
				빠른 액세스
			</h2>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- 긴급 알림 전송 -->
				<div class="card bg-red-50 border border-red-200">
					<div class="card-body">
						<h3 class="card-title text-red-700">
							<i class="fas fa-exclamation-triangle mr-2"></i>
							긴급 알림 전송
						</h3>
						<p class="text-red-600 text-sm">
							긴급한 상황에서 모든 직원에게 즉시 알림을 전송합니다.
						</p>
						<div class="card-actions justify-end">
							<button
								class="btn btn-error btn-sm"
								onclick={() => goto('/admin/notifications/send?priority=urgent&target=all')}
							>
								긴급 전송
							</button>
						</div>
					</div>
				</div>

				<!-- 최근 활동 -->
				<div class="card bg-blue-50 border border-blue-200">
					<div class="card-body">
						<h3 class="card-title text-blue-700">
							<i class="fas fa-clock mr-2"></i>
							최근 활동
						</h3>
						<p class="text-blue-600 text-sm">최근 전송된 알림과 현황을 빠르게 확인합니다.</p>
						<div class="card-actions justify-end">
							<button
								class="btn btn-info btn-sm"
								onclick={() => goto('/admin/notifications/dashboard')}
							>
								현황 보기
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 도움말 섹션 -->
		<div class="mt-8">
			<div class="alert alert-info">
				<i class="fas fa-info-circle"></i>
				<div>
					<h3 class="font-bold">알림 관리 시스템 사용 안내</h3>
					<div class="text-sm mt-1">
						• <strong>대시보드</strong>: 전체 알림 현황과 통계를 확인할 수 있습니다<br />
						• <strong>알림 전송</strong>: 개별, 그룹, 전체 직원에게 알림을 전송할 수 있습니다<br />
						• <strong>그룹 관리</strong>: 부서별, 업무별로 직원 그룹을 만들어 관리할 수 있습니다<br
						/>
						• <strong>템플릿</strong>: 자주 사용하는 알림 내용을 템플릿으로 저장하여 재사용할 수
						있습니다
					</div>
				</div>
			</div>
		</div>
	</div>
</AppLayout>

<style>
	.card:hover {
		box-shadow:
			0 20px 25px -5px rgba(0, 0, 0, 0.1),
			0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}

	.card-body {
		padding: 1.5rem;
	}

	.grid {
		gap: 1.5rem;
	}

	@media (max-width: 768px) {
		.grid {
			grid-template-columns: 1fr;
		}
	}
</style>
