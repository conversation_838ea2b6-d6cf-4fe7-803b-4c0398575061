<script lang="ts">
	import { goto } from '$app/navigation';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import NotificationForm from '$components/Notification/Send/NotificationForm.svelte';

	let user: User = getUser();

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '알림 전송', url: '#' }
	];

	// 알림 전송 성공 핸들러
	function handleNotificationSent() {
		// 성공 후 히스토리 페이지로 이동
		goto('/admin/notifications/history');
	}

	// 취소 핸들러
	function handleCancel() {
		// 대시보드로 돌아가기
		goto('/admin/notifications/dashboard');
	}
</script>

<svelte:head>
	<title>알림 전송</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full p-4">
					<!-- 페이지 헤더 -->
					<div class="mb-6">
						<h1 class="text-2xl font-bold text-base-content mb-2">알림 전송</h1>
						<p class="text-base-content/70">직원들에게 실시간 알림을 전송하세요</p>
					</div>

					<!-- 알림 작성 폼 -->
					<div class="max-w-4xl mx-auto">
						<NotificationForm {user} onsent={handleNotificationSent} oncancel={handleCancel} />
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
