<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import StatCard from '$lib/components/Notification/Dashboard/StatCard.svelte';
	import ChartWidget from '$lib/components/Notification/Dashboard/ChartWidget.svelte';
	import GroupStatsTable from '$lib/components/Notification/Dashboard/GroupStatsTable.svelte';

	import { dashboardStore, loadDashboardStats, refreshDashboard } from '$lib/stores/notification/dashboardStore';

	// Font Awesome 아이콘 import
	import { faPaperPlane } from '@fortawesome/free-solid-svg-icons/faPaperPlane';
	import { faUsers } from '@fortawesome/free-solid-svg-icons/faUsers';
	import { faChartBar } from '@fortawesome/free-solid-svg-icons/faChartBar';
	import { faLayerGroup } from '@fortawesome/free-solid-svg-icons/faLayerGroup';

	let user: User = getUser();
	let selectedPeriod = $state('week');
	let selectedPriority = $state('');
	let groupSortBy = $state('name');
	let groupSortOrder = $state<'asc' | 'desc'>('asc');
	let groupSearchTerm = $state('');
	let groupReadRateFilter = $state('');

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리자', url: '/admin' },
		{ title: '알림 관리', url: '/admin/notifications' },
		{ title: '대시보드', url: '#' }
	];

	// 대시보드 데이터 로드
	onMount(async () => {
		await loadDashboardStats(user);
	});

	// 새로고침 핸들러
	async function handleRefresh() {
		await refreshDashboard(user);
	}

	// 통계 카드 클릭 핸들러들
	function handleTodaySentClick() {
		// 오늘 전송된 알림 상세 페이지로 이동
		goto('/admin/notifications/history?date=today');
	}

	function handleActiveUsersClick() {
		// 활성 직원 관리 페이지로 이동
		goto('/settings/members?status=active');
	}

	function handleReadRateClick() {
		// 읽음률 상세 통계 페이지로 이동
		goto('/admin/notifications/history?view=stats');
	}

	function handleGroupsClick() {
		// 그룹 관리 페이지로 이동
		goto('/admin/notifications/groups');
	}

	// 반응형 데이터 - Svelte 5 $derived rune 사용
	const stats = $derived($dashboardStore.stats);
	const loading = $derived($dashboardStore.loading);
	const error = $derived($dashboardStore.error);

	// 읽음률 계산 (전체 평균) - Svelte 5 $derived rune 사용
	const averageReadRate = $derived(
		stats?.priority_read_rates?.length
			? Math.round(
					stats.priority_read_rates.reduce((sum, item) => sum + item.read_rate, 0) /
						stats.priority_read_rates.length
				)
			: 0
	);

	// 그룹 수 계산 - Svelte 5 $derived rune 사용
	const totalGroups = $derived(stats?.group_stats?.length || 0);

	// 그룹별 통계 데이터 필터링 및 정렬 - Svelte 5 $derived rune 사용
	const filteredAndSortedGroupStats = $derived.by(() => {
		if (!stats?.group_stats) return [];

		// 필터링
		let filtered = stats.group_stats.filter((group) => {
			// 검색어 필터
			if (groupSearchTerm) {
				const searchLower = groupSearchTerm.toLowerCase();
				const nameMatch = group.name.toLowerCase().includes(searchLower);
				const descMatch = group.description?.toLowerCase().includes(searchLower) || false;
				if (!nameMatch && !descMatch) return false;
			}

			// 읽음률 필터
			if (groupReadRateFilter) {
				switch (groupReadRateFilter) {
					case 'high':
						if (group.read_rate < 80) return false;
						break;
					case 'medium':
						if (group.read_rate < 60 || group.read_rate >= 80) return false;
						break;
					case 'low':
						if (group.read_rate >= 60) return false;
						break;
				}
			}

			return true;
		});

		// 정렬
		return [...filtered].sort((a, b) => {
			let aValue: any = a[groupSortBy as keyof typeof a];
			let bValue: any = b[groupSortBy as keyof typeof b];

			// 문자열 비교
			if (typeof aValue === 'string' && typeof bValue === 'string') {
				aValue = aValue.toLowerCase();
				bValue = bValue.toLowerCase();
			}

			// 날짜 비교
			if (groupSortBy === 'last_activity') {
				aValue = aValue ? new Date(aValue).getTime() : 0;
				bValue = bValue ? new Date(bValue).getTime() : 0;
			}

			if (aValue < bValue) return groupSortOrder === 'asc' ? -1 : 1;
			if (aValue > bValue) return groupSortOrder === 'asc' ? 1 : -1;
			return 0;
		});
	});

	// 차트 데이터 준비 - Svelte 5 $derived rune 사용
	const trendChartData = $derived(
		stats?.weekly_trend?.map((item) => ({
			date: new Date(item.date).toLocaleDateString('ko-KR', {
				month: 'short',
				day: 'numeric'
			}),
			count: item.count
		})) || []
	);

	const priorityChartData = $derived(
		stats?.priority_read_rates?.map((item) => ({
			priority: item.priority,
			read_rate: item.read_rate
		})) || []
	);

	// 그룹별 통계 테이블 이벤트 핸들러들
	function handleGroupSort(event: { field: string; order: 'asc' | 'desc' }) {
		groupSortBy = event.field;
		groupSortOrder = event.order;
	}

	function handleGroupClick(event: { groupId: number }) {
		// 그룹 상세 페이지로 이동 (Shallow Routing 사용)
		goto(`/admin/notifications/groups?selected=${event.groupId}`);
	}

	// 그룹 필터 초기화
	function resetGroupFilters() {
		groupSearchTerm = '';
		groupReadRateFilter = '';
		groupSortBy = 'name';
		groupSortOrder = 'asc';
	}
</script>

<svelte:head>
	<title>알림 관리 대시보드</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full p-4">
					<!-- 대시보드 헤더 -->
					<div class="mb-6">
						<h1 class="text-2xl font-bold text-base-content mb-2">알림 관리 대시보드</h1>
						<p class="text-base-content/70">실시간 알림 현황과 통계를 확인하세요</p>
					</div>

					<!-- 검색 및 필터 영역 -->
					<SearchUI>
						<div class="flex flex-wrap gap-2 items-center">
							<div class="form-control">
								<label class="label" for="period-select">
									<span class="label-text text-sm">기간 선택</span>
								</label>
								<select
									id="period-select"
									class="select select-bordered select-sm w-full max-w-xs"
									bind:value={selectedPeriod}
								>
									<option value="today">오늘</option>
									<option value="week">최근 7일</option>
									<option value="month">최근 30일</option>
								</select>
							</div>

							<div class="form-control">
								<label class="label" for="priority-select">
									<span class="label-text text-sm">우선순위</span>
								</label>
								<select
									id="priority-select"
									class="select select-bordered select-sm w-full max-w-xs"
									bind:value={selectedPriority}
								>
									<option value="">전체</option>
									<option value="urgent">긴급</option>
									<option value="high">높음</option>
									<option value="normal">보통</option>
									<option value="low">낮음</option>
								</select>
							</div>

							<div class="form-control">
								<label class="label" for="group-search">
									<span class="label-text text-sm">그룹 검색</span>
								</label>
								<input
									id="group-search"
									type="text"
									placeholder="그룹명 또는 설명 검색..."
									class="input input-bordered input-sm w-full max-w-xs"
									bind:value={groupSearchTerm}
								/>
							</div>

							<div class="form-control">
								<label class="label" for="read-rate-filter">
									<span class="label-text text-sm">읽음률 필터</span>
								</label>
								<select
									id="read-rate-filter"
									class="select select-bordered select-sm w-full max-w-xs"
									bind:value={groupReadRateFilter}
								>
									<option value="">전체</option>
									<option value="high">높음 (80% 이상)</option>
									<option value="medium">보통 (60-79%)</option>
									<option value="low">낮음 (60% 미만)</option>
								</select>
							</div>

							<div class="form-control">
								<div class="label">
									<span class="label-text text-sm">필터 초기화</span>
								</div>
								<button
									class="btn btn-ghost btn-sm"
									onclick={resetGroupFilters}
									disabled={!groupSearchTerm && !groupReadRateFilter}
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										class="h-4 w-4 fill-none stroke-current"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
										/>
									</svg>
									초기화
								</button>
							</div>

							<div class="form-control">
								<div class="label">
									<span class="label-text text-sm">새로고침</span>
								</div>
								<button
									class="btn btn-primary btn-sm"
									class:loading
									onclick={handleRefresh}
									disabled={loading}
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										class="h-4 w-4 fill-none stroke-current"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
										/>
									</svg>
									새로고침
								</button>
							</div>
						</div>
					</SearchUI>

					<!-- 대시보드 콘텐츠 영역 -->
					<div class="mt-6">
						<!-- 에러 메시지 표시 -->
						{#if error}
							<div class="alert alert-error mb-6">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="stroke-current shrink-0 h-6 w-6 fill-none"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
								<span>{error}</span>
								<div>
									<button class="btn btn-sm btn-ghost" onclick={handleRefresh}> 다시 시도 </button>
								</div>
							</div>
						{/if}

						<!-- 통계 카드 영역 -->
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
							<!-- 오늘 전송된 알림 수 카드 -->
							<StatCard
								title="오늘 전송"
								value={stats?.today_sent ?? 0}
								icon={faPaperPlane}
								color="primary"
								{loading}
								subtitle="건의 알림이 전송됨"
								onClick={handleTodaySentClick}
							/>

							<!-- 활성 직원 수 카드 -->
							<StatCard
								title="활성 직원"
								value={stats?.online_users ?? 0}
								icon={faUsers}
								color="success"
								{loading}
								subtitle="명이 현재 온라인"
								onClick={handleActiveUsersClick}
							/>

							<!-- 읽음률 통계 카드 -->
							<StatCard
								title="평균 읽음률"
								value="{averageReadRate}%"
								icon={faChartBar}
								color="info"
								{loading}
								subtitle="전체 알림 읽음률"
								onClick={handleReadRateClick}
							/>

							<!-- 총 그룹 수 카드 -->
							<StatCard
								title="총 그룹"
								value={totalGroups}
								icon={faLayerGroup}
								color="warning"
								{loading}
								subtitle="개의 알림 그룹"
								onClick={handleGroupsClick}
							/>
						</div>

						<!-- 차트 및 상세 정보 영역 -->
						<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<!-- 알림 전송 추이 차트 -->
							<ChartWidget
								type="trend"
								title="최근 7일 알림 전송 추이"
								data={trendChartData}
								{loading}
								height={280}
								color="primary"
							/>

							<!-- 우선순위별 읽음률 차트 -->
							<ChartWidget
								type="priority"
								title="우선순위별 읽음률"
								data={priorityChartData}
								{loading}
								height={280}
								color="info"
							/>
						</div>

						<!-- 그룹별 통계 테이블 -->
						<div class="mt-6">
							<GroupStatsTable
								data={filteredAndSortedGroupStats}
								{loading}
								sortBy={groupSortBy}
								sortOrder={groupSortOrder}
								onsort={handleGroupSort}
								ongroupclick={handleGroupClick}
							/>
						</div>

						<!-- 빠른 액션 버튼들 -->
						<div class="flex flex-wrap gap-4 mt-6">
							<button class="btn btn-primary" onclick={() => goto('/admin/notifications/send')}>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 fill-none stroke-current"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
									/>
								</svg>
								알림 전송
							</button>

							<button class="btn btn-outline" onclick={() => goto('/admin/notifications/groups')}>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 fill-none stroke-current"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
									/>
								</svg>
								그룹 관리
							</button>

							<button class="btn btn-outline" onclick={() => goto('/admin/notifications/history')}>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 fill-none stroke-current"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
								알림 히스토리
							</button>

							<button
								class="btn btn-outline"
								onclick={() => goto('/admin/notifications/templates')}
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 fill-none stroke-current"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
									/>
								</svg>
								템플릿 관리
							</button>
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
